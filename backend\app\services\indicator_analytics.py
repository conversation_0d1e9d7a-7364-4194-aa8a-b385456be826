"""
Advanced Indicator Analytics Service
Provides correlation analysis, performance metrics, and advanced analytics for multi-indicator strategies
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import json

from app.services.multi_indicator_engine import MultiIndicatorEngine
from app.core.data_access import OHLCVDataAccess

logger = logging.getLogger(__name__)

@dataclass
class IndicatorCorrelation:
    """Correlation data between two indicators"""
    indicator1: str
    indicator2: str
    pearson_correlation: float
    spearman_correlation: float
    p_value: float
    significance_level: str
    sample_size: int

@dataclass
class IndicatorPerformance:
    """Performance metrics for an indicator"""
    indicator_name: str
    config: Dict[str, Any]
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_return: float
    volatility: float
    signal_count: int

@dataclass
class IndicatorSignal:
    """Trading signal from an indicator"""
    timestamp: datetime
    indicator_name: str
    signal_type: str  # 'buy', 'sell', 'neutral'
    strength: float  # 0-1
    price: float
    confidence: float

class IndicatorAnalyticsService:
    """
    Advanced analytics service for technical indicators
    """
    
    def __init__(self):
        self.correlation_cache = {}
        self.performance_cache = {}
        
    def calculate_indicator_correlations(self, ohlcv_data: List[Dict[str, Any]], 
                                       indicators_config: Dict[str, Dict[str, Any]],
                                       min_correlation: float = 0.1) -> List[IndicatorCorrelation]:
        """
        Calculate correlations between all pairs of indicators
        
        Args:
            ohlcv_data: Historical OHLCV data
            indicators_config: Configuration for indicators
            min_correlation: Minimum correlation threshold to report
            
        Returns:
            List of significant correlations
        """
        try:
            # Calculate all indicators
            indicator_results = MultiIndicatorEngine.calculate_all_indicators(
                ohlcv_data, indicators_config
            )
            
            if not indicator_results:
                return []
            
            correlations = []
            indicator_names = list(indicators_config.keys())
            
            # Calculate pairwise correlations
            for i, indicator1 in enumerate(indicator_names):
                for j, indicator2 in enumerate(indicator_names[i+1:], i+1):
                    correlation = self._calculate_pairwise_correlation(
                        indicator_results, indicator1, indicator2, min_correlation
                    )
                    if correlation:
                        correlations.append(correlation)
            
            # Sort by absolute correlation strength
            correlations.sort(key=lambda x: abs(x.pearson_correlation), reverse=True)
            
            return correlations
            
        except Exception as e:
            logger.error(f"Error calculating indicator correlations: {e}")
            return []
    
    def _calculate_pairwise_correlation(self, indicator_results: Dict[str, Any],
                                      indicator1: str, indicator2: str,
                                      min_correlation: float) -> Optional[IndicatorCorrelation]:
        """Calculate correlation between two specific indicators"""
        try:
            # Get indicator data
            data1 = self._extract_indicator_values(indicator_results, indicator1)
            data2 = self._extract_indicator_values(indicator_results, indicator2)
            
            if not data1 or not data2 or len(data1) != len(data2):
                return None
            
            # Remove NaN values
            df = pd.DataFrame({'ind1': data1, 'ind2': data2}).dropna()
            if len(df) < 10:  # Need minimum samples
                return None
            
            # Calculate correlations
            pearson_corr, pearson_p = pearsonr(df['ind1'], df['ind2'])
            spearman_corr, spearman_p = spearmanr(df['ind1'], df['ind2'])
            
            # Check significance
            if abs(pearson_corr) < min_correlation:
                return None
            
            significance = self._determine_significance(pearson_p)
            
            return IndicatorCorrelation(
                indicator1=indicator1,
                indicator2=indicator2,
                pearson_correlation=pearson_corr,
                spearman_correlation=spearman_corr,
                p_value=pearson_p,
                significance_level=significance,
                sample_size=len(df)
            )
            
        except Exception as e:
            logger.error(f"Error calculating correlation between {indicator1} and {indicator2}: {e}")
            return None
    
    def _extract_indicator_values(self, indicator_results: Dict[str, Any], 
                                indicator_name: str) -> List[float]:
        """Extract numeric values from indicator results"""
        indicator_data = indicator_results.get(indicator_name.lower())
        if not indicator_data:
            return []
        
        # Handle different indicator data structures
        if isinstance(indicator_data, dict):
            # For complex indicators like MACD, use the main line
            if 'macd' in indicator_data:
                return indicator_data['macd']
            elif 'rsi' in indicator_data:
                return indicator_data['rsi']
            elif 'upper' in indicator_data:  # Bollinger Bands
                return indicator_data['middle']  # Use middle line
            else:
                # Use first numeric array found
                for key, values in indicator_data.items():
                    if isinstance(values, list) and values:
                        return values
        elif isinstance(indicator_data, list):
            return indicator_data
        
        return []
    
    def _determine_significance(self, p_value: float) -> str:
        """Determine statistical significance level"""
        if p_value < 0.001:
            return "highly_significant"
        elif p_value < 0.01:
            return "very_significant"
        elif p_value < 0.05:
            return "significant"
        elif p_value < 0.1:
            return "marginally_significant"
        else:
            return "not_significant"
    
    def analyze_indicator_performance(self, symbol: str, timeframe: str,
                                    indicator_name: str, config: Dict[str, Any],
                                    start_time: datetime, end_time: datetime,
                                    benchmark_returns: Optional[List[float]] = None) -> IndicatorPerformance:
        """
        Analyze performance of a specific indicator
        
        Args:
            symbol: Trading symbol
            timeframe: Chart timeframe
            indicator_name: Name of indicator to analyze
            config: Indicator configuration
            start_time: Analysis start time
            end_time: Analysis end time
            benchmark_returns: Optional benchmark returns for comparison
            
        Returns:
            Performance metrics
        """
        try:
            # Get historical data
            ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_time,
                end_time=end_time
            )
            
            if not ohlcv_data:
                raise ValueError("No OHLCV data available")
            
            # Calculate indicator
            indicators_config = {indicator_name: config}
            indicator_results = MultiIndicatorEngine.calculate_all_indicators(
                ohlcv_data, indicators_config
            )
            
            # Generate trading signals
            signals = self._generate_trading_signals(
                ohlcv_data, indicator_results, indicator_name
            )
            
            # Calculate performance metrics
            returns = self._calculate_signal_returns(ohlcv_data, signals)
            
            performance = IndicatorPerformance(
                indicator_name=indicator_name,
                config=config,
                accuracy=self._calculate_accuracy(signals, ohlcv_data),
                precision=self._calculate_precision(signals, returns),
                recall=self._calculate_recall(signals, returns),
                f1_score=0.0,  # Will be calculated from precision and recall
                sharpe_ratio=self._calculate_sharpe_ratio(returns),
                max_drawdown=self._calculate_max_drawdown(returns),
                win_rate=self._calculate_win_rate(returns),
                avg_return=np.mean(returns) if returns else 0.0,
                volatility=np.std(returns) if returns else 0.0,
                signal_count=len(signals)
            )
            
            # Calculate F1 score
            if performance.precision + performance.recall > 0:
                performance.f1_score = 2 * (performance.precision * performance.recall) / (
                    performance.precision + performance.recall
                )
            
            return performance
            
        except Exception as e:
            logger.error(f"Error analyzing indicator performance: {e}")
            return IndicatorPerformance(
                indicator_name=indicator_name,
                config=config,
                accuracy=0.0, precision=0.0, recall=0.0, f1_score=0.0,
                sharpe_ratio=0.0, max_drawdown=0.0, win_rate=0.0,
                avg_return=0.0, volatility=0.0, signal_count=0
            )
    
    def _generate_trading_signals(self, ohlcv_data: List[Dict[str, Any]],
                                indicator_results: Dict[str, Any],
                                indicator_name: str) -> List[IndicatorSignal]:
        """Generate trading signals from indicator data"""
        signals = []
        
        try:
            indicator_data = indicator_results.get(indicator_name.lower())
            if not indicator_data:
                return signals
            
            timestamps = indicator_results.get('timestamps', [])
            prices = [candle['close'] for candle in ohlcv_data]
            
            # Generate signals based on indicator type
            if indicator_name.upper() == 'RSI':
                signals = self._generate_rsi_signals(
                    indicator_data, timestamps, prices, indicator_name
                )
            elif indicator_name.upper() == 'MACD':
                signals = self._generate_macd_signals(
                    indicator_data, timestamps, prices, indicator_name
                )
            elif indicator_name.upper() in ['EMA', 'SMA']:
                signals = self._generate_ma_signals(
                    indicator_data, timestamps, prices, indicator_name, ohlcv_data
                )
            
        except Exception as e:
            logger.error(f"Error generating signals for {indicator_name}: {e}")
        
        return signals
    
    def _generate_rsi_signals(self, rsi_data: List[float], timestamps: List[str],
                            prices: List[float], indicator_name: str) -> List[IndicatorSignal]:
        """Generate RSI-based trading signals"""
        signals = []
        
        for i, (rsi, timestamp, price) in enumerate(zip(rsi_data, timestamps, prices)):
            if pd.isna(rsi):
                continue
                
            signal_type = 'neutral'
            strength = 0.0
            confidence = 0.0
            
            if rsi < 30:  # Oversold
                signal_type = 'buy'
                strength = (30 - rsi) / 30
                confidence = min(1.0, strength * 2)
            elif rsi > 70:  # Overbought
                signal_type = 'sell'
                strength = (rsi - 70) / 30
                confidence = min(1.0, strength * 2)
            
            if signal_type != 'neutral':
                signals.append(IndicatorSignal(
                    timestamp=pd.to_datetime(timestamp),
                    indicator_name=indicator_name,
                    signal_type=signal_type,
                    strength=strength,
                    price=price,
                    confidence=confidence
                ))
        
        return signals
    
    def _generate_macd_signals(self, macd_data: Dict[str, List[float]], 
                             timestamps: List[str], prices: List[float],
                             indicator_name: str) -> List[IndicatorSignal]:
        """Generate MACD-based trading signals"""
        signals = []
        
        macd_line = macd_data.get('macd', [])
        signal_line = macd_data.get('signal', [])
        
        if not macd_line or not signal_line:
            return signals
        
        for i in range(1, len(macd_line)):
            if pd.isna(macd_line[i]) or pd.isna(signal_line[i]):
                continue
            
            prev_macd = macd_line[i-1]
            curr_macd = macd_line[i]
            prev_signal = signal_line[i-1]
            curr_signal = signal_line[i]
            
            # MACD crossover signals
            if prev_macd <= prev_signal and curr_macd > curr_signal:
                # Bullish crossover
                strength = abs(curr_macd - curr_signal) / max(abs(curr_macd), abs(curr_signal), 0.001)
                signals.append(IndicatorSignal(
                    timestamp=pd.to_datetime(timestamps[i]),
                    indicator_name=indicator_name,
                    signal_type='buy',
                    strength=min(1.0, strength),
                    price=prices[i],
                    confidence=0.7
                ))
            elif prev_macd >= prev_signal and curr_macd < curr_signal:
                # Bearish crossover
                strength = abs(curr_macd - curr_signal) / max(abs(curr_macd), abs(curr_signal), 0.001)
                signals.append(IndicatorSignal(
                    timestamp=pd.to_datetime(timestamps[i]),
                    indicator_name=indicator_name,
                    signal_type='sell',
                    strength=min(1.0, strength),
                    price=prices[i],
                    confidence=0.7
                ))
        
        return signals
    
    def _generate_ma_signals(self, ma_data: List[float], timestamps: List[str],
                           prices: List[float], indicator_name: str,
                           ohlcv_data: List[Dict[str, Any]]) -> List[IndicatorSignal]:
        """Generate moving average-based trading signals"""
        signals = []
        
        for i in range(1, len(ma_data)):
            if pd.isna(ma_data[i]) or pd.isna(prices[i]):
                continue
            
            # Price crossing MA signals
            prev_price = prices[i-1]
            curr_price = prices[i]
            prev_ma = ma_data[i-1]
            curr_ma = ma_data[i]
            
            if prev_price <= prev_ma and curr_price > curr_ma:
                # Bullish crossover
                strength = (curr_price - curr_ma) / curr_ma
                signals.append(IndicatorSignal(
                    timestamp=pd.to_datetime(timestamps[i]),
                    indicator_name=indicator_name,
                    signal_type='buy',
                    strength=min(1.0, abs(strength) * 10),
                    price=curr_price,
                    confidence=0.6
                ))
            elif prev_price >= prev_ma and curr_price < curr_ma:
                # Bearish crossover
                strength = (curr_ma - curr_price) / curr_ma
                signals.append(IndicatorSignal(
                    timestamp=pd.to_datetime(timestamps[i]),
                    indicator_name=indicator_name,
                    signal_type='sell',
                    strength=min(1.0, abs(strength) * 10),
                    price=curr_price,
                    confidence=0.6
                ))
        
        return signals
    
    def _calculate_signal_returns(self, ohlcv_data: List[Dict[str, Any]],
                                signals: List[IndicatorSignal]) -> List[float]:
        """Calculate returns from trading signals"""
        returns = []
        position = None
        entry_price = None
        
        # Create price lookup
        price_lookup = {
            pd.to_datetime(candle['timestamp']): candle['close']
            for candle in ohlcv_data
        }
        
        for signal in signals:
            current_price = price_lookup.get(signal.timestamp)
            if not current_price:
                continue
            
            if signal.signal_type == 'buy' and position != 'long':
                if position == 'short' and entry_price:
                    # Close short position
                    returns.append((entry_price - current_price) / entry_price)
                
                # Open long position
                position = 'long'
                entry_price = current_price
                
            elif signal.signal_type == 'sell' and position != 'short':
                if position == 'long' and entry_price:
                    # Close long position
                    returns.append((current_price - entry_price) / entry_price)
                
                # Open short position
                position = 'short'
                entry_price = current_price
        
        return returns
    
    def _calculate_accuracy(self, signals: List[IndicatorSignal], 
                          ohlcv_data: List[Dict[str, Any]]) -> float:
        """Calculate signal accuracy"""
        if not signals:
            return 0.0
        
        correct_signals = 0
        total_signals = len(signals)
        
        # Simple accuracy: check if price moved in predicted direction
        for signal in signals:
            # Find next price after signal
            signal_time = signal.timestamp
            future_prices = [
                candle['close'] for candle in ohlcv_data
                if pd.to_datetime(candle['timestamp']) > signal_time
            ]
            
            if future_prices:
                future_price = future_prices[0]
                if signal.signal_type == 'buy' and future_price > signal.price:
                    correct_signals += 1
                elif signal.signal_type == 'sell' and future_price < signal.price:
                    correct_signals += 1
        
        return correct_signals / total_signals if total_signals > 0 else 0.0
    
    def _calculate_precision(self, signals: List[IndicatorSignal], 
                           returns: List[float]) -> float:
        """Calculate precision of positive signals"""
        if not returns:
            return 0.0
        
        positive_returns = [r for r in returns if r > 0]
        return len(positive_returns) / len(returns) if returns else 0.0
    
    def _calculate_recall(self, signals: List[IndicatorSignal], 
                        returns: List[float]) -> float:
        """Calculate recall (sensitivity) of signals"""
        # Simplified recall calculation
        return self._calculate_precision(signals, returns)
    
    def _calculate_sharpe_ratio(self, returns: List[float], 
                              risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        if not returns or len(returns) < 2:
            return 0.0
        
        excess_returns = [r - risk_free_rate/252 for r in returns]  # Daily risk-free rate
        mean_excess = np.mean(excess_returns)
        std_excess = np.std(excess_returns)
        
        return mean_excess / std_excess if std_excess > 0 else 0.0
    
    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown"""
        if not returns:
            return 0.0
        
        cumulative = np.cumprod([1 + r for r in returns])
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        
        return abs(np.min(drawdown)) if len(drawdown) > 0 else 0.0
    
    def _calculate_win_rate(self, returns: List[float]) -> float:
        """Calculate win rate (percentage of positive returns)"""
        if not returns:
            return 0.0
        
        winning_trades = sum(1 for r in returns if r > 0)
        return winning_trades / len(returns)
    
    def generate_analytics_report(self, symbol: str, timeframe: str,
                                indicators_config: Dict[str, Dict[str, Any]],
                                start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """
        Generate comprehensive analytics report for multiple indicators
        
        Returns:
            Complete analytics report with correlations, performance, and recommendations
        """
        try:
            # Get OHLCV data
            ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_time,
                end_time=end_time
            )
            
            if not ohlcv_data:
                return {'error': 'No OHLCV data available'}
            
            # Calculate correlations
            correlations = self.calculate_indicator_correlations(
                ohlcv_data, indicators_config
            )
            
            # Calculate individual performance
            performances = []
            for indicator_name, config in indicators_config.items():
                performance = self.analyze_indicator_performance(
                    symbol, timeframe, indicator_name, config, start_time, end_time
                )
                performances.append(performance)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(correlations, performances)
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'analysis_period': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat(),
                    'days': (end_time - start_time).days
                },
                'data_points': len(ohlcv_data),
                'correlations': [
                    {
                        'indicator1': c.indicator1,
                        'indicator2': c.indicator2,
                        'pearson_correlation': round(c.pearson_correlation, 4),
                        'spearman_correlation': round(c.spearman_correlation, 4),
                        'significance': c.significance_level,
                        'sample_size': c.sample_size
                    }
                    for c in correlations
                ],
                'performances': [
                    {
                        'indicator_name': p.indicator_name,
                        'config': p.config,
                        'metrics': {
                            'accuracy': round(p.accuracy, 4),
                            'precision': round(p.precision, 4),
                            'recall': round(p.recall, 4),
                            'f1_score': round(p.f1_score, 4),
                            'sharpe_ratio': round(p.sharpe_ratio, 4),
                            'max_drawdown': round(p.max_drawdown, 4),
                            'win_rate': round(p.win_rate, 4),
                            'avg_return': round(p.avg_return, 6),
                            'volatility': round(p.volatility, 6),
                            'signal_count': p.signal_count
                        }
                    }
                    for p in performances
                ],
                'recommendations': recommendations,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating analytics report: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self, correlations: List[IndicatorCorrelation],
                                performances: List[IndicatorPerformance]) -> List[Dict[str, Any]]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        # High correlation warning
        high_correlations = [c for c in correlations if abs(c.pearson_correlation) > 0.8]
        if high_correlations:
            recommendations.append({
                'type': 'warning',
                'category': 'correlation',
                'message': f'Found {len(high_correlations)} highly correlated indicator pairs. Consider removing redundant indicators.',
                'details': [
                    f"{c.indicator1} and {c.indicator2} (r={c.pearson_correlation:.3f})"
                    for c in high_correlations
                ]
            })
        
        # Performance recommendations
        best_performer = max(performances, key=lambda p: p.sharpe_ratio) if performances else None
        if best_performer and best_performer.sharpe_ratio > 1.0:
            recommendations.append({
                'type': 'positive',
                'category': 'performance',
                'message': f'{best_performer.indicator_name} shows excellent performance with Sharpe ratio of {best_performer.sharpe_ratio:.2f}',
                'details': {
                    'indicator': best_performer.indicator_name,
                    'sharpe_ratio': best_performer.sharpe_ratio,
                    'win_rate': best_performer.win_rate
                }
            })
        
        # Poor performers
        poor_performers = [p for p in performances if p.sharpe_ratio < 0]
        if poor_performers:
            recommendations.append({
                'type': 'warning',
                'category': 'performance',
                'message': f'{len(poor_performers)} indicators show negative Sharpe ratios. Consider parameter optimization.',
                'details': [p.indicator_name for p in poor_performers]
            })
        
        return recommendations

# Global analytics service instance
analytics_service = IndicatorAnalyticsService()
