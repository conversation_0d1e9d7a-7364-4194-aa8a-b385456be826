-- Enhanced Multi-Indicator System Database Schema Migration
-- Version: 002
-- Description: Add support for configurable multi-indicator system

-- 1. Create indicator_defaults table for managing default configurations
CREATE TABLE IF NOT EXISTS indicator_defaults (
    id INT AUTO_INCREMENT PRIMARY KEY,
    indicator_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_config JSON NOT NULL,
    chart_type ENUM('overlay', 'subchart') NOT NULL DEFAULT 'overlay',
    supports_multiple BOOLEAN DEFAULT FALSE,
    parameter_schema JSON,  -- JSO<PERSON> schema for parameter validation
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_chart_type (chart_type),
    INDEX idx_supports_multiple (supports_multiple)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create strategy_indicator_configs table for strategy-specific configurations
CREATE TABLE IF NOT EXISTS strategy_indicator_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    strategy_id INT NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    config JSON NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_strategy_indicator (strategy_id, indicator_name),
    INDEX idx_strategy_enabled (strategy_id, is_enabled),
    INDEX idx_display_order (display_order),
    FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Update indicators_data table to support enhanced multi-indicator system
-- First, check if the table needs to be updated
ALTER TABLE indicators_data 
ADD COLUMN IF NOT EXISTS indicator_config JSON AFTER indicator_name,
ADD COLUMN IF NOT EXISTS indicator_values JSON AFTER indicator_config,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at;

-- Update the unique constraint to include strategy_id
ALTER TABLE indicators_data 
DROP INDEX IF EXISTS unique_indicator,
ADD UNIQUE KEY unique_indicator_enhanced (strategy_id, symbol, timeframe, timestamp, indicator_name);

-- Add additional indexes for performance
ALTER TABLE indicators_data
ADD INDEX IF NOT EXISTS idx_strategy_symbol_timeframe (strategy_id, symbol, timeframe),
ADD INDEX IF NOT EXISTS idx_indicator_name_timestamp (indicator_name, timestamp);

-- 4. Insert default indicator configurations
INSERT INTO indicator_defaults (indicator_name, display_name, description, default_config, chart_type, supports_multiple, parameter_schema) VALUES
('EMA', 'Exponential Moving Average', 'Exponential Moving Average with customizable periods', 
 '{"periods": [20, 50, 100], "colors": ["#FF6B6B", "#4ECDC4", "#45B7D1"], "lineWidth": 2}', 
 'overlay', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 500}}, "colors": {"type": "array", "items": {"type": "string"}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('SMA', 'Simple Moving Average', 'Simple Moving Average with customizable periods',
 '{"periods": [20, 50, 100], "colors": ["#9C27B0", "#FF9800", "#795548"], "lineWidth": 2}',
 'overlay', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 500}}, "colors": {"type": "array", "items": {"type": "string"}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('RSI', 'Relative Strength Index', 'RSI oscillator with multiple timeframes',
 '{"periods": [14], "colors": ["#2196F3"], "overbought": 70, "oversold": 30, "lineWidth": 2}',
 'subchart', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 2, "maximum": 100}}, "colors": {"type": "array", "items": {"type": "string"}}, "overbought": {"type": "number", "minimum": 50, "maximum": 100}, "oversold": {"type": "number", "minimum": 0, "maximum": 50}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('MACD', 'MACD Oscillator', 'Moving Average Convergence Divergence',
 '{"fast": 12, "slow": 26, "signal": 9, "colors": {"macd": "#2196F3", "signal": "#FF9800", "histogram": "#4CAF50"}, "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"fast": {"type": "integer", "minimum": 1, "maximum": 50}, "slow": {"type": "integer", "minimum": 1, "maximum": 100}, "signal": {"type": "integer", "minimum": 1, "maximum": 50}, "colors": {"type": "object", "properties": {"macd": {"type": "string"}, "signal": {"type": "string"}, "histogram": {"type": "string"}}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('BOLLINGER_BANDS', 'Bollinger Bands', 'Bollinger Bands with customizable parameters',
 '{"period": 20, "stdDev": 2, "colors": {"upper": "#FF5722", "middle": "#607D8B", "lower": "#FF5722"}, "fillOpacity": 0.1, "lineWidth": 1}',
 'overlay', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 5, "maximum": 100}, "stdDev": {"type": "number", "minimum": 0.5, "maximum": 5}, "colors": {"type": "object", "properties": {"upper": {"type": "string"}, "middle": {"type": "string"}, "lower": {"type": "string"}}}, "fillOpacity": {"type": "number", "minimum": 0, "maximum": 1}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('STOCHASTIC', 'Stochastic Oscillator', 'Stochastic %K and %D oscillator',
 '{"kPeriod": 14, "dPeriod": 3, "colors": {"k": "#E91E63", "d": "#9C27B0"}, "overbought": 80, "oversold": 20, "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"kPeriod": {"type": "integer", "minimum": 1, "maximum": 50}, "dPeriod": {"type": "integer", "minimum": 1, "maximum": 20}, "colors": {"type": "object", "properties": {"k": {"type": "string"}, "d": {"type": "string"}}}, "overbought": {"type": "number", "minimum": 50, "maximum": 100}, "oversold": {"type": "number", "minimum": 0, "maximum": 50}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('VOLUME_SMA', 'Volume SMA', 'Simple Moving Average of Volume',
 '{"period": 20, "color": "#FFC107", "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 1, "maximum": 200}, "color": {"type": "string"}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('ATR', 'Average True Range', 'Average True Range volatility indicator',
 '{"period": 14, "color": "#795548", "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 1, "maximum": 100}, "color": {"type": "string"}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}')

ON DUPLICATE KEY UPDATE
    display_name = VALUES(display_name),
    description = VALUES(description),
    default_config = VALUES(default_config),
    chart_type = VALUES(chart_type),
    supports_multiple = VALUES(supports_multiple),
    parameter_schema = VALUES(parameter_schema),
    updated_at = CURRENT_TIMESTAMP;

-- 5. Create indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_indicators_data_strategy_time ON indicators_data(strategy_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_indicators_data_symbol_timeframe_time ON indicators_data(symbol, timeframe, timestamp DESC);

-- 6. Create view for easy indicator data retrieval with configurations
CREATE OR REPLACE VIEW v_indicator_data_with_config AS
SELECT 
    id.id,
    id.strategy_id,
    id.symbol,
    id.timeframe,
    id.timestamp,
    id.indicator_name,
    id.indicator_config,
    id.indicator_values,
    id.created_at,
    id.updated_at,
    idef.display_name,
    idef.chart_type,
    idef.supports_multiple,
    sic.is_enabled,
    sic.display_order
FROM indicators_data id
LEFT JOIN indicator_defaults idef ON id.indicator_name = idef.indicator_name
LEFT JOIN strategy_indicator_configs sic ON id.strategy_id = sic.strategy_id AND id.indicator_name = sic.indicator_name;

-- 7. Add comments for documentation
ALTER TABLE indicator_defaults COMMENT = 'Default configurations and metadata for technical indicators';
ALTER TABLE strategy_indicator_configs COMMENT = 'Strategy-specific indicator configurations and settings';
ALTER TABLE indicators_data COMMENT = 'Calculated indicator values with configurations and metadata';

-- Migration completed successfully
SELECT 'Enhanced Multi-Indicator System Schema Migration Completed' as status;
