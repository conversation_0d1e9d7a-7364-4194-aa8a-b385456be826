"""
Enhanced Indicator Presets API
Handles saving, loading, and managing indicator configuration presets
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Body, Query
from pydantic import BaseModel, Field, validator

from app.core.database import get_db_cursor, DatabaseError
from app.services.indicator_config_manager import IndicatorConfigManager
from app.services.multi_indicator_engine import MultiIndicatorEngine

logger = logging.getLogger(__name__)
router = APIRouter()

# Pydantic models for request/response
class IndicatorPreset(BaseModel):
    """Indicator preset model"""
    name: str = Field(..., description="Unique preset name")
    display_name: str = Field(..., description="Human-readable display name")
    description: Optional[str] = Field(None, description="Preset description")
    indicators: Dict[str, Any] = Field(..., description="Indicator configurations")
    is_public: bool = Field(False, description="Whether preset is publicly available")
    tags: List[str] = Field(default_factory=list, description="Preset tags for categorization")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Preset name cannot be empty')
        # Only allow alphanumeric, underscore, and hyphen
        if not all(c.isalnum() or c in '_-' for c in v):
            raise ValueError('Preset name can only contain letters, numbers, underscore, and hyphen')
        return v.strip().lower()

class PresetResponse(BaseModel):
    """Response model for preset operations"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class PresetListResponse(BaseModel):
    """Response model for listing presets"""
    success: bool
    data: List[Dict[str, Any]]
    total: int
    message: str

@router.get("/presets", response_model=PresetListResponse)
async def get_indicator_presets(
    public_only: bool = Query(False, description="Return only public presets"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    search: Optional[str] = Query(None, description="Search in name and description")
) -> PresetListResponse:
    """
    Get list of available indicator presets
    """
    try:
        with get_db_cursor() as cursor:
            sql = """
            SELECT name, display_name, description, indicators, is_public, tags, 
                   created_at, updated_at, usage_count
            FROM indicator_presets
            WHERE 1=1
            """
            params = []
            
            if public_only:
                sql += " AND is_public = %s"
                params.append(True)
            
            if tags:
                tag_list = [tag.strip() for tag in tags.split(',')]
                tag_conditions = []
                for tag in tag_list:
                    tag_conditions.append("JSON_CONTAINS(tags, %s)")
                    params.append(f'"{tag}"')
                sql += f" AND ({' OR '.join(tag_conditions)})"
            
            if search:
                sql += " AND (name LIKE %s OR display_name LIKE %s OR description LIKE %s)"
                search_term = f"%{search}%"
                params.extend([search_term, search_term, search_term])
            
            sql += " ORDER BY usage_count DESC, updated_at DESC"
            
            cursor.execute(sql, params)
            presets = cursor.fetchall()
            
            # Convert to response format
            preset_list = []
            for preset in presets:
                preset_data = {
                    'name': preset[0],
                    'display_name': preset[1],
                    'description': preset[2],
                    'indicators': preset[3] if isinstance(preset[3], dict) else {},
                    'is_public': preset[4],
                    'tags': preset[5] if isinstance(preset[5], list) else [],
                    'created_at': preset[6].isoformat() if preset[6] else None,
                    'updated_at': preset[7].isoformat() if preset[7] else None,
                    'usage_count': preset[8] or 0
                }
                preset_list.append(preset_data)
            
            return PresetListResponse(
                success=True,
                data=preset_list,
                total=len(preset_list),
                message=f"Found {len(preset_list)} presets"
            )
            
    except Exception as e:
        logger.error(f"Error getting indicator presets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/presets", response_model=PresetResponse)
async def save_indicator_preset(preset: IndicatorPreset) -> PresetResponse:
    """
    Save a new indicator preset or update existing one
    """
    try:
        # Validate indicator configurations
        for indicator_name, config in preset.indicators.items():
            if not MultiIndicatorEngine.validate_config(indicator_name, config.get('config', {})):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Invalid configuration for indicator {indicator_name}"
                )
        
        with get_db_cursor() as cursor:
            # Check if preset already exists
            cursor.execute("SELECT name FROM indicator_presets WHERE name = %s", (preset.name,))
            exists = cursor.fetchone()
            
            if exists:
                # Update existing preset
                sql = """
                UPDATE indicator_presets 
                SET display_name = %s, description = %s, indicators = %s, 
                    is_public = %s, tags = %s, updated_at = CURRENT_TIMESTAMP
                WHERE name = %s
                """
                cursor.execute(sql, (
                    preset.display_name,
                    preset.description,
                    preset.indicators,
                    preset.is_public,
                    preset.tags,
                    preset.name
                ))
                message = f"Preset '{preset.display_name}' updated successfully"
            else:
                # Create new preset
                sql = """
                INSERT INTO indicator_presets 
                (name, display_name, description, indicators, is_public, tags, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """
                cursor.execute(sql, (
                    preset.name,
                    preset.display_name,
                    preset.description,
                    preset.indicators,
                    preset.is_public,
                    preset.tags
                ))
                message = f"Preset '{preset.display_name}' created successfully"
            
            return PresetResponse(
                success=True,
                message=message,
                data={'name': preset.name, 'display_name': preset.display_name}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving indicator preset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/presets/{preset_name}", response_model=PresetResponse)
async def get_indicator_preset(preset_name: str) -> PresetResponse:
    """
    Get a specific indicator preset by name
    """
    try:
        with get_db_cursor() as cursor:
            sql = """
            SELECT name, display_name, description, indicators, is_public, tags, 
                   created_at, updated_at, usage_count
            FROM indicator_presets 
            WHERE name = %s
            """
            cursor.execute(sql, (preset_name,))
            preset = cursor.fetchone()
            
            if not preset:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Preset '{preset_name}' not found"
                )
            
            # Increment usage count
            cursor.execute(
                "UPDATE indicator_presets SET usage_count = usage_count + 1 WHERE name = %s",
                (preset_name,)
            )
            
            preset_data = {
                'name': preset[0],
                'display_name': preset[1],
                'description': preset[2],
                'indicators': preset[3] if isinstance(preset[3], dict) else {},
                'is_public': preset[4],
                'tags': preset[5] if isinstance(preset[5], list) else [],
                'created_at': preset[6].isoformat() if preset[6] else None,
                'updated_at': preset[7].isoformat() if preset[7] else None,
                'usage_count': (preset[8] or 0) + 1
            }
            
            return PresetResponse(
                success=True,
                message=f"Preset '{preset[1]}' retrieved successfully",
                data=preset_data
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator preset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/presets/{preset_name}", response_model=PresetResponse)
async def delete_indicator_preset(preset_name: str) -> PresetResponse:
    """
    Delete an indicator preset
    """
    try:
        with get_db_cursor() as cursor:
            # Check if preset exists
            cursor.execute("SELECT display_name FROM indicator_presets WHERE name = %s", (preset_name,))
            preset = cursor.fetchone()
            
            if not preset:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Preset '{preset_name}' not found"
                )
            
            # Delete the preset
            cursor.execute("DELETE FROM indicator_presets WHERE name = %s", (preset_name,))
            
            return PresetResponse(
                success=True,
                message=f"Preset '{preset[0]}' deleted successfully"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting indicator preset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/presets/{preset_name}/apply", response_model=PresetResponse)
async def apply_preset_to_strategy(
    preset_name: str,
    strategy_id: int = Body(..., description="Strategy ID to apply preset to")
) -> PresetResponse:
    """
    Apply a preset to a specific strategy
    """
    try:
        # Get the preset
        with get_db_cursor() as cursor:
            cursor.execute(
                "SELECT display_name, indicators FROM indicator_presets WHERE name = %s",
                (preset_name,)
            )
            preset = cursor.fetchone()
            
            if not preset:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Preset '{preset_name}' not found"
                )
            
            display_name, indicators = preset
            
            # Apply the preset to the strategy
            success = IndicatorConfigManager.bulk_save_strategy_indicators(
                strategy_id, indicators
            )
            
            if success:
                # Increment usage count
                cursor.execute(
                    "UPDATE indicator_presets SET usage_count = usage_count + 1 WHERE name = %s",
                    (preset_name,)
                )
                
                return PresetResponse(
                    success=True,
                    message=f"Preset '{display_name}' applied to strategy {strategy_id} successfully",
                    data={'strategy_id': strategy_id, 'preset_name': preset_name}
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to apply preset to strategy"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error applying preset to strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/presets/tags/popular")
async def get_popular_preset_tags(limit: int = Query(20, ge=1, le=100)) -> Dict[str, Any]:
    """
    Get most popular preset tags
    """
    try:
        with get_db_cursor() as cursor:
            # This is a simplified version - in a real implementation you'd want
            # to properly extract and count tags from the JSON field
            sql = """
            SELECT tags, COUNT(*) as count
            FROM indicator_presets 
            WHERE tags IS NOT NULL AND JSON_LENGTH(tags) > 0
            GROUP BY tags
            ORDER BY count DESC
            LIMIT %s
            """
            cursor.execute(sql, (limit,))
            results = cursor.fetchall()
            
            # Process tags (this is simplified - you'd want better tag extraction)
            tag_counts = {}
            for row in results:
                tags = row[0] if isinstance(row[0], list) else []
                for tag in tags:
                    tag_counts[tag] = tag_counts.get(tag, 0) + row[1]
            
            # Sort by count
            popular_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:limit]
            
            return {
                'success': True,
                'data': [{'tag': tag, 'count': count} for tag, count in popular_tags],
                'message': f'Found {len(popular_tags)} popular tags'
            }
            
    except Exception as e:
        logger.error(f"Error getting popular preset tags: {e}")
        raise HTTPException(status_code=500, detail=str(e))
