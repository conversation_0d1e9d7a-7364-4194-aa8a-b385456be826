<!-- Enhanced Multi-Indicator Configuration Panel -->
<div class="indicator-config-panel enhanced-config-panel">
    <!-- Enhanced Header with Advanced Controls -->
    <div class="enhanced-config-header">
        <div class="config-title-section">
            <h3 class="indicator-config-title">Multi-Indicator Configuration</h3>
            <div class="config-mode-controls">
                <label class="mode-toggle" data-tooltip="Enable bulk operations for multiple indicators">
                    <input type="checkbox" id="bulk-operation-toggle">
                    <span>Bulk Mode</span>
                </label>
                <label class="mode-toggle" data-tooltip="See changes in real-time as you adjust settings">
                    <input type="checkbox" id="preview-mode-toggle">
                    <span>Live Preview</span>
                </label>
            </div>
        </div>
        
        <div class="config-controls-section">
            <!-- Preset Controls -->
            <div class="preset-controls">
                <select id="preset-selector" data-tooltip="Load a saved indicator configuration preset">
                    <option value="">Select preset...</option>
                </select>
                <button class="btn-preset save" id="save-preset" data-tooltip="Save current configuration as a preset">
                    Save Preset
                </button>
            </div>
            
            <!-- History Controls -->
            <div class="history-controls">
                <button class="btn-history" id="undo-config" data-tooltip="Undo last change (Ctrl+Z)" disabled>
                    ↶
                </button>
                <button class="btn-history" id="redo-config" data-tooltip="Redo last undone change (Ctrl+Shift+Z)" disabled>
                    ↷
                </button>
            </div>
            
            <!-- Import/Export Controls -->
            <div class="import-export-controls">
                <button class="btn-import-export" id="export-config" data-tooltip="Export configuration to file">
                    Export
                </button>
                <button class="btn-import-export" id="import-config" data-tooltip="Import configuration from file">
                    Import
                </button>
                <input type="file" id="config-file-input" class="file-input-hidden" accept=".json">
            </div>
            
            <!-- Main Action Buttons -->
            <div class="indicator-config-controls">
                <button class="btn-save-config" id="save-indicator-config" data-tooltip="Save all changes (Ctrl+S)">
                    Save All
                </button>
                <button class="btn-reset-config" id="reset-indicator-config" data-tooltip="Reset all indicators to defaults">
                    Reset All
                </button>
            </div>
        </div>
    </div>
    
    <!-- Bulk Operations Panel (Hidden by default) -->
    <div class="bulk-operations">
        <span>Selected indicators:</span>
        <button class="btn-bulk enable" id="bulk-enable" disabled data-tooltip="Enable all selected indicators">
            Enable Selected
        </button>
        <button class="btn-bulk disable" id="bulk-disable" disabled data-tooltip="Disable all selected indicators">
            Disable Selected
        </button>
        <button class="btn-bulk delete" id="bulk-delete" disabled data-tooltip="Delete all selected indicators">
            Delete Selected
        </button>
        <button class="btn-bulk" id="select-all" data-tooltip="Select all indicators (Ctrl+A)">
            Select All
        </button>
    </div>
    
    <!-- Enhanced Indicator Container -->
    <div id="indicator-config-container" class="enhanced-indicator-container">
        <!-- Indicator panels will be dynamically generated here -->
    </div>
    
    <!-- Status Bar -->
    <div class="config-status-bar">
        <div class="status-info">
            <span id="indicator-count">0 indicators configured</span>
            <span id="enabled-count">0 enabled</span>
        </div>
        <div class="status-actions">
            <button class="btn-status" id="validate-config" data-tooltip="Validate current configuration">
                Validate
            </button>
            <button class="btn-status" id="optimize-config" data-tooltip="Optimize configuration for performance">
                Optimize
            </button>
        </div>
    </div>
</div>

<!-- Enhanced Indicator Panel Template -->
<template id="enhanced-indicator-panel-template">
    <div class="indicator-panel" data-indicator="">
        <div class="indicator-header">
            <!-- Drag Handle -->
            <span class="drag-handle" data-tooltip="Drag to reorder">⋮⋮</span>
            
            <!-- Indicator Title Section -->
            <div class="indicator-title">
                <input type="checkbox" class="indicator-enabled" data-tooltip="Enable/disable this indicator">
                <span class="indicator-name"></span>
                <span class="indicator-type"></span>
                <span class="indicator-status"></span>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn-quick-action btn-duplicate" data-tooltip="Duplicate this indicator">
                    📋
                </button>
                <button class="btn-quick-action btn-reset" data-tooltip="Reset to defaults">
                    🔄
                </button>
                <button class="btn-quick-action btn-settings" data-tooltip="Advanced settings">
                    ⚙️
                </button>
            </div>
            
            <!-- Expand/Collapse -->
            <div class="indicator-controls">
                <span class="expand-icon">▼</span>
            </div>
        </div>
        
        <!-- Indicator Content -->
        <div class="indicator-content">
            <!-- Configuration sections will be dynamically generated -->
            <div class="config-sections">
                <!-- Basic Configuration -->
                <div class="config-section basic-config">
                    <h4>Basic Settings</h4>
                    <div class="config-rows">
                        <!-- Dynamic config rows -->
                    </div>
                </div>
                
                <!-- Visual Configuration -->
                <div class="config-section visual-config">
                    <h4>Visual Settings</h4>
                    <div class="config-rows">
                        <!-- Color, line width, etc. -->
                    </div>
                </div>
                
                <!-- Advanced Configuration -->
                <div class="config-section advanced-config">
                    <h4>Advanced Settings</h4>
                    <div class="config-rows">
                        <!-- Advanced parameters -->
                    </div>
                </div>
            </div>
            
            <!-- Indicator Preview -->
            <div class="indicator-preview">
                <h4>Preview</h4>
                <div class="preview-chart" data-tooltip="Live preview of indicator values">
                    <!-- Mini chart preview -->
                </div>
            </div>
            
            <!-- Indicator Actions -->
            <div class="indicator-actions">
                <button class="btn-indicator-action" data-action="test">Test</button>
                <button class="btn-indicator-action" data-action="export">Export</button>
                <button class="btn-indicator-action" data-action="share">Share</button>
            </div>
        </div>
    </div>
</template>

<!-- Preset Save Dialog -->
<div id="preset-save-dialog" class="modal-dialog" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Save Configuration Preset</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="preset-name">Preset Name:</label>
                <input type="text" id="preset-name" placeholder="Enter preset name" required>
            </div>
            <div class="form-group">
                <label for="preset-display-name">Display Name:</label>
                <input type="text" id="preset-display-name" placeholder="Friendly display name">
            </div>
            <div class="form-group">
                <label for="preset-description">Description:</label>
                <textarea id="preset-description" placeholder="Describe this preset configuration" rows="3"></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-modal-cancel">Cancel</button>
            <button class="btn-modal-save">Save Preset</button>
        </div>
    </div>
</div>

<!-- Configuration Validation Results -->
<div id="validation-results" class="validation-panel" style="display: none;">
    <div class="validation-header">
        <h4>Configuration Validation</h4>
        <button class="validation-close">&times;</button>
    </div>
    <div class="validation-content">
        <div class="validation-summary">
            <div class="validation-stat">
                <span class="stat-label">Valid:</span>
                <span class="stat-value valid" id="valid-count">0</span>
            </div>
            <div class="validation-stat">
                <span class="stat-label">Warnings:</span>
                <span class="stat-value warning" id="warning-count">0</span>
            </div>
            <div class="validation-stat">
                <span class="stat-label">Errors:</span>
                <span class="stat-value error" id="error-count">0</span>
            </div>
        </div>
        <div class="validation-details">
            <!-- Validation messages will be populated here -->
        </div>
    </div>
</div>

<style>
/* Additional styles for enhanced features */
.enhanced-config-panel {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
}

.config-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #2a2a2a;
    border-top: 1px solid #444;
    font-size: 13px;
    color: #888;
}

.status-info {
    display: flex;
    gap: 20px;
}

.status-actions {
    display: flex;
    gap: 8px;
}

.btn-status {
    padding: 4px 8px;
    background: #444;
    color: #d1d4dc;
    border: 1px solid #555;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.btn-status:hover {
    background: #555;
}

.modal-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: #1e1e1e;
    border: 1px solid #555;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    color: #d1d4dc;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #444;
}

.modal-close {
    background: none;
    border: none;
    color: #888;
    font-size: 24px;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    background: #2a2a2a;
    border: 1px solid #555;
    border-radius: 4px;
    color: #d1d4dc;
    padding: 8px 12px;
    font-size: 14px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 16px 20px;
    border-top: 1px solid #444;
}

.btn-modal-cancel,
.btn-modal-save {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-modal-cancel {
    background: #666;
    color: white;
}

.btn-modal-save {
    background: #4CAF50;
    color: white;
}

.validation-panel {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 350px;
    background: #1e1e1e;
    border: 1px solid #555;
    border-radius: 8px;
    color: #d1d4dc;
    z-index: 9999;
}

.validation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #444;
}

.validation-summary {
    display: flex;
    justify-content: space-around;
    padding: 16px;
    background: #2a2a2a;
}

.validation-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #888;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    margin-top: 4px;
}

.stat-value.valid { color: #4CAF50; }
.stat-value.warning { color: #FF9800; }
.stat-value.error { color: #f44336; }
</style>
