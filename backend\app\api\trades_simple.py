"""
Trade Management API Endpoints (Simplified)
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.core.data_access import TradeDataAccess, OHLCVDataAccess
from app.services.indicators import IndicatorsService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/mark")
async def create_trade_mark(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    mark_type: str = Query(..., description="Mark type (entry or exit)"),
    entry_side: Optional[str] = Query(None, description="Entry side (buy or sell)"),
    timestamp: str = Query(..., description="Mark timestamp (ISO format)"),
    price: float = Query(..., description="Price at mark", gt=0),
    linked_trade_id: Optional[int] = Query(None, description="Linked trade ID")
):
    """Create a trade mark (entry or exit)"""
    try:
        # Parse timestamp
        mark_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Validate mark type
        if mark_type.lower() not in ['entry', 'exit']:
            raise HTTPException(status_code=400, detail="Mark type must be 'entry' or 'exit'")
        
        # Validate entry side for entry marks
        if mark_type.lower() == 'entry' and not entry_side:
            raise HTTPException(status_code=400, detail="Entry side is required for entry marks")
        
        if entry_side and entry_side.lower() not in ['buy', 'sell']:
            raise HTTPException(status_code=400, detail="Entry side must be 'buy' or 'sell'")
        
        # Get OHLCV snapshot at the time
        ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
            symbol=symbol.upper(),
            timeframe=timeframe,
            limit=1
        )
        
        ohlcv_snapshot = ohlcv_data[0] if ohlcv_data else None
        
        # Insert trade mark
        mark_id = TradeDataAccess.insert_mark(
            symbol=symbol.upper(),
            timeframe=timeframe,
            mark_type=mark_type.lower(),
            entry_side=entry_side.lower() if entry_side else None,
            timestamp=mark_timestamp,
            price=price,
            ohlcv_snapshot=ohlcv_snapshot,
            linked_trade_id=linked_trade_id
        )
        
        return {
            "success": True,
            "data": {
                "mark_id": mark_id,
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "mark_type": mark_type.lower(),
                "entry_side": entry_side.lower() if entry_side else None,
                "timestamp": mark_timestamp.isoformat(),
                "price": price
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating trade mark: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/marks")
async def get_trade_marks(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    timeframe: Optional[str] = Query(None, description="Filter by timeframe"),
    mark_type: Optional[str] = Query(None, description="Filter by mark type"),
    limit: int = Query(100, description="Number of marks to return", ge=1, le=1000)
):
    """Get trade marks"""
    try:
        marks = TradeDataAccess.get_marks(
            symbol=symbol.upper() if symbol else None,
            timeframe=timeframe,
            mark_type=mark_type.lower() if mark_type else None,
            limit=limit
        )
        
        return {
            "success": True,
            "data": {
                "marks": marks,
                "count": len(marks),
                "filters": {
                    "symbol": symbol.upper() if symbol else None,
                    "timeframe": timeframe,
                    "mark_type": mark_type.lower() if mark_type else None
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting trade marks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/marks/{mark_id}")
async def get_trade_mark(mark_id: int):
    """Get specific trade mark by ID"""
    try:
        mark = TradeDataAccess.get_mark_by_id(mark_id)
        
        if not mark:
            raise HTTPException(status_code=404, detail="Trade mark not found")
        
        return {
            "success": True,
            "data": mark
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trade mark: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/strategy-log")
async def create_strategy_log(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    entry_id: int = Query(..., description="Entry mark ID"),
    exit_id: int = Query(..., description="Exit mark ID")
):
    """Create strategy log entry from entry and exit marks"""
    try:
        # Get entry and exit marks
        entry_mark = TradeDataAccess.get_mark_by_id(entry_id)
        exit_mark = TradeDataAccess.get_mark_by_id(exit_id)
        
        if not entry_mark or not exit_mark:
            raise HTTPException(status_code=404, detail="Entry or exit mark not found")
        
        if entry_mark['mark_type'] != 'entry' or exit_mark['mark_type'] != 'exit':
            raise HTTPException(status_code=400, detail="Invalid mark types")
        
        # Calculate profit percentage
        entry_price = float(entry_mark['price'])
        exit_price = float(exit_mark['price'])
        entry_side = entry_mark['entry_side']
        
        if entry_side == 'buy':
            profit_pct = ((exit_price - entry_price) / entry_price) * 100
        else:  # sell
            profit_pct = ((entry_price - exit_price) / entry_price) * 100
        
        # Create strategy log entry
        strategy_id = TradeDataAccess.insert_strategy_log(
            symbol=symbol.upper(),
            timeframe=timeframe,
            entry_id=entry_id,
            exit_id=exit_id,
            entry_side=entry_side,
            profit_pct=profit_pct,
            entry_ohlcv=entry_mark.get('ohlcv_snapshot'),
            exit_ohlcv=exit_mark.get('ohlcv_snapshot'),
            entry_indicators=entry_mark.get('indicator_snapshot'),
            exit_indicators=exit_mark.get('indicator_snapshot')
        )
        
        return {
            "success": True,
            "data": {
                "strategy_id": strategy_id,
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "entry_id": entry_id,
                "exit_id": exit_id,
                "entry_side": entry_side,
                "profit_pct": round(profit_pct, 4),
                "entry_price": entry_price,
                "exit_price": exit_price
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating strategy log: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/strategy-log")
async def get_strategy_log(
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    timeframe: Optional[str] = Query(None, description="Filter by timeframe"),
    limit: int = Query(100, description="Number of records", ge=1, le=1000)
):
    """Get strategy log entries"""
    try:
        log_entries = TradeDataAccess.get_strategy_log(
            symbol=symbol.upper() if symbol else None,
            timeframe=timeframe,
            limit=limit
        )
        
        # Calculate summary statistics
        if log_entries:
            profits = [float(entry['profit_pct']) for entry in log_entries]
            total_trades = len(profits)
            winning_trades = len([p for p in profits if p > 0])
            losing_trades = len([p for p in profits if p < 0])
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            avg_profit = sum(profits) / total_trades if total_trades > 0 else 0
            
            summary = {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate_pct": round(win_rate, 2),
                "avg_profit_pct": round(avg_profit, 4),
                "total_profit_pct": round(sum(profits), 4)
            }
        else:
            summary = {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate_pct": 0,
                "avg_profit_pct": 0,
                "total_profit_pct": 0
            }
        
        return {
            "success": True,
            "data": {
                "log_entries": log_entries,
                "count": len(log_entries),
                "summary": summary,
                "filters": {
                    "symbol": symbol.upper() if symbol else None,
                    "timeframe": timeframe
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting strategy log: {e}")
        raise HTTPException(status_code=500, detail=str(e))
