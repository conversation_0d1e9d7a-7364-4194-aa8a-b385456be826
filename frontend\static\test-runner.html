<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Marking Tools Test Runner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .status {
            margin-left: auto;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status.running {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.passed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .results {
            padding: 20px;
        }
        
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .summary-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .test-details {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .test-details h3 {
            background: #e9ecef;
            margin: 0;
            padding: 15px 20px;
            color: #333;
        }
        
        .test-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .test-item {
            padding: 12px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            font-size: 18px;
            width: 20px;
        }
        
        .test-name {
            font-weight: 500;
            flex: 1;
        }
        
        .test-message {
            color: #666;
            font-size: 12px;
        }
        
        .test-time {
            color: #999;
            font-size: 11px;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Marking Tools Test Suite</h1>
            <p>Comprehensive testing for entry mark creation and exit mark data extraction</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="runTests()">Run All Tests</button>
            <button class="btn btn-secondary" onclick="clearResults()">Clear Results</button>
            <button class="btn btn-secondary" onclick="exportResults()">Export Results</button>
            <div class="status" id="status">Ready</div>
        </div>
        
        <div class="results" id="results">
            <div class="loading">
                <p>Click "Run All Tests" to start testing the marking tools functionality.</p>
            </div>
        </div>
    </div>

    <script src="js/tests/marking-tools-tests.js"></script>
    <script>
        let testRunner = null;
        let testResults = null;

        async function runTests() {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            
            // Update status
            statusEl.textContent = 'Running Tests...';
            statusEl.className = 'status running';
            
            // Show loading
            resultsEl.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Running comprehensive tests...</p>
                </div>
            `;
            
            try {
                // Create test runner and run tests
                testRunner = new MarkingToolsTests();
                testResults = await testRunner.runAllTests();
                
                // Update status
                const allPassed = testResults.suitesPassed === testResults.suitesTotal;
                statusEl.textContent = allPassed ? 'All Tests Passed' : 'Some Tests Failed';
                statusEl.className = allPassed ? 'status passed' : 'status failed';
                
                // Display results
                displayResults(testResults);
                
            } catch (error) {
                console.error('Test execution error:', error);
                statusEl.textContent = 'Test Execution Failed';
                statusEl.className = 'status failed';
                
                resultsEl.innerHTML = `
                    <div class="loading">
                        <p style="color: #dc3545;">❌ Test execution failed: ${error.message}</p>
                    </div>
                `;
            }
        }

        function displayResults(results) {
            const resultsEl = document.getElementById('results');
            
            const passRate = ((results.testsPassed / results.testsTotal) * 100).toFixed(1);
            const suitePassRate = ((results.suitesPassed / results.suitesTotal) * 100).toFixed(1);
            
            resultsEl.innerHTML = `
                <div class="summary">
                    <div class="summary-card">
                        <h3>Test Suites</h3>
                        <div class="number">${results.suitesPassed}/${results.suitesTotal}</div>
                        <p>${suitePassRate}% passed</p>
                    </div>
                    <div class="summary-card">
                        <h3>Individual Tests</h3>
                        <div class="number">${results.testsPassed}/${results.testsTotal}</div>
                        <p>${passRate}% passed</p>
                    </div>
                    <div class="summary-card">
                        <h3>Execution Time</h3>
                        <div class="number">${new Date().toLocaleTimeString()}</div>
                        <p>Completed</p>
                    </div>
                </div>
                
                <div class="test-details">
                    <h3>📋 Test Details</h3>
                    <div class="test-list">
                        ${results.results.map(test => `
                            <div class="test-item">
                                <div class="test-status">${test.passed ? '✅' : '❌'}</div>
                                <div class="test-name">${test.testName}</div>
                                <div class="test-message">${test.message}</div>
                                <div class="test-time">${new Date(test.timestamp).toLocaleTimeString()}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function clearResults() {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            
            statusEl.textContent = 'Ready';
            statusEl.className = 'status';
            
            resultsEl.innerHTML = `
                <div class="loading">
                    <p>Click "Run All Tests" to start testing the marking tools functionality.</p>
                </div>
            `;
            
            testResults = null;
        }

        function exportResults() {
            if (!testResults) {
                alert('No test results to export. Please run tests first.');
                return;
            }
            
            const report = testRunner.generateReport();
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `marking-tools-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Auto-run tests on page load for development
        // window.addEventListener('load', () => {
        //     setTimeout(runTests, 1000);
        // });
    </script>
</body>
</html>
