"""
Enhanced Multi-Indicator Engine
Supports configurable indicators with multiple parameters and JSON value storage
"""

import pandas as pd
import numpy as np
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import logging
from dataclasses import dataclass

# Try to import pandas_ta for advanced indicators
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    logging.warning("pandas_ta not available, using manual calculations")

logger = logging.getLogger(__name__)

@dataclass
class IndicatorConfig:
    """Configuration for a single indicator"""
    name: str
    parameters: Dict[str, Any]
    chart_type: str = 'overlay'  # 'overlay' or 'subchart'
    enabled: bool = True

class MultiIndicatorEngine:
    """
    Enhanced indicator engine supporting multiple parameters per indicator
    and JSON-based value storage for complex indicators
    """
    
    # Default configurations for all supported indicators
    DEFAULT_CONFIGS = {
        'EMA': {
            'periods': [20, 50, 100],
            'colors': ['#FF6B6B', '#4ECDC4', '#45B7D1'],
            'lineWidth': 2
        },
        'SMA': {
            'periods': [20, 50, 100],
            'colors': ['#9C27B0', '#FF9800', '#795548'],
            'lineWidth': 2
        },
        'RSI': {
            'periods': [14],
            'colors': ['#2196F3'],
            'overbought': 70,
            'oversold': 30,
            'lineWidth': 2
        },
        'MACD': {
            'fast': 12,
            'slow': 26,
            'signal': 9,
            'colors': {
                'macd': '#2196F3',
                'signal': '#FF9800',
                'histogram': '#4CAF50'
            },
            'lineWidth': 2
        },
        'BOLLINGER_BANDS': {
            'period': 20,
            'stdDev': 2,
            'colors': {
                'upper': '#FF5722',
                'middle': '#607D8B',
                'lower': '#FF5722'
            },
            'fillOpacity': 0.1,
            'lineWidth': 1
        },
        'STOCHASTIC': {
            'kPeriod': 14,
            'dPeriod': 3,
            'colors': {
                'k': '#E91E63',
                'd': '#9C27B0'
            },
            'overbought': 80,
            'oversold': 20,
            'lineWidth': 2
        },
        'VOLUME_SMA': {
            'period': 20,
            'color': '#FFC107',
            'lineWidth': 2
        },
        'ATR': {
            'period': 14,
            'color': '#795548',
            'lineWidth': 2
        }
    }

    @classmethod
    def prepare_dataframe(cls, ohlcv_data: List[Dict]) -> pd.DataFrame:
        """Convert OHLCV data to pandas DataFrame"""
        if not ohlcv_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(ohlcv_data)
        
        # Ensure proper column names and types
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Missing required column: {col}")
        
        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # Convert OHLCV to numeric
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Sort by timestamp
        df.sort_index(inplace=True)
        
        return df

    @classmethod
    def calculate_ema_multiple(cls, df: pd.DataFrame, periods: List[int]) -> Dict[str, List[float]]:
        """Calculate multiple EMAs with different periods"""
        results = {}
        
        for period in periods:
            try:
                if PANDAS_TA_AVAILABLE:
                    ema = ta.ema(df['close'], length=period)
                else:
                    # Manual EMA calculation
                    ema = df['close'].ewm(span=period, adjust=False).mean()
                
                results[f'EMA_{period}'] = ema.fillna(0).tolist()
            except Exception as e:
                logger.error(f"Error calculating EMA {period}: {e}")
                results[f'EMA_{period}'] = [0] * len(df)
        
        return results

    @classmethod
    def calculate_sma_multiple(cls, df: pd.DataFrame, periods: List[int]) -> Dict[str, List[float]]:
        """Calculate multiple SMAs with different periods"""
        results = {}
        
        for period in periods:
            try:
                if PANDAS_TA_AVAILABLE:
                    sma = ta.sma(df['close'], length=period)
                else:
                    # Manual SMA calculation
                    sma = df['close'].rolling(window=period).mean()
                
                results[f'SMA_{period}'] = sma.fillna(0).tolist()
            except Exception as e:
                logger.error(f"Error calculating SMA {period}: {e}")
                results[f'SMA_{period}'] = [0] * len(df)
        
        return results

    @classmethod
    def calculate_rsi_multiple(cls, df: pd.DataFrame, periods: List[int]) -> Dict[str, List[float]]:
        """Calculate multiple RSIs with different periods"""
        results = {}
        
        for period in periods:
            try:
                if PANDAS_TA_AVAILABLE:
                    rsi = ta.rsi(df['close'], length=period)
                else:
                    # Manual RSI calculation
                    delta = df['close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                    rs = gain / loss
                    rsi = 100 - (100 / (1 + rs))
                
                results[f'RSI_{period}'] = rsi.fillna(50).tolist()
            except Exception as e:
                logger.error(f"Error calculating RSI {period}: {e}")
                results[f'RSI_{period}'] = [50] * len(df)
        
        return results

    @classmethod
    def calculate_macd(cls, df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, List[float]]:
        """Calculate MACD with all components"""
        try:
            if PANDAS_TA_AVAILABLE:
                macd_data = ta.macd(df['close'], fast=fast, slow=slow, signal=signal)
                return {
                    'macd': macd_data[f'MACD_{fast}_{slow}_{signal}'].fillna(0).tolist(),
                    'signal': macd_data[f'MACDs_{fast}_{slow}_{signal}'].fillna(0).tolist(),
                    'histogram': macd_data[f'MACDh_{fast}_{slow}_{signal}'].fillna(0).tolist()
                }
            else:
                # Manual MACD calculation
                ema_fast = df['close'].ewm(span=fast).mean()
                ema_slow = df['close'].ewm(span=slow).mean()
                macd_line = ema_fast - ema_slow
                signal_line = macd_line.ewm(span=signal).mean()
                histogram = macd_line - signal_line
                
                return {
                    'macd': macd_line.fillna(0).tolist(),
                    'signal': signal_line.fillna(0).tolist(),
                    'histogram': histogram.fillna(0).tolist()
                }
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return {
                'macd': [0] * len(df),
                'signal': [0] * len(df),
                'histogram': [0] * len(df)
            }

    @classmethod
    def calculate_bollinger_bands(cls, df: pd.DataFrame, period: int = 20, std_dev: float = 2) -> Dict[str, List[float]]:
        """Calculate Bollinger Bands"""
        try:
            if PANDAS_TA_AVAILABLE:
                bb = ta.bbands(df['close'], length=period, std=std_dev)
                return {
                    'upper': bb[f'BBU_{period}_{std_dev}'].fillna(0).tolist(),
                    'middle': bb[f'BBM_{period}_{std_dev}'].fillna(0).tolist(),
                    'lower': bb[f'BBL_{period}_{std_dev}'].fillna(0).tolist()
                }
            else:
                # Manual Bollinger Bands calculation
                sma = df['close'].rolling(window=period).mean()
                std = df['close'].rolling(window=period).std()
                upper = sma + (std * std_dev)
                lower = sma - (std * std_dev)
                
                return {
                    'upper': upper.fillna(0).tolist(),
                    'middle': sma.fillna(0).tolist(),
                    'lower': lower.fillna(0).tolist()
                }
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return {
                'upper': [0] * len(df),
                'middle': [0] * len(df),
                'lower': [0] * len(df)
            }

    @classmethod
    def calculate_stochastic(cls, df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Dict[str, List[float]]:
        """Calculate Stochastic Oscillator"""
        try:
            if PANDAS_TA_AVAILABLE:
                stoch = ta.stoch(df['high'], df['low'], df['close'], k=k_period, d=d_period)
                return {
                    'k': stoch[f'STOCHk_{k_period}_{d_period}_3'].fillna(50).tolist(),
                    'd': stoch[f'STOCHd_{k_period}_{d_period}_3'].fillna(50).tolist()
                }
            else:
                # Manual Stochastic calculation
                lowest_low = df['low'].rolling(window=k_period).min()
                highest_high = df['high'].rolling(window=k_period).max()
                k_percent = 100 * ((df['close'] - lowest_low) / (highest_high - lowest_low))
                d_percent = k_percent.rolling(window=d_period).mean()
                
                return {
                    'k': k_percent.fillna(50).tolist(),
                    'd': d_percent.fillna(50).tolist()
                }
        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return {
                'k': [50] * len(df),
                'd': [50] * len(df)
            }

    @classmethod
    def calculate_volume_sma(cls, df: pd.DataFrame, period: int = 20) -> Dict[str, List[float]]:
        """Calculate Volume SMA"""
        try:
            volume_sma = df['volume'].rolling(window=period).mean()
            return {
                'volume_sma': volume_sma.fillna(0).tolist()
            }
        except Exception as e:
            logger.error(f"Error calculating Volume SMA: {e}")
            return {'volume_sma': [0] * len(df)}

    @classmethod
    def calculate_atr(cls, df: pd.DataFrame, period: int = 14) -> Dict[str, List[float]]:
        """Calculate Average True Range"""
        try:
            if PANDAS_TA_AVAILABLE:
                atr = ta.atr(df['high'], df['low'], df['close'], length=period)
                return {'atr': atr.fillna(0).tolist()}
            else:
                # Manual ATR calculation
                high_low = df['high'] - df['low']
                high_close = np.abs(df['high'] - df['close'].shift())
                low_close = np.abs(df['low'] - df['close'].shift())
                true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = true_range.rolling(window=period).mean()
                
                return {'atr': atr.fillna(0).tolist()}
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return {'atr': [0] * len(df)}

    @classmethod
    def calculate_indicator(cls, indicator_name: str, df: pd.DataFrame, config: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate a single indicator with given configuration"""
        indicator_name = indicator_name.upper()
        
        # Merge with default config
        default_config = cls.DEFAULT_CONFIGS.get(indicator_name, {})
        merged_config = {**default_config, **config}
        
        if indicator_name == 'EMA':
            periods = merged_config.get('periods', [20])
            return cls.calculate_ema_multiple(df, periods)
        
        elif indicator_name == 'SMA':
            periods = merged_config.get('periods', [20])
            return cls.calculate_sma_multiple(df, periods)
        
        elif indicator_name == 'RSI':
            periods = merged_config.get('periods', [14])
            return cls.calculate_rsi_multiple(df, periods)
        
        elif indicator_name == 'MACD':
            return cls.calculate_macd(
                df,
                fast=merged_config.get('fast', 12),
                slow=merged_config.get('slow', 26),
                signal=merged_config.get('signal', 9)
            )
        
        elif indicator_name == 'BOLLINGER_BANDS':
            return cls.calculate_bollinger_bands(
                df,
                period=merged_config.get('period', 20),
                std_dev=merged_config.get('stdDev', 2)
            )
        
        elif indicator_name == 'STOCHASTIC':
            return cls.calculate_stochastic(
                df,
                k_period=merged_config.get('kPeriod', 14),
                d_period=merged_config.get('dPeriod', 3)
            )
        
        elif indicator_name == 'VOLUME_SMA':
            return cls.calculate_volume_sma(
                df,
                period=merged_config.get('period', 20)
            )
        
        elif indicator_name == 'ATR':
            return cls.calculate_atr(
                df,
                period=merged_config.get('period', 14)
            )
        
        else:
            logger.warning(f"Unknown indicator: {indicator_name}")
            return {}

    @classmethod
    def calculate_all_indicators(cls, ohlcv_data: List[Dict], 
                               indicators_config: Dict[str, Dict] = None) -> Dict[str, Any]:
        """
        Calculate all requested indicators with their configurations
        
        Args:
            ohlcv_data: List of OHLCV dictionaries
            indicators_config: Dictionary of indicator configurations
        
        Returns:
            Dictionary containing all calculated indicators with timestamps
        """
        if not ohlcv_data:
            return {}
        
        try:
            df = cls.prepare_dataframe(ohlcv_data)
            results = {
                'timestamps': df.index.strftime('%Y-%m-%dT%H:%M:%S').tolist()
            }
            
            # Use default config if none provided
            if indicators_config is None:
                indicators_config = {
                    'EMA': {'periods': [20, 50]},
                    'RSI': {'periods': [14]},
                    'MACD': {}
                }
            
            # Calculate each requested indicator
            for indicator_name, config in indicators_config.items():
                try:
                    indicator_values = cls.calculate_indicator(indicator_name, df, config)
                    if indicator_values:
                        results[indicator_name.lower()] = indicator_values
                except Exception as e:
                    logger.error(f"Error calculating {indicator_name}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error in calculate_all_indicators: {e}")
            return {}

    @classmethod
    def get_default_config(cls, indicator_name: str) -> Dict[str, Any]:
        """Get default configuration for an indicator"""
        return cls.DEFAULT_CONFIGS.get(indicator_name.upper(), {})

    @classmethod
    def get_supported_indicators(cls) -> List[str]:
        """Get list of all supported indicators"""
        return list(cls.DEFAULT_CONFIGS.keys())

    @classmethod
    def validate_config(cls, indicator_name: str, config: Dict[str, Any]) -> bool:
        """Validate indicator configuration"""
        indicator_name = indicator_name.upper()

        if indicator_name not in cls.DEFAULT_CONFIGS:
            return False

        # Basic validation - can be enhanced with JSON schema validation
        default_config = cls.DEFAULT_CONFIGS[indicator_name]

        # Check if all required parameters are present or have defaults
        for key in config.keys():
            if key not in default_config and key not in ['colors', 'lineWidth']:
                logger.warning(f"Unknown parameter {key} for {indicator_name}")

        return True

    @classmethod
    def get_indicator_values_at_timestamp(cls, indicators_data: Dict[str, Any],
                                        timestamp: datetime) -> Dict[str, Any]:
        """
        Extract indicator values at a specific timestamp

        Args:
            indicators_data: Full indicators data with timestamps
            timestamp: Target timestamp

        Returns:
            Dictionary of indicator values at the timestamp
        """
        if not indicators_data or 'timestamps' not in indicators_data:
            return {}

        try:
            timestamps = indicators_data['timestamps']
            target_timestamp = timestamp.strftime('%Y-%m-%dT%H:%M:%S')

            # Find exact match or closest timestamp
            try:
                index = timestamps.index(target_timestamp)
            except ValueError:
                # Find closest timestamp
                target_dt = pd.to_datetime(timestamp)
                timestamps_dt = [pd.to_datetime(ts) for ts in timestamps]
                closest_index = min(range(len(timestamps_dt)),
                                  key=lambda i: abs(timestamps_dt[i] - target_dt))
                index = closest_index

            # Extract values at the index
            result = {'timestamp': timestamps[index]}

            for indicator_name, values in indicators_data.items():
                if indicator_name == 'timestamps':
                    continue

                if isinstance(values, dict):
                    # Multi-value indicator (like MACD, EMA with multiple periods)
                    result[indicator_name] = {}
                    for key, value_list in values.items():
                        if isinstance(value_list, list) and len(value_list) > index:
                            result[indicator_name][key] = value_list[index]
                elif isinstance(values, list) and len(values) > index:
                    # Single-value indicator
                    result[indicator_name] = values[index]

            return result

        except Exception as e:
            logger.error(f"Error extracting indicator values at timestamp: {e}")
            return {}
