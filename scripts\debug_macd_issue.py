#!/usr/bin/env python3
"""
Script to debug MACD calculation and hover display issues
"""

import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.core.database import get_db_cursor
from app.services.indicators import IndicatorsService
import pandas as pd

def test_macd_calculation():
    """Test MACD calculation with sample data"""
    print("🔍 Testing MACD calculation...")
    
    with get_db_cursor(dict_cursor=True) as cursor:
        # Get recent OHLCV data for testing
        cursor.execute("""
            SELECT timestamp, open, high, low, close, volume
            FROM ohlcv_data 
            WHERE symbol = 'BTCUSDT' AND timeframe = '15m'
            ORDER BY timestamp DESC 
            LIMIT 100
        """)
        
        ohlcv_records = cursor.fetchall()
        
        if not ohlcv_records:
            print("❌ No OHLCV data found for testing")
            return
            
        print(f"📊 Found {len(ohlcv_records)} OHLCV records for testing")
        
        # Convert to the format expected by IndicatorsService
        ohlcv_data = []
        for record in reversed(ohlcv_records):  # Reverse to get chronological order
            ohlcv_data.append({
                'timestamp': record['timestamp'],
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': float(record['volume'])
            })
        
        # Test MACD calculation
        try:
            df = IndicatorsService.prepare_dataframe(ohlcv_data)
            print(f"📈 Prepared dataframe with {len(df)} rows")
            print(f"   Sample close prices: {df['close'].tail(5).tolist()}")
            
            # Calculate MACD
            macd_result = IndicatorsService.calculate_macd(df, fast=12, slow=26, signal=9)
            
            print(f"\n🎯 MACD Calculation Results:")
            print(f"   MACD line length: {len(macd_result['macd'])}")
            print(f"   Signal line length: {len(macd_result['signal'])}")
            print(f"   Histogram length: {len(macd_result['histogram'])}")
            
            # Check for non-zero values
            macd_nonzero = sum(1 for x in macd_result['macd'] if abs(x) > 0.0001)
            signal_nonzero = sum(1 for x in macd_result['signal'] if abs(x) > 0.0001)
            histogram_nonzero = sum(1 for x in macd_result['histogram'] if abs(x) > 0.0001)
            
            print(f"   MACD non-zero values: {macd_nonzero}")
            print(f"   Signal non-zero values: {signal_nonzero}")
            print(f"   Histogram non-zero values: {histogram_nonzero}")
            
            # Show latest values
            latest_macd = macd_result['macd'].iloc[-1] if len(macd_result['macd']) > 0 else 0
            latest_signal = macd_result['signal'].iloc[-1] if len(macd_result['signal']) > 0 else 0
            latest_histogram = macd_result['histogram'].iloc[-1] if len(macd_result['histogram']) > 0 else 0
            
            print(f"\n📊 Latest MACD Values:")
            print(f"   MACD: {latest_macd:.6f}")
            print(f"   Signal: {latest_signal:.6f}")
            print(f"   Histogram: {latest_histogram:.6f}")
            
            # Show some historical values to see the trend
            print(f"\n📈 Last 10 MACD values:")
            for i in range(max(0, len(macd_result['macd']) - 10), len(macd_result['macd'])):
                macd_val = macd_result['macd'].iloc[i]
                signal_val = macd_result['signal'].iloc[i]
                histogram_val = macd_result['histogram'].iloc[i]
                print(f"   [{i}] MACD: {macd_val:.6f}, Signal: {signal_val:.6f}, Histogram: {histogram_val:.6f}")
            
            # Test the full indicators calculation
            indicators_config = {
                'macd': {'fast': 12, 'slow': 26, 'signal': 9}
            }
            
            full_indicators = IndicatorsService.calculate_all_indicators(ohlcv_data, indicators_config)
            
            if 'macd' in full_indicators:
                macd_data = full_indicators['macd']
                print(f"\n🔧 Full indicators calculation:")
                print(f"   MACD array length: {len(macd_data['macd'])}")
                print(f"   Signal array length: {len(macd_data['signal'])}")
                print(f"   Histogram array length: {len(macd_data['histogram'])}")
                
                # Check latest values from full calculation
                if macd_data['macd']:
                    latest_full_macd = macd_data['macd'][-1]
                    latest_full_signal = macd_data['signal'][-1]
                    latest_full_histogram = macd_data['histogram'][-1]
                    
                    print(f"   Latest MACD: {latest_full_macd:.6f}")
                    print(f"   Latest Signal: {latest_full_signal:.6f}")
                    print(f"   Latest Histogram: {latest_full_histogram:.6f}")
                    
                    if abs(latest_full_macd) < 0.0001 and abs(latest_full_signal) < 0.0001:
                        print("   ❌ PROBLEM: MACD and Signal are both near zero!")
                        print("   This suggests an issue with the calculation or data")
                    else:
                        print("   ✅ MACD values look normal")
            
        except Exception as e:
            print(f"❌ Error calculating MACD: {e}")
            import traceback
            traceback.print_exc()

def check_manual_marks_macd():
    """Check manual marks for MACD data issues"""
    print("\n🔍 Checking manual marks for MACD data...")
    
    with get_db_cursor(dict_cursor=True) as cursor:
        # Get recent marks with their data
        cursor.execute("""
            SELECT id, symbol, timeframe, mark_type, entry_side, timestamp, price, 
                   indicator_snapshot, created_at
            FROM manual_marks 
            WHERE indicator_snapshot IS NOT NULL
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        marks = cursor.fetchall()
        
        print(f"\n📊 Recent marks with indicators ({len(marks)} found):")
        for mark in marks:
            print(f"\n  Mark ID {mark['id']} ({mark['mark_type']}):")
            print(f"    Price: ${mark['price']:.8f}")
            print(f"    Created: {mark['created_at']}")
            
            # Check indicator data specifically for MACD
            if mark['indicator_snapshot']:
                try:
                    indicators = json.loads(mark['indicator_snapshot'])
                    print(f"    Indicators available: {list(indicators.keys())}")
                    
                    # Check MACD specifically
                    if 'macd' in indicators:
                        macd_data = indicators['macd']
                        print(f"    MACD data: {macd_data}")
                        
                        # Check if values are zero
                        macd_val = macd_data.get('macd', 0)
                        signal_val = macd_data.get('signal', 0)
                        histogram_val = macd_data.get('histogram', 0)
                        
                        if abs(macd_val) < 0.0001 and abs(signal_val) < 0.0001:
                            print("    ❌ MACD and Signal are zero - this is the problem!")
                        else:
                            print("    ✅ MACD values look normal")
                    else:
                        print("    ❌ MACD data: Not found in snapshot")
                        
                except Exception as e:
                    print(f"    ❌ Indicator snapshot: Invalid JSON - {e}")

def test_indicator_value_at_timestamp():
    """Test the get_indicator_value_at_timestamp function"""
    print("\n🔍 Testing get_indicator_value_at_timestamp function...")
    
    with get_db_cursor(dict_cursor=True) as cursor:
        # Get a recent timestamp
        cursor.execute("""
            SELECT timestamp FROM ohlcv_data 
            WHERE symbol = 'BTCUSDT' AND timeframe = '15m'
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ No timestamp found")
            return
            
        test_timestamp = result['timestamp']
        print(f"📅 Testing with timestamp: {test_timestamp}")
        
        # Get OHLCV data for calculation
        cursor.execute("""
            SELECT timestamp, open, high, low, close, volume
            FROM ohlcv_data 
            WHERE symbol = 'BTCUSDT' AND timeframe = '15m'
            AND timestamp <= %s
            ORDER BY timestamp ASC 
            LIMIT 100
        """, (test_timestamp,))
        
        ohlcv_records = cursor.fetchall()
        
        if not ohlcv_records:
            print("❌ No OHLCV data found")
            return
            
        # Convert to expected format
        ohlcv_data = []
        for record in ohlcv_records:
            ohlcv_data.append({
                'timestamp': record['timestamp'],
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': float(record['volume'])
            })
        
        # Test the function
        indicators_config = {
            'macd': {'fast': 12, 'slow': 26, 'signal': 9}
        }
        
        try:
            snapshot = IndicatorsService.get_indicator_value_at_timestamp(
                ohlcv_data, test_timestamp, indicators_config
            )
            
            print(f"📊 Indicator snapshot result: {snapshot}")
            
            if 'macd' in snapshot:
                macd_data = snapshot['macd']
                print(f"   MACD: {macd_data.get('macd', 'N/A')}")
                print(f"   Signal: {macd_data.get('signal', 'N/A')}")
                print(f"   Histogram: {macd_data.get('histogram', 'N/A')}")
                
                if abs(macd_data.get('macd', 0)) < 0.0001 and abs(macd_data.get('signal', 0)) < 0.0001:
                    print("   ❌ PROBLEM: Values are zero in snapshot function!")
                else:
                    print("   ✅ Snapshot function returns normal values")
            else:
                print("   ❌ No MACD data in snapshot")
                
        except Exception as e:
            print(f"❌ Error in get_indicator_value_at_timestamp: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    try:
        test_macd_calculation()
        check_manual_marks_macd()
        test_indicator_value_at_timestamp()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
