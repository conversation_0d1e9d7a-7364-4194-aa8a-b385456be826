# Multi-Indicator Trading System Architecture

## 🎯 System Overview

A comprehensive, configurable multi-indicator trading system that allows users to:
- Select and configure technical indicators visually
- Plot multiple values per indicator (e.g., EMA 50, 100, 200)
- Associate indicator configurations with trading strategies
- Store computed values with full historical tracking
- Provide real-time updates and visual feedback

## 🏗️ Architecture Components

### 1. Database Layer

#### Enhanced `indicators_data` Table
```sql
CREATE TABLE indicators_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    strategy_id INT NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    timestamp DATETIME NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    indicator_config JSON NOT NULL,  -- Configuration used for calculation
    indicator_values JSON NOT NULL,  -- Computed values (multi-value support)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_indicator (strategy_id, symbol, timeframe, timestamp, indicator_name),
    INDEX idx_strategy_symbol_timeframe (strategy_id, symbol, timeframe),
    INDEX idx_timestamp_indicator (timestamp, indicator_name),
    FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
);
```

#### New `indicator_defaults` Table
```sql
CREATE TABLE indicator_defaults (
    id INT AUTO_INCREMENT PRIMARY KEY,
    indicator_name VARCHAR(50) NOT NULL UNIQUE,
    default_config JSON NOT NULL,
    chart_type ENUM('overlay', 'subchart') NOT NULL,
    supports_multiple BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### New `strategy_indicator_configs` Table
```sql
CREATE TABLE strategy_indicator_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    strategy_id INT NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    config JSON NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_strategy_indicator (strategy_id, indicator_name),
    FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
);
```

### 2. Backend Services Architecture

#### Indicator Engine (`backend/app/services/indicator_engine.py`)
- **Multi-Parameter Support**: Calculate indicators with multiple configurations
- **JSON Value Storage**: Store complex indicator outputs (MACD, Bollinger Bands)
- **Batch Processing**: Efficient calculation for historical data
- **Real-time Updates**: Incremental calculation for new data

#### Configuration Manager (`backend/app/services/indicator_config.py`)
- **Default Management**: Load and apply default configurations
- **Strategy Association**: Link indicators to strategies
- **Validation**: Ensure parameter validity
- **Persistence**: Save/load configurations

#### Data Access Layer (`backend/app/core/indicator_data_access.py`)
- **Optimized Queries**: Efficient data retrieval
- **Bulk Operations**: Batch insert/update for performance
- **Time-series Optimization**: Indexed queries for chart data

### 3. API Layer

#### Indicator Management Endpoints
```
POST   /api/v1/indicators/defaults          # Get default configurations
GET    /api/v1/indicators/defaults/{name}   # Get specific indicator defaults
POST   /api/v1/strategies/{id}/indicators   # Save indicator config to strategy
GET    /api/v1/strategies/{id}/indicators   # Get strategy indicator configs
PUT    /api/v1/strategies/{id}/indicators/{name} # Update specific indicator
DELETE /api/v1/strategies/{id}/indicators/{name} # Remove indicator from strategy
POST   /api/v1/indicators/calculate         # Calculate indicators for data
GET    /api/v1/indicators/data             # Get calculated indicator data
```

### 4. Frontend Architecture

#### Component Structure
```
frontend/static/js/indicators/
├── IndicatorConfigManager.js     # Main configuration management
├── IndicatorUI.js               # Visual configuration interface
├── IndicatorCalculator.js       # Client-side calculations
├── IndicatorPlotter.js          # Chart plotting logic
├── DefaultsManager.js           # Default configuration handling
└── components/
    ├── EMAConfig.js            # EMA-specific configuration
    ├── MACDConfig.js           # MACD-specific configuration
    ├── RSIConfig.js            # RSI-specific configuration
    └── BollingerBandsConfig.js # Bollinger Bands configuration
```

## 🔧 Implementation Details

### Indicator Configuration Format

#### EMA Configuration Example
```json
{
  "indicator_name": "EMA",
  "parameters": [
    {"period": 50, "color": "#FF6B6B", "lineWidth": 2},
    {"period": 100, "color": "#4ECDC4", "lineWidth": 2},
    {"period": 200, "color": "#45B7D1", "lineWidth": 2}
  ],
  "chart_type": "overlay",
  "enabled": true
}
```

#### MACD Configuration Example
```json
{
  "indicator_name": "MACD",
  "parameters": {
    "fast": 12,
    "slow": 26,
    "signal": 9,
    "colors": {
      "macd": "#2196F3",
      "signal": "#FF9800",
      "histogram": "#4CAF50"
    }
  },
  "chart_type": "subchart",
  "enabled": true
}
```

### Multi-Value Storage Format

#### EMA Values Example
```json
{
  "EMA_50": 48700.25,
  "EMA_100": 48550.88,
  "EMA_200": 48210.50
}
```

#### MACD Values Example
```json
{
  "macd": 125.45,
  "signal": 98.32,
  "histogram": 27.13
}
```

## 🎨 UI/UX Design Patterns

### Visual Configuration Interface
- **Accordion-style** indicator groups
- **Real-time preview** of parameter changes
- **Color picker integration** for line styling
- **Drag-and-drop reordering** for display priority
- **Toggle switches** for enable/disable
- **Parameter validation** with visual feedback

### Chart Integration
- **Overlay indicators**: EMA, Bollinger Bands on price chart
- **Subchart indicators**: RSI, MACD in separate panels
- **Dynamic scaling**: Auto-adjust chart scales
- **Interactive legends**: Click to show/hide specific lines
- **Hover tooltips**: Display all indicator values at cursor position

## 🚀 Performance Optimizations

### Database Optimizations
- **Composite indexes** on (strategy_id, symbol, timeframe, timestamp)
- **JSON indexing** for frequently queried indicator values
- **Partitioning** by timestamp for large datasets
- **Connection pooling** for concurrent requests

### Frontend Optimizations
- **Lazy loading** of indicator configurations
- **Debounced updates** for parameter changes
- **Canvas rendering** for high-frequency data
- **Web Workers** for intensive calculations

### Caching Strategy
- **Redis caching** for frequently accessed configurations
- **Browser caching** for default configurations
- **Computed value caching** with invalidation on parameter changes

## 🔌 Extensibility Framework

### Adding New Indicators
1. **Register in defaults table** with configuration schema
2. **Implement calculation function** in indicator engine
3. **Create UI component** for parameter configuration
4. **Add plotting logic** for chart integration

### Plugin Architecture
- **Modular indicator definitions** with standardized interfaces
- **Dynamic loading** of indicator modules
- **Configuration validation** schemas
- **Automatic UI generation** from configuration schemas

## 📊 Data Flow

### Configuration Flow
1. User selects indicators in UI
2. Parameters sent to backend for validation
3. Configuration saved to strategy_indicator_configs
4. Real-time calculation triggered
5. Chart updated with new indicators

### Calculation Flow
1. OHLCV data retrieved for timeframe
2. Indicator configurations loaded for strategy
3. Batch calculation performed
4. Results stored in indicators_data
5. Frontend receives calculated values
6. Chart plotting with styling applied

## 🧪 Testing Strategy

### Unit Tests
- **Indicator calculations** with known datasets
- **Configuration validation** with edge cases
- **Data access layer** with mock databases

### Integration Tests
- **End-to-end indicator workflows**
- **Strategy-indicator associations**
- **Real-time update mechanisms**

### Performance Tests
- **Large dataset calculations**
- **Concurrent user scenarios**
- **Memory usage optimization**

This architecture provides a solid foundation for a professional-grade multi-indicator trading system with excellent scalability and maintainability.
