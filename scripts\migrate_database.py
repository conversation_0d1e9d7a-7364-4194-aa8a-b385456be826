#!/usr/bin/env python3
"""
Database Migration Script
Migrates existing database to new schema with strategy management
"""
import sys
import os
import mysql.connector
from contextlib import contextmanager
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def migrate_database():
    """Migrate database to new schema"""
    print("🔄 Starting database migration...")
    
    try:
        with get_db_cursor() as cursor:
            # Step 1: Create strategies table if it doesn't exist
            print("📋 Creating strategies table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    symbol VARCHAR(20) NOT NULL,
                    timeframe VARCHAR(10) NOT NULL,
                    exchange VARCHAR(20) DEFAULT 'binance',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    INDEX idx_symbol_timeframe (symbol, timeframe),
                    INDEX idx_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✅ Strategies table created/verified")
            
            # Step 2: Check if ohlcv_data table needs migration
            print("🔍 Checking ohlcv_data table structure...")
            cursor.execute("DESCRIBE ohlcv_data")
            columns = [row['Field'] for row in cursor.fetchall()]
            
            needs_migration = False
            
            # Check if strategy_id column exists
            if 'strategy_id' not in columns:
                print("➕ Adding strategy_id column to ohlcv_data...")
                # Add strategy_id column at the beginning
                cursor.execute("ALTER TABLE ohlcv_data ADD COLUMN strategy_id INT FIRST")
                needs_migration = True
            
            # Check if exchange column exists
            if 'exchange' not in columns:
                print("➕ Adding exchange column to ohlcv_data...")
                cursor.execute("ALTER TABLE ohlcv_data ADD COLUMN exchange VARCHAR(20) DEFAULT 'binance' AFTER volume")
                needs_migration = True
            
            # Update column names if needed (open_price -> open, etc.)
            if 'open_price' in columns:
                print("🔄 Renaming price columns...")
                cursor.execute("ALTER TABLE ohlcv_data CHANGE COLUMN open_price `open` DECIMAL(20, 8) NOT NULL")
                cursor.execute("ALTER TABLE ohlcv_data CHANGE COLUMN high_price `high` DECIMAL(20, 8) NOT NULL")
                cursor.execute("ALTER TABLE ohlcv_data CHANGE COLUMN low_price `low` DECIMAL(20, 8) NOT NULL")
                cursor.execute("ALTER TABLE ohlcv_data CHANGE COLUMN close_price `close` DECIMAL(20, 8) NOT NULL")
                needs_migration = True
            
            if needs_migration:
                print("🔗 Adding foreign key constraint...")
                try:
                    cursor.execute("""
                        ALTER TABLE ohlcv_data 
                        ADD CONSTRAINT fk_ohlcv_strategy 
                        FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
                    """)
                except Exception as e:
                    if "Duplicate key name" not in str(e):
                        print(f"⚠️  Warning: Could not add foreign key: {e}")
                
                print("📊 Updating unique constraint...")
                try:
                    cursor.execute("ALTER TABLE ohlcv_data DROP INDEX unique_ohlcv")
                except:
                    pass  # Index might not exist
                
                cursor.execute("""
                    ALTER TABLE ohlcv_data 
                    ADD UNIQUE KEY unique_ohlcv (symbol, timeframe, timestamp, exchange, strategy_id)
                """)
            
            # Step 3: Update indicators_data table
            print("🔍 Checking indicators_data table...")
            try:
                cursor.execute("DESCRIBE indicators_data")
                ind_columns = [row['Field'] for row in cursor.fetchall()]
                
                if 'strategy_id' not in ind_columns:
                    print("➕ Adding strategy_id to indicators_data...")
                    cursor.execute("ALTER TABLE indicators_data ADD COLUMN strategy_id INT FIRST")
                    cursor.execute("""
                        ALTER TABLE indicators_data 
                        ADD CONSTRAINT fk_indicators_strategy 
                        FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
                    """)
                    
                    # Update unique constraint
                    try:
                        cursor.execute("ALTER TABLE indicators_data DROP INDEX unique_indicator")
                    except:
                        pass
                    cursor.execute("""
                        ALTER TABLE indicators_data 
                        ADD UNIQUE KEY unique_indicator (symbol, timeframe, timestamp, indicator_name, strategy_id)
                    """)
                    
            except Exception as e:
                print(f"⚠️  Warning: Could not update indicators_data: {e}")
            
            # Step 4: Update manual_marks table
            print("🔍 Checking manual_marks table...")
            try:
                cursor.execute("DESCRIBE manual_marks")
                marks_columns = [row['Field'] for row in cursor.fetchall()]
                
                if 'strategy_id' not in marks_columns:
                    print("➕ Adding strategy_id to manual_marks...")
                    cursor.execute("ALTER TABLE manual_marks ADD COLUMN strategy_id INT FIRST")
                    cursor.execute("""
                        ALTER TABLE manual_marks 
                        ADD CONSTRAINT fk_marks_strategy 
                        FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
                    """)
                    
            except Exception as e:
                print(f"⚠️  Warning: Could not update manual_marks: {e}")
            
            # Step 5: Update strategy_log table
            print("🔍 Checking strategy_log table...")
            try:
                cursor.execute("DESCRIBE strategy_log")
                log_columns = [row['Field'] for row in cursor.fetchall()]
                
                # Check if strategy_id column exists and is properly named
                if 'strategy_id' not in log_columns:
                    # Check if the old column name exists
                    if any('strategy_id' in col for col in log_columns if 'PRIMARY' in str(col)):
                        print("🔄 Renaming strategy_log primary key...")
                        cursor.execute("ALTER TABLE strategy_log CHANGE COLUMN strategy_id id INT AUTO_INCREMENT PRIMARY KEY")
                        cursor.execute("ALTER TABLE strategy_log ADD COLUMN strategy_id INT NOT NULL AFTER id")
                    else:
                        print("➕ Adding strategy_id to strategy_log...")
                        cursor.execute("ALTER TABLE strategy_log ADD COLUMN strategy_id INT NOT NULL AFTER id")
                    
                    cursor.execute("""
                        ALTER TABLE strategy_log 
                        ADD CONSTRAINT fk_log_strategy 
                        FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
                    """)
                    
            except Exception as e:
                print(f"⚠️  Warning: Could not update strategy_log: {e}")
            
            # Step 6: Create a default strategy for existing data
            print("🎯 Creating default strategy for existing data...")
            cursor.execute("SELECT COUNT(*) as count FROM strategies")
            strategy_count = cursor.fetchone()['count']
            
            if strategy_count == 0:
                cursor.execute("""
                    INSERT INTO strategies (name, description, symbol, timeframe, exchange)
                    VALUES ('Default Strategy', 'Default strategy for existing data', 'BTCUSDT', '15m', 'binance')
                """)
                default_strategy_id = cursor.lastrowid
                print(f"✅ Created default strategy with ID: {default_strategy_id}")
                
                # Update existing OHLCV data to link to default strategy
                cursor.execute("SELECT COUNT(*) as count FROM ohlcv_data WHERE strategy_id IS NULL")
                unlinked_count = cursor.fetchone()['count']
                
                if unlinked_count > 0:
                    cursor.execute("""
                        UPDATE ohlcv_data 
                        SET strategy_id = %s 
                        WHERE strategy_id IS NULL
                    """, (default_strategy_id,))
                    print(f"🔗 Linked {unlinked_count} existing OHLCV records to default strategy")
            
            print("✅ Database migration completed successfully!")
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise

def verify_migration():
    """Verify the migration was successful"""
    print("\n🔍 Verifying migration...")
    
    try:
        with get_db_cursor() as cursor:
            # Check strategies table
            cursor.execute("SELECT COUNT(*) as count FROM strategies")
            strategy_count = cursor.fetchone()['count']
            print(f"📊 Strategies table: {strategy_count} records")
            
            # Check ohlcv_data structure
            cursor.execute("DESCRIBE ohlcv_data")
            ohlcv_columns = [row['Field'] for row in cursor.fetchall()]
            required_columns = ['strategy_id', 'exchange', 'open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in ohlcv_columns]
            
            if missing_columns:
                print(f"❌ Missing columns in ohlcv_data: {missing_columns}")
                return False
            else:
                print("✅ OHLCV table structure verified")
            
            # Check data linking
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM ohlcv_data 
                WHERE strategy_id IS NOT NULL
            """)
            linked_count = cursor.fetchone()['count']
            print(f"🔗 Linked OHLCV records: {linked_count}")
            
            print("✅ Migration verification completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    try:
        migrate_database()
        if verify_migration():
            print("\n🎉 Database migration completed successfully!")
            print("🚀 You can now restart the server and use the enhanced Strategy Builder!")
        else:
            print("\n❌ Migration verification failed. Please check the errors above.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Migration failed: {e}")
        sys.exit(1)
