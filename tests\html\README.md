# HTML Test Files

This directory contains HTML-based test files for frontend components and debugging.

## Files

- `test_coordinates.html` - Test coordinate system and positioning
- `test_macd_fix.html` - MACD indicator fix testing
- `test_tooltip.html` - Tooltip functionality testing
- `test_trade_lines.html` - Trade lines visualization testing
- `test_trade_lines_functionality.html` - Trade lines functionality testing
- `debug_volume_issue.html` - Volume data debugging

## Usage

Open these HTML files directly in a web browser to test frontend functionality.
These files are typically used for:

1. Visual debugging of chart components
2. Testing JavaScript functionality
3. Validating CSS styling
4. Interactive testing of user interfaces

## Running Tests

```bash
# Open in default browser (Windows)
start tests/html/test_coordinates.html

# Or open manually in your preferred browser
```

## Notes

- These files may require a local server to be running for API calls
- Some tests may need specific data to be loaded in the database
- Check console output for JavaScript errors or debugging information
