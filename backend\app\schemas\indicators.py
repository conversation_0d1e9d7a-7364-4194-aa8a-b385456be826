"""
Pydantic schemas for indicator management API
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum

class ChartType(str, Enum):
    """Chart type enumeration"""
    OVERLAY = "overlay"
    SUBCHART = "subchart"

class IndicatorDefaultsResponse(BaseModel):
    """Response schema for indicator defaults"""
    indicator_name: str
    display_name: str
    description: Optional[str] = None
    default_config: Dict[str, Any]
    chart_type: ChartType
    supports_multiple: bool
    parameter_schema: Optional[Dict[str, Any]] = None

class IndicatorConfigRequest(BaseModel):
    """Request schema for saving indicator configuration"""
    indicator_name: str = Field(..., description="Name of the indicator")
    config: Dict[str, Any] = Field(..., description="Indicator configuration parameters")
    is_enabled: bool = Field(True, description="Whether the indicator is enabled")
    display_order: int = Field(0, description="Display order in UI")

    @validator('indicator_name')
    def validate_indicator_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Indicator name cannot be empty')
        return v.upper().strip()

class IndicatorConfigResponse(BaseModel):
    """Response schema for indicator configuration"""
    indicator_name: str
    config: Dict[str, Any]
    is_enabled: bool
    display_order: int
    display_name: Optional[str] = None
    chart_type: Optional[ChartType] = None
    supports_multiple: Optional[bool] = None

class BulkIndicatorConfigRequest(BaseModel):
    """Request schema for bulk saving indicator configurations"""
    indicators: Dict[str, Dict[str, Any]] = Field(
        ..., 
        description="Dictionary of indicator configurations"
    )

    @validator('indicators')
    def validate_indicators(cls, v):
        if not v:
            raise ValueError('At least one indicator configuration is required')
        return v

class IndicatorCalculationRequest(BaseModel):
    """Request schema for indicator calculation"""
    symbol: str = Field(..., description="Trading symbol")
    timeframe: str = Field(..., description="Timeframe")
    ohlcv_data: List[Dict[str, Any]] = Field(..., description="OHLCV data for calculation")
    indicators_config: Optional[Dict[str, Dict[str, Any]]] = Field(
        None, 
        description="Indicator configurations"
    )
    strategy_id: Optional[int] = Field(None, description="Strategy ID for configuration lookup")

    @validator('ohlcv_data')
    def validate_ohlcv_data(cls, v):
        if not v:
            raise ValueError('OHLCV data is required')
        
        required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        for i, candle in enumerate(v):
            for field in required_fields:
                if field not in candle:
                    raise ValueError(f'Missing {field} in OHLCV data at index {i}')
        
        return v

class IndicatorCalculationResponse(BaseModel):
    """Response schema for indicator calculation"""
    success: bool
    data: Dict[str, Any]
    symbol: str
    timeframe: str
    strategy_id: Optional[int] = None
    data_points: int

class IndicatorDataRequest(BaseModel):
    """Request schema for getting indicator data"""
    strategy_id: int
    symbol: str
    timeframe: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    indicator_names: Optional[List[str]] = None

class IndicatorDataResponse(BaseModel):
    """Response schema for indicator data"""
    success: bool
    data: List[Dict[str, Any]]
    strategy_id: int
    symbol: str
    timeframe: str
    count: int

class IndicatorSnapshotRequest(BaseModel):
    """Request schema for indicator snapshot"""
    symbol: str
    timeframe: str
    timestamp: str
    ohlcv_data: List[Dict[str, Any]]
    indicators_config: Optional[Dict[str, Dict[str, Any]]] = None
    strategy_id: Optional[int] = None

class IndicatorSnapshotResponse(BaseModel):
    """Response schema for indicator snapshot"""
    success: bool
    data: Dict[str, Any]

class IndicatorValidationRequest(BaseModel):
    """Request schema for indicator validation"""
    indicator_name: str
    config: Dict[str, Any]

class IndicatorValidationResponse(BaseModel):
    """Response schema for indicator validation"""
    success: bool
    valid: bool
    indicator_name: str
    message: str

class SupportedIndicatorInfo(BaseModel):
    """Information about a supported indicator"""
    name: str
    display_name: str
    description: str
    chart_type: ChartType
    supports_multiple: bool

class SupportedIndicatorsResponse(BaseModel):
    """Response schema for supported indicators"""
    success: bool
    data: List[SupportedIndicatorInfo]
    count: int

class IndicatorConfigUpdate(BaseModel):
    """Schema for updating indicator configuration"""
    config: Optional[Dict[str, Any]] = None
    is_enabled: Optional[bool] = None
    display_order: Optional[int] = None

class EMAConfig(BaseModel):
    """EMA-specific configuration schema"""
    periods: List[int] = Field(default=[20, 50, 100], description="EMA periods")
    colors: List[str] = Field(default=["#FF6B6B", "#4ECDC4", "#45B7D1"], description="Line colors")
    lineWidth: int = Field(default=2, ge=1, le=5, description="Line width")

    @validator('periods')
    def validate_periods(cls, v):
        if not v:
            raise ValueError('At least one period is required')
        for period in v:
            if period < 1 or period > 500:
                raise ValueError('Period must be between 1 and 500')
        return v

    @validator('colors')
    def validate_colors(cls, v, values):
        periods = values.get('periods', [])
        if len(v) != len(periods):
            raise ValueError('Number of colors must match number of periods')
        return v

class RSIConfig(BaseModel):
    """RSI-specific configuration schema"""
    periods: List[int] = Field(default=[14], description="RSI periods")
    colors: List[str] = Field(default=["#2196F3"], description="Line colors")
    overbought: float = Field(default=70, ge=50, le=100, description="Overbought level")
    oversold: float = Field(default=30, ge=0, le=50, description="Oversold level")
    lineWidth: int = Field(default=2, ge=1, le=5, description="Line width")

    @validator('periods')
    def validate_periods(cls, v):
        if not v:
            raise ValueError('At least one period is required')
        for period in v:
            if period < 2 or period > 100:
                raise ValueError('RSI period must be between 2 and 100')
        return v

class MACDConfig(BaseModel):
    """MACD-specific configuration schema"""
    fast: int = Field(default=12, ge=1, le=50, description="Fast EMA period")
    slow: int = Field(default=26, ge=1, le=100, description="Slow EMA period")
    signal: int = Field(default=9, ge=1, le=50, description="Signal line period")
    colors: Dict[str, str] = Field(
        default={
            "macd": "#2196F3",
            "signal": "#FF9800",
            "histogram": "#4CAF50"
        },
        description="Colors for MACD components"
    )
    lineWidth: int = Field(default=2, ge=1, le=5, description="Line width")

    @validator('slow')
    def validate_slow_greater_than_fast(cls, v, values):
        fast = values.get('fast')
        if fast and v <= fast:
            raise ValueError('Slow period must be greater than fast period')
        return v

class BollingerBandsConfig(BaseModel):
    """Bollinger Bands-specific configuration schema"""
    period: int = Field(default=20, ge=5, le=100, description="Moving average period")
    stdDev: float = Field(default=2.0, ge=0.5, le=5.0, description="Standard deviation multiplier")
    colors: Dict[str, str] = Field(
        default={
            "upper": "#FF5722",
            "middle": "#607D8B",
            "lower": "#FF5722"
        },
        description="Colors for Bollinger Bands"
    )
    fillOpacity: float = Field(default=0.1, ge=0.0, le=1.0, description="Fill opacity")
    lineWidth: int = Field(default=1, ge=1, le=5, description="Line width")

class StochasticConfig(BaseModel):
    """Stochastic-specific configuration schema"""
    kPeriod: int = Field(default=14, ge=1, le=50, description="%K period")
    dPeriod: int = Field(default=3, ge=1, le=20, description="%D period")
    colors: Dict[str, str] = Field(
        default={
            "k": "#E91E63",
            "d": "#9C27B0"
        },
        description="Colors for %K and %D lines"
    )
    overbought: float = Field(default=80, ge=50, le=100, description="Overbought level")
    oversold: float = Field(default=20, ge=0, le=50, description="Oversold level")
    lineWidth: int = Field(default=2, ge=1, le=5, description="Line width")

class GenericIndicatorConfig(BaseModel):
    """Generic indicator configuration schema"""
    period: int = Field(default=20, ge=1, le=200, description="Period")
    color: str = Field(default="#2196F3", description="Line color")
    lineWidth: int = Field(default=2, ge=1, le=5, description="Line width")

# Union type for all indicator configurations
IndicatorConfigUnion = Union[
    EMAConfig,
    RSIConfig,
    MACDConfig,
    BollingerBandsConfig,
    StochasticConfig,
    GenericIndicatorConfig
]
