<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Issue Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e1e1e;
            color: #ffffff;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #444;
            border-radius: 5px;
            background-color: #2d2d2d;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0052a3;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #1a4d1a;
            border: 1px solid #2d7d2d;
        }
        .error {
            background-color: #4d1a1a;
            border: 1px solid #7d2d2d;
        }
        .warning {
            background-color: #4d4d1a;
            border: 1px solid #7d7d2d;
        }
    </style>
</head>
<body>
    <h1>Volume Issue Debug Tool</h1>
    <p>This tool helps debug why volume is showing as 0 during entry/exit marking.</p>
    
    <div class="debug-section">
        <h2>1. Check Chart Data</h2>
        <button onclick="checkChartData()">Check Chart Data</button>
        <div id="chart-data-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Test OHLCV Extraction</h2>
        <button onclick="testOHLCVExtraction()">Test OHLCV Extraction</button>
        <div id="ohlcv-extraction-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. Test Candlestick Data</h2>
        <button onclick="testCandlestickData()">Test getCandlestickAtTime</button>
        <div id="candlestick-data-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>4. Test Backend OHLCV</h2>
        <button onclick="testBackendOHLCV()">Test Backend OHLCV</button>
        <div id="backend-ohlcv-result"></div>
    </div>

    <script>
        function checkChartData() {
            const resultDiv = document.getElementById('chart-data-result');
            
            try {
                // Check if chart instances exist
                const charts = [];
                if (window.professionalChart) charts.push({ name: 'professionalChart', chart: window.professionalChart });
                if (window.tradingViewChart) charts.push({ name: 'tradingViewChart', chart: window.tradingViewChart });
                if (window.chart) charts.push({ name: 'chart', chart: window.chart });
                
                if (charts.length === 0) {
                    resultDiv.innerHTML = '<div class="result error">❌ No chart instances found</div>';
                    return;
                }
                
                let output = '<div class="result success">✅ Found chart instances:</div>';
                
                charts.forEach(({ name, chart }) => {
                    output += `<h3>${name}</h3>`;
                    output += `<p><strong>Has currentData:</strong> ${!!chart.currentData}</p>`;
                    
                    if (chart.currentData && Array.isArray(chart.currentData)) {
                        const dataLength = chart.currentData.length;
                        output += `<p><strong>Data length:</strong> ${dataLength}</p>`;
                        
                        if (dataLength > 0) {
                            const sample = chart.currentData.slice(-3);
                            output += `<p><strong>Sample data (last 3):</strong></p>`;
                            output += `<pre>${JSON.stringify(sample, null, 2)}</pre>`;
                            
                            // Check volume specifically
                            const volumeStats = {
                                total: dataLength,
                                withVolume: chart.currentData.filter(c => c.volume && c.volume > 0).length,
                                volumeValues: chart.currentData.slice(-5).map(c => c.volume)
                            };
                            
                            output += `<p><strong>Volume stats:</strong></p>`;
                            output += `<pre>${JSON.stringify(volumeStats, null, 2)}</pre>`;
                            
                            if (volumeStats.withVolume === 0) {
                                output += '<div class="result error">❌ No candles have volume > 0!</div>';
                            } else {
                                output += `<div class="result success">✅ ${volumeStats.withVolume}/${volumeStats.total} candles have volume</div>`;
                            }
                        }
                    }
                });
                
                resultDiv.innerHTML = output;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function testOHLCVExtraction() {
            const resultDiv = document.getElementById('ohlcv-extraction-result');
            
            if (!window.markingTools) {
                resultDiv.innerHTML = '<div class="result error">❌ Marking tools not available</div>';
                return;
            }
            
            const testTimestamp = Math.floor(Date.now() / 1000);
            
            window.markingTools.getOHLCVData(testTimestamp).then(result => {
                if (result) {
                    let output = '<div class="result success">✅ OHLCV extraction successful</div>';
                    output += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    
                    if (result.volume === 0) {
                        output += '<div class="result warning">⚠️ Volume is 0 in extracted data</div>';
                    } else {
                        output += `<div class="result success">✅ Volume: ${result.volume}</div>`;
                    }
                    
                    resultDiv.innerHTML = output;
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ No OHLCV data returned</div>';
                }
            }).catch(error => {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }
        
        function testCandlestickData() {
            const resultDiv = document.getElementById('candlestick-data-result');
            
            if (!window.markingTools) {
                resultDiv.innerHTML = '<div class="result error">❌ Marking tools not available</div>';
                return;
            }
            
            const testTimestamp = Math.floor(Date.now() / 1000);
            
            try {
                const result = window.markingTools.getCandlestickAtTime(testTimestamp);
                
                if (result) {
                    let output = '<div class="result success">✅ Candlestick data found</div>';
                    output += `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                    
                    if (result.volume === 0) {
                        output += '<div class="result warning">⚠️ Volume is 0 in candlestick data</div>';
                    } else {
                        output += `<div class="result success">✅ Volume: ${result.volume}</div>`;
                    }
                    
                    resultDiv.innerHTML = output;
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ No candlestick data returned</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testBackendOHLCV() {
            const resultDiv = document.getElementById('backend-ohlcv-result');
            
            try {
                const response = await fetch('/api/v1/ohlcv/data?symbol=BTCUSDT&timeframe=15m&limit=5');
                
                if (response.ok) {
                    const data = await response.json();
                    
                    let output = '<div class="result success">✅ Backend OHLCV data retrieved</div>';
                    output += `<p><strong>Success:</strong> ${data.success}</p>`;
                    output += `<p><strong>Data count:</strong> ${data.data?.length || 0}</p>`;
                    
                    if (data.data && data.data.length > 0) {
                        const sample = data.data.slice(-3);
                        output += `<p><strong>Sample data (last 3):</strong></p>`;
                        output += `<pre>${JSON.stringify(sample, null, 2)}</pre>`;
                        
                        // Check volume in backend data
                        const volumeStats = {
                            total: data.data.length,
                            withVolume: data.data.filter(c => c.volume && c.volume > 0).length,
                            volumeValues: data.data.slice(-5).map(c => c.volume)
                        };
                        
                        output += `<p><strong>Backend volume stats:</strong></p>`;
                        output += `<pre>${JSON.stringify(volumeStats, null, 2)}</pre>`;
                        
                        if (volumeStats.withVolume === 0) {
                            output += '<div class="result error">❌ Backend data has no volume!</div>';
                        } else {
                            output += `<div class="result success">✅ Backend has ${volumeStats.withVolume}/${volumeStats.total} candles with volume</div>`;
                        }
                    }
                    
                    resultDiv.innerHTML = output;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Backend request failed: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }
        
        // Auto-run chart data check on load
        window.addEventListener('load', () => {
            setTimeout(checkChartData, 1000);
        });
    </script>
</body>
</html>
