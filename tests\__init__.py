"""
Strategy Builder Test Suite

This package contains all tests for the Strategy Builder application.

Test Structure:
- unit/: Unit tests for individual components
- integration/: Integration tests for API endpoints and workflows
- performance/: Performance and load tests
- html/: HTML-based test files for frontend components
- fixtures/: Test data and shared fixtures

Usage:
    # Run all tests
    python -m pytest tests/

    # Run specific test types
    python -m pytest tests/unit/
    python -m pytest tests/integration/
    python -m pytest tests/performance/

    # Run with coverage
    python -m pytest tests/ --cov=backend/app
"""
