<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exit Mark Functionality Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .status {
            margin-left: auto;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status.ready {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status.testing {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.passed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .results {
            padding: 20px;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            border-radius: 5px;
        }
        
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .test-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-info li {
            margin: 5px 0;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Exit Mark Functionality Test</h1>
            <p>Test suite for exit mark real data extraction and entry selection fixes</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="initializeTest()">Initialize Test Environment</button>
            <button class="btn btn-success" onclick="runExitMarkTest()" id="testBtn" disabled>Run Exit Mark Test</button>
            <button class="btn btn-secondary" onclick="clearConsole()">Clear Console</button>
            <button class="btn btn-secondary" onclick="debugMarks()">Debug Marks</button>
            <div class="status ready" id="status">Ready</div>
        </div>
        
        <div class="results">
            <div class="test-info">
                <h3>🎯 Test Objectives</h3>
                <p>This test verifies the fixes for two critical exit mark issues:</p>
                <ul>
                    <li><strong>Real Data Extraction:</strong> Ensures exit marks capture actual OHLCV and indicator data instead of dummy/placeholder data</li>
                    <li><strong>Entry Selection:</strong> Verifies that users can properly select and link to existing open entry marks when creating exits</li>
                </ul>
                
                <h3>📋 Test Steps</h3>
                <ol>
                    <li>Initialize the marking tools environment</li>
                    <li>Create a test entry mark with proper data structure</li>
                    <li>Test entry selection dropdown population</li>
                    <li>Test OHLCV and indicator data extraction</li>
                    <li>Verify exit mark creation logic</li>
                    <li>Validate end-to-end functionality</li>
                </ol>
            </div>
            
            <div class="console-output" id="console">
                <div class="loading">
                    <p>Click "Initialize Test Environment" to begin testing...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the marking tools script -->
    <script src="js/marking-tools.js"></script>
    
    <script>
        let consoleOutput = document.getElementById('console');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;
        
        // Capture console output
        function captureConsole() {
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                appendToConsole('LOG', args.join(' '));
            };
            
            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                appendToConsole('ERROR', args.join(' '));
            };
            
            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                appendToConsole('WARN', args.join(' '));
            };
        }
        
        function appendToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'ERROR' ? '#ff6b6b' : type === 'WARN' ? '#feca57' : '#d4d4d4';
            consoleOutput.innerHTML += `<span style="color: #888">[${timestamp}]</span> <span style="color: ${color}">[${type}]</span> ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        function updateStatus(text, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = text;
            statusEl.className = `status ${className}`;
        }
        
        async function initializeTest() {
            updateStatus('Initializing...', 'testing');
            clearConsole();
            captureConsole();
            
            console.log('🚀 Initializing Exit Mark Test Environment');
            console.log('=' * 50);
            
            try {
                // Initialize marking tools if not already done
                if (!window.markingTools) {
                    console.log('📝 Creating MarkingTools instance...');
                    
                    // Create a mock chart object
                    const mockChart = {
                        candlestickSeries: {
                            setMarkers: function(markers) {
                                console.log('📊 Chart markers set:', markers.length);
                            }
                        }
                    };
                    
                    window.markingTools = new MarkingTools(mockChart);
                    console.log('✅ MarkingTools initialized');
                } else {
                    console.log('✅ MarkingTools already initialized');
                }
                
                // Enable test button
                document.getElementById('testBtn').disabled = false;
                updateStatus('Ready to Test', 'ready');
                console.log('🎯 Test environment ready! Click "Run Exit Mark Test" to begin.');
                
            } catch (error) {
                console.error('❌ Failed to initialize test environment:', error);
                updateStatus('Initialization Failed', 'failed');
            }
        }
        
        async function runExitMarkTest() {
            updateStatus('Testing...', 'testing');
            
            try {
                console.log('\n🧪 Starting Exit Mark Functionality Test');
                
                if (typeof window.testExitMarkCreation === 'function') {
                    const result = await window.testExitMarkCreation();
                    
                    if (result) {
                        updateStatus('Tests Passed', 'passed');
                        console.log('\n🎉 All exit mark tests passed successfully!');
                    } else {
                        updateStatus('Tests Partial', 'failed');
                        console.log('\n⚠️ Some tests passed, but issues were found. Check the log above.');
                    }
                } else {
                    console.error('❌ Test function not available. Make sure marking-tools.js is loaded.');
                    updateStatus('Test Function Missing', 'failed');
                }
                
            } catch (error) {
                console.error('❌ Test execution failed:', error);
                updateStatus('Test Failed', 'failed');
            }
        }
        
        function debugMarks() {
            console.log('\n🔍 Debug: Current Marks State');
            
            if (window.markingTools) {
                console.log('📊 Total marks:', window.markingTools.marks.size);
                
                if (window.markingTools.marks.size > 0) {
                    console.log('📋 Mark details:');
                    window.markingTools.marks.forEach((mark, id) => {
                        console.log(`   - ID: ${id} (${typeof id})`);
                        console.log(`     Type: ${mark.mark_type || 'unknown'}`);
                        console.log(`     Side: ${mark.entry_side || mark.side || 'unknown'}`);
                        console.log(`     Price: ${mark.price || mark.entry_price || 'unknown'}`);
                        console.log(`     Status: ${mark.status || 'unknown'}`);
                    });
                } else {
                    console.log('📭 No marks found');
                }
            } else {
                console.log('❌ MarkingTools not initialized');
            }
        }
        
        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🌟 Exit Mark Test Page Loaded');
                console.log('Click "Initialize Test Environment" to begin testing the exit mark fixes.');
            }, 500);
        });
    </script>
</body>
</html>
