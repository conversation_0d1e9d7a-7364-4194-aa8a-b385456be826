#!/usr/bin/env python3
"""
Run the enhanced multi-indicator system database migration
"""

import sys
import os
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.core.database import get_db_cursor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """Run the enhanced indicators schema migration"""
    
    migration_file = Path(__file__).parent.parent / "database" / "migrations" / "002_enhance_indicators_schema_fixed.sql"
    
    if not migration_file.exists():
        logger.error(f"Migration file not found: {migration_file}")
        return False
    
    try:
        # Read the migration SQL
        with open(migration_file, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # Split into individual statements
        statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
        
        logger.info(f"Running migration with {len(statements)} statements...")
        
        with get_db_cursor() as cursor:
            for i, statement in enumerate(statements, 1):
                try:
                    logger.info(f"Executing statement {i}/{len(statements)}")
                    cursor.execute(statement)
                    
                    # Log results for SELECT statements
                    if statement.upper().strip().startswith('SELECT'):
                        result = cursor.fetchone()
                        if result:
                            logger.info(f"Result: {result[0]}")
                    
                except Exception as e:
                    logger.warning(f"Statement {i} failed (may be expected): {e}")
                    continue
        
        logger.info("✅ Migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify that the migration was successful"""
    
    try:
        with get_db_cursor() as cursor:
            # Check if new tables exist
            tables_to_check = [
                'indicator_defaults',
                'strategy_indicator_configs'
            ]
            
            for table in tables_to_check:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                result = cursor.fetchone()
                if result:
                    logger.info(f"✅ Table '{table}' exists")
                    
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    result = cursor.fetchone()
                    count = result[0] if result else 0
                    logger.info(f"   - {count} rows")
                else:
                    logger.error(f"❌ Table '{table}' not found")
                    return False
            
            # Check if indicators_data table has new columns
            cursor.execute("DESCRIBE indicators_data")
            columns = [row[0] for row in cursor.fetchall()]
            
            required_columns = ['indicator_config', 'indicator_values', 'updated_at', 'strategy_id']
            for col in required_columns:
                if col in columns:
                    logger.info(f"✅ Column 'indicators_data.{col}' exists")
                else:
                    logger.error(f"❌ Column 'indicators_data.{col}' not found")
                    return False
            
            # Check if default indicators were inserted
            cursor.execute("SELECT COUNT(*) FROM indicator_defaults")
            result = cursor.fetchone()
            defaults_count = result[0] if result else 0
            logger.info(f"✅ {defaults_count} default indicators configured")
            
            if defaults_count == 0:
                logger.warning("⚠️  No default indicators found - this may be expected")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Migration verification failed: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Starting enhanced multi-indicator system migration...")
    
    # Run migration
    if not run_migration():
        logger.error("Migration failed!")
        sys.exit(1)
    
    # Verify migration
    if not verify_migration():
        logger.error("Migration verification failed!")
        sys.exit(1)
    
    logger.info("🎉 Enhanced multi-indicator system migration completed successfully!")
    logger.info("")
    logger.info("Next steps:")
    logger.info("1. Test the new indicator API endpoints")
    logger.info("2. Update the frontend to use the new configuration system")
    logger.info("3. Test indicator calculations with the new engine")

if __name__ == "__main__":
    main()
