"""
Database model for manual marks/trades
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Enum
from app.core.database import Base
from datetime import datetime
import enum


class SideEnum(enum.Enum):
    buy = "buy"
    sell = "sell"


class StatusEnum(enum.Enum):
    open = "open"
    partial = "partial"
    closed = "closed"


class ManualMark(Base):
    __tablename__ = "manual_marks"

    id = Column(Integer, primary_key=True, index=True)
    
    # Entry data
    entry_timestamp = Column(DateTime, nullable=False, index=True)
    entry_price = Column(Float, nullable=False)
    side = Column(Enum(SideEnum), nullable=False)
    quantity = Column(Float, nullable=False)
    notes = Column(Text, nullable=True)
    
    # Exit data (optional)
    exit_timestamp = Column(DateTime, nullable=True, index=True)
    exit_price = Column(Float, nullable=True)
    exit_quantity = Column(Float, nullable=True)
    exit_notes = Column(Text, nullable=True)
    
    # P&L calculation
    pnl = Column(Float, nullable=True)
    
    # Status
    status = Column(Enum(StatusEnum), default=StatusEnum.open, nullable=False)
    
    # OHLCV data at entry and exit
    entry_ohlcv_data = Column(Text, nullable=True)  # JSON string
    exit_ohlcv_data = Column(Text, nullable=True)   # JSON string
    
    # Indicator data at entry and exit
    entry_indicator_data = Column(Text, nullable=True)  # JSON string
    exit_indicator_data = Column(Text, nullable=True)   # JSON string
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<ManualMark(id={self.id}, side={self.side}, entry_price={self.entry_price}, status={self.status})>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'entry_timestamp': self.entry_timestamp.isoformat() if self.entry_timestamp else None,
            'entry_price': float(self.entry_price) if self.entry_price else None,
            'side': self.side.value if self.side else None,
            'quantity': float(self.quantity) if self.quantity else None,
            'notes': self.notes,
            'exit_timestamp': self.exit_timestamp.isoformat() if self.exit_timestamp else None,
            'exit_price': float(self.exit_price) if self.exit_price else None,
            'exit_quantity': float(self.exit_quantity) if self.exit_quantity else None,
            'exit_notes': self.exit_notes,
            'pnl': float(self.pnl) if self.pnl else None,
            'status': self.status.value if self.status else None,
            'entry_ohlcv_data': self.entry_ohlcv_data,
            'exit_ohlcv_data': self.exit_ohlcv_data,
            'entry_indicator_data': self.entry_indicator_data,
            'exit_indicator_data': self.exit_indicator_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
