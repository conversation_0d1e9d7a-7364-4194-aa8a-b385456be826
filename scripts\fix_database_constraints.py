#!/usr/bin/env python3
"""
Database constraint fix script
Safely handles foreign key constraints when recreating tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db_cursor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_database_constraints():
    """Fix database foreign key constraints by recreating tables in correct order"""
    
    try:
        with get_db_cursor(dict_cursor=False) as cursor:
            logger.info("🔧 Starting database constraint fix...")
            
            # Step 1: Disable foreign key checks temporarily
            logger.info("📋 Disabling foreign key checks...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            
            # Step 2: Drop tables with foreign key dependencies first
            logger.info("🗑️  Dropping tables with foreign key constraints...")
            tables_to_drop = [
                "strategy_log",
                "manual_marks"
            ]
            
            for table in tables_to_drop:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS {table}")
                    logger.info(f"✅ Dropped table: {table}")
                except Exception as e:
                    logger.warning(f"⚠️  Could not drop table {table}: {e}")
            
            # Step 3: Re-enable foreign key checks
            logger.info("📋 Re-enabling foreign key checks...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            
            # Step 4: Create manual_marks table with new schema
            logger.info("🏗️  Creating manual_marks table with new schema...")
            manual_marks_sql = """
            CREATE TABLE manual_marks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL DEFAULT 'BTCUSDT',
                timeframe VARCHAR(10) NOT NULL DEFAULT '15m',
                mark_type ENUM('ENTRY', 'EXIT') NOT NULL,
                entry_side ENUM('BUY', 'SELL') NULL,
                timestamp DATETIME NOT NULL,
                price DECIMAL(20, 8) NOT NULL,
                indicator_snapshot JSON,
                ohlcv_snapshot JSON,
                linked_trade_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timestamp (timestamp),
                INDEX idx_mark_type (mark_type),
                INDEX idx_entry_side (entry_side),
                INDEX idx_linked_trade (linked_trade_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            cursor.execute(manual_marks_sql)
            logger.info("✅ Created manual_marks table")
            
            # Step 5: Create strategy_log table with proper foreign keys
            logger.info("🏗️  Creating strategy_log table...")
            strategy_log_sql = """
            CREATE TABLE IF NOT EXISTS strategy_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                strategy_id INT NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                mark_id INT NOT NULL,
                entry_side ENUM('BUY', 'SELL') NOT NULL,
                profit_pct DECIMAL(10, 4) NOT NULL,
                entry_ohlcv JSON,
                exit_ohlcv JSON,
                entry_indicator_snapshot JSON,
                exit_indicator_snapshot JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_profit (profit_pct),
                INDEX idx_strategy (strategy_id),
                FOREIGN KEY (mark_id) REFERENCES manual_marks(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            cursor.execute(strategy_log_sql)
            logger.info("✅ Created strategy_log table")
            
            # Step 6: Verify table structure
            logger.info("🔍 Verifying table structures...")
            
            # Check manual_marks
            cursor.execute("DESCRIBE manual_marks")
            marks_columns = cursor.fetchall()
            logger.info(f"✅ manual_marks table has {len(marks_columns)} columns")
            
            # Check strategy_log
            cursor.execute("DESCRIBE strategy_log")
            log_columns = cursor.fetchall()
            logger.info(f"✅ strategy_log table has {len(log_columns)} columns")
            
            # Step 7: Check foreign key constraints
            cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME = 'manual_marks'
            """)
            
            fk_constraints = cursor.fetchall()
            logger.info(f"✅ Found {len(fk_constraints)} foreign key constraints referencing manual_marks")
            
            for constraint in fk_constraints:
                logger.info(f"   - {constraint[0]}.{constraint[1]} -> {constraint[3]}.{constraint[4]}")
            
            logger.info("🎉 Database constraint fix completed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Database constraint fix failed: {e}")
        return False

def verify_database_health():
    """Verify database health after fix"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            logger.info("🏥 Running database health check...")
            
            # Check if tables exist
            cursor.execute("SHOW TABLES")
            tables = [row['Tables_in_strategy_builder'] for row in cursor.fetchall()]
            
            required_tables = ['manual_marks', 'strategy_log', 'strategies']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                logger.error(f"❌ Missing tables: {missing_tables}")
                return False
            
            logger.info(f"✅ All required tables present: {required_tables}")
            
            # Test insert into manual_marks
            test_insert_sql = """
                INSERT INTO manual_marks (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    indicator_snapshot, ohlcv_snapshot, linked_trade_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(test_insert_sql, (
                'BTCUSDT', '15m', 'ENTRY', 'BUY', '2023-01-01 12:00:00', 45000.50,
                '{}', '{}', None
            ))
            
            test_id = cursor.lastrowid
            logger.info(f"✅ Test insert successful, ID: {test_id}")
            
            # Clean up test data
            cursor.execute("DELETE FROM manual_marks WHERE id = %s", (test_id,))
            logger.info("✅ Test data cleaned up")
            
            logger.info("🎉 Database health check passed!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Database health check failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Database Constraint Fix Tool")
    print("=" * 40)
    
    # Step 1: Fix constraints
    if not fix_database_constraints():
        print("❌ Failed to fix database constraints")
        sys.exit(1)
    
    # Step 2: Verify health
    if not verify_database_health():
        print("❌ Database health check failed")
        sys.exit(1)
    
    print("\n🎉 Database constraint fix completed successfully!")
    print("You can now start the Strategy Builder server.")

if __name__ == "__main__":
    main()
