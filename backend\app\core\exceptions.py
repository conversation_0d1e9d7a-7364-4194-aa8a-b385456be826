"""
Custom exceptions for Strategy Builder
"""
from typing import Optional, Dict, Any


class StrategyBuilderError(Exception):
    """Base exception for Strategy Builder"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(StrategyBuilderError):
    """Validation error"""
    pass


class DatabaseError(StrategyBuilderError):
    """Database operation error"""
    pass


class ExchangeError(StrategyBuilderError):
    """Exchange API error"""
    
    def __init__(self, message: str, exchange: str, status_code: Optional[int] = None, 
                 response: Optional[Dict[str, Any]] = None):
        self.exchange = exchange
        self.status_code = status_code
        self.response = response
        details = {
            "exchange": exchange,
            "status_code": status_code,
            "response": response
        }
        super().__init__(message, details)


class RateLimitError(ExchangeError):
    """Rate limit exceeded error"""
    
    def __init__(self, exchange: str, retry_after: Optional[int] = None):
        self.retry_after = retry_after
        message = f"Rate limit exceeded for {exchange}"
        if retry_after:
            message += f". Retry after {retry_after} seconds"
        super().__init__(message, exchange)


class AuthenticationError(ExchangeError):
    """Authentication error"""
    pass


class InsufficientDataError(StrategyBuilderError):
    """Insufficient data error"""
    pass


class IndicatorCalculationError(StrategyBuilderError):
    """Indicator calculation error"""
    pass


class TradeMarkError(StrategyBuilderError):
    """Trade marking error"""
    pass
