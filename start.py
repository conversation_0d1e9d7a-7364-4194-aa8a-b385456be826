#!/usr/bin/env python3
"""
Strategy Builder - Quick Start Script
"""
import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found. Please create it with your configuration.")
        return False
    
    print("✅ .env file found")
    
    # Check if requirements.txt exists
    req_file = Path("requirements.txt")
    if not req_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    print("✅ requirements.txt found")
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print(f"Error output: {e.stderr}")
        return False

def setup_database():
    """Setup database"""
    print("🗄️ Setting up database...")
    
    try:
        result = subprocess.run([sys.executable, "scripts/setup_database.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database setup completed")
            return True
        else:
            print(f"❌ Database setup failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting Strategy Builder server...")
    
    try:
        # Run the server script
        subprocess.run([sys.executable, "scripts/run_server.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

def main():
    """Main startup function"""
    print("🎯 Strategy Builder - Quick Start")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Ask user if they want to install dependencies
    install_deps = input("\n📦 Install/update dependencies? (y/n): ").lower().strip()
    if install_deps in ['y', 'yes', '']:
        if not install_dependencies():
            print("\n❌ Failed to install dependencies.")
            sys.exit(1)
    
    # Ask user if they want to setup database
    setup_db = input("\n🗄️ Setup/update database? (y/n): ").lower().strip()
    if setup_db in ['y', 'yes', '']:
        if not setup_database():
            print("\n❌ Database setup failed. Please check your MySQL connection and .env file.")
            sys.exit(1)
    
    print("\n" + "=" * 40)
    print("🎉 Setup completed successfully!")
    print("🌐 Starting web server...")
    print("📊 Access the application at: http://localhost:8000")
    print("📚 API documentation at: http://localhost:8000/api/docs")
    print("⌨️  Press Ctrl+C to stop the server")
    print("=" * 40)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
