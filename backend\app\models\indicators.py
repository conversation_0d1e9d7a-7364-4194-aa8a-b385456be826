"""
Technical Indicators Data Model
"""
from sqlalchemy import Column, String, DateTime, JSON, Index
from sqlalchemy.sql import func
from app.core.database import Base

class IndicatorsData(Base):
    """Technical indicators data model"""
    __tablename__ = "indicators_data"
    
    symbol = Column(String(20), primary_key=True, nullable=False)
    timeframe = Column(String(10), primary_key=True, nullable=False)
    timestamp = Column(DateTime, primary_key=True, nullable=False)
    indicator_name = Column(String(50), primary_key=True, nullable=False)
    value = Column(JSON, nullable=False)  # Store indicator values as JSON
    created_at = Column(DateTime, default=func.now())
    
    # Indexes for better query performance
    __table_args__ = (
        Index('idx_symbol_timeframe_indicator', 'symbol', 'timeframe', 'indicator_name'),
        Index('idx_timestamp_indicator', 'timestamp', 'indicator_name'),
    )
    
    def __repr__(self):
        return f"<IndicatorsData(symbol='{self.symbol}', timeframe='{self.timeframe}', indicator='{self.indicator_name}', timestamp='{self.timestamp}')>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'indicator_name': self.indicator_name,
            'value': self.value
        }
