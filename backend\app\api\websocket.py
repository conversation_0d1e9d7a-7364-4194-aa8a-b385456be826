"""
WebSocket API for Real-time Trading Data
Provides live price feeds, candlestick updates, and market data
"""
import asyncio
import json
import logging
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from fastapi.websockets import WebSocketState

from app.services.binance_websocket import BinanceWebSocketClient, KlineData, TickerData

logger = logging.getLogger(__name__)

router = APIRouter()

# Global connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.binance_client = BinanceWebSocketClient()
        self.active_streams: Set[str] = set()
        
    async def connect(self, websocket: WebSocket, stream_type: str):
        """Connect a WebSocket client"""
        await websocket.accept()
        
        if stream_type not in self.active_connections:
            self.active_connections[stream_type] = set()
        self.active_connections[stream_type].add(websocket)
        
        logger.info(f"Client connected to {stream_type} stream. Total connections: {len(self.active_connections[stream_type])}")
        
    async def disconnect(self, websocket: WebSocket, stream_type: str):
        """Disconnect a WebSocket client"""
        if stream_type in self.active_connections:
            self.active_connections[stream_type].discard(websocket)
            
            # If no more connections for this stream, stop the Binance stream
            if not self.active_connections[stream_type]:
                await self.binance_client.disconnect_stream(stream_type)
                self.active_streams.discard(stream_type)
                logger.info(f"Stopped Binance stream: {stream_type}")
                
        logger.info(f"Client disconnected from {stream_type} stream")
        
    async def broadcast_to_stream(self, stream_type: str, data: Dict):
        """Broadcast data to all clients subscribed to a stream"""
        if stream_type not in self.active_connections:
            return
            
        disconnected = []
        for websocket in self.active_connections[stream_type]:
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_json(data)
                else:
                    disconnected.append(websocket)
            except Exception as e:
                logger.error(f"Error broadcasting to client: {e}")
                disconnected.append(websocket)
                
        # Clean up disconnected clients
        for websocket in disconnected:
            self.active_connections[stream_type].discard(websocket)
            
    async def start_binance_stream(self, symbol: str, interval: str):
        """Start Binance kline stream if not already active"""
        stream_name = f"{symbol.lower()}@kline_{interval}"
        
        if stream_name not in self.active_streams:
            self.active_streams.add(stream_name)
            
            async def kline_callback(kline_data: KlineData):
                """Callback for kline data"""
                data = {
                    'type': 'kline',
                    'data': kline_data.to_dict()
                }
                await self.broadcast_to_stream('kline', data)
                
            # Start the stream in background
            asyncio.create_task(
                self.binance_client.connect_kline_stream(symbol, interval, kline_callback)
            )
            logger.info(f"Started Binance kline stream: {stream_name}")
            
    async def start_ticker_stream(self, symbol: str):
        """Start Binance ticker stream if not already active"""
        stream_name = f"{symbol.lower()}@ticker"
        
        if stream_name not in self.active_streams:
            self.active_streams.add(stream_name)
            
            async def ticker_callback(ticker_data: TickerData):
                """Callback for ticker data"""
                data = {
                    'type': 'ticker',
                    'data': ticker_data.to_dict()
                }
                await self.broadcast_to_stream('ticker', data)
                
            # Start the stream in background
            asyncio.create_task(
                self.binance_client.connect_ticker_stream(symbol, ticker_callback)
            )
            logger.info(f"Started Binance ticker stream: {stream_name}")

# Global connection manager instance
manager = ConnectionManager()


@router.websocket("/kline")
async def websocket_kline_endpoint(
    websocket: WebSocket,
    symbol: str = Query(..., description="Trading symbol (e.g., BTCUSDT)"),
    interval: str = Query(..., description="Kline interval (1m, 5m, 15m, 1h, 4h, 1d)")
):
    """
    WebSocket endpoint for real-time candlestick data
    
    Provides live kline/candlestick updates from Binance
    """
    await manager.connect(websocket, 'kline')
    
    try:
        # Start Binance stream if needed
        await manager.start_binance_stream(symbol, interval)
        
        # Send initial connection confirmation
        await websocket.send_json({
            'type': 'connection',
            'status': 'connected',
            'stream': f"{symbol}@kline_{interval}",
            'message': f'Connected to {symbol} {interval} candlestick stream'
        })
        
        # Keep connection alive and handle client messages
        while True:
            try:
                # Wait for client messages (for ping/pong or commands)
                message = await websocket.receive_text()
                data = json.loads(message)
                
                if data.get('type') == 'ping':
                    await websocket.send_json({'type': 'pong'})
                elif data.get('type') == 'subscribe':
                    # Handle subscription changes
                    new_symbol = data.get('symbol', symbol)
                    new_interval = data.get('interval', interval)
                    if new_symbol != symbol or new_interval != interval:
                        await manager.start_binance_stream(new_symbol, new_interval)
                        await websocket.send_json({
                            'type': 'subscribed',
                            'stream': f"{new_symbol}@kline_{new_interval}"
                        })
                        
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_json({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                })
            except Exception as e:
                logger.error(f"Error in kline WebSocket: {e}")
                await websocket.send_json({
                    'type': 'error',
                    'message': str(e)
                })
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket kline endpoint error: {e}")
    finally:
        await manager.disconnect(websocket, 'kline')


@router.websocket("/ticker")
async def websocket_ticker_endpoint(
    websocket: WebSocket,
    symbol: str = Query(..., description="Trading symbol (e.g., BTCUSDT)")
):
    """
    WebSocket endpoint for real-time ticker data
    
    Provides live 24hr ticker statistics from Binance
    """
    await manager.connect(websocket, 'ticker')
    
    try:
        # Start Binance ticker stream if needed
        await manager.start_ticker_stream(symbol)
        
        # Send initial connection confirmation
        await websocket.send_json({
            'type': 'connection',
            'status': 'connected',
            'stream': f"{symbol}@ticker",
            'message': f'Connected to {symbol} ticker stream'
        })
        
        # Keep connection alive
        while True:
            try:
                message = await websocket.receive_text()
                data = json.loads(message)
                
                if data.get('type') == 'ping':
                    await websocket.send_json({'type': 'pong'})
                    
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_json({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                })
            except Exception as e:
                logger.error(f"Error in ticker WebSocket: {e}")
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket ticker endpoint error: {e}")
    finally:
        await manager.disconnect(websocket, 'ticker')


@router.websocket("/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    symbols: str = Query(..., description="Comma-separated trading symbols (e.g., BTCUSDT,ETHUSDT)")
):
    """
    WebSocket endpoint for multiple market data streams
    
    Provides combined market data for multiple symbols
    """
    await manager.connect(websocket, 'market')
    
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(',')]
        
        # Start streams for all symbols
        for symbol in symbol_list:
            await manager.start_ticker_stream(symbol)
            
        # Send initial connection confirmation
        await websocket.send_json({
            'type': 'connection',
            'status': 'connected',
            'symbols': symbol_list,
            'message': f'Connected to market data for {len(symbol_list)} symbols'
        })
        
        # Keep connection alive
        while True:
            try:
                message = await websocket.receive_text()
                data = json.loads(message)
                
                if data.get('type') == 'ping':
                    await websocket.send_json({'type': 'pong'})
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error in market WebSocket: {e}")
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket market endpoint error: {e}")
    finally:
        await manager.disconnect(websocket, 'market')


@router.get("/status")
async def websocket_status():
    """Get WebSocket connection status"""
    return {
        'active_connections': {
            stream_type: len(connections) 
            for stream_type, connections in manager.active_connections.items()
        },
        'active_streams': list(manager.active_streams),
        'total_connections': sum(len(connections) for connections in manager.active_connections.values())
    }
