<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Chart Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            margin: 0;
            padding: 20px;
        }
        
        #chart {
            width: 100%;
            height: 600px;
            background-color: #131722;
            border: 1px solid #363c4e;
            margin: 20px 0;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background-color: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #1e88e5;
        }
        
        #status {
            margin: 10px 0;
            padding: 10px;
            background-color: #1e222d;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Simple Chart Test</h1>
    <div id="status">Loading...</div>
    
    <div class="controls">
        <button onclick="testChart()">Test Chart</button>
        <button onclick="clearChart()">Clear Chart</button>
    </div>
    
    <div id="chart"></div>

    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        let chart = null;
        let candlestickSeries = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function initChart() {
            try {
                updateStatus('Initializing chart...');
                
                const container = document.getElementById('chart');
                console.log('Container:', container);
                console.log('Container dimensions:', container.clientWidth, 'x', container.clientHeight);
                
                if (typeof LightweightCharts === 'undefined') {
                    updateStatus('Error: TradingView library not loaded');
                    return;
                }
                
                chart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: 600,
                    layout: {
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                        fontSize: 12,
                        fontFamily: 'Trebuchet MS, sans-serif',
                    },
                    grid: {
                        vertLines: {
                            color: '#363c4e',
                            style: 1,
                            visible: true,
                        },
                        horzLines: {
                            color: '#363c4e',
                            style: 1,
                            visible: true,
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#485c7b',
                        textColor: '#b2b5be',
                    },
                    timeScale: {
                        borderColor: '#485c7b',
                        textColor: '#b2b5be',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                });
                
                candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderDownColor: '#ef5350',
                    borderUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                });
                
                updateStatus('Chart initialized successfully');
                console.log('Chart created:', chart);
                console.log('Candlestick series:', candlestickSeries);
                
            } catch (error) {
                console.error('Error initializing chart:', error);
                updateStatus('Error initializing chart: ' + error.message);
            }
        }
        
        function testChart() {
            if (!chart || !candlestickSeries) {
                updateStatus('Chart not initialized');
                return;
            }
            
            try {
                updateStatus('Generating test data...');
                
                // Generate sample data
                const data = [];
                const startTime = new Date('2024-01-01T00:00:00Z').getTime() / 1000;
                const oneHour = 60 * 60;
                
                for (let i = 0; i < 100; i++) {
                    const time = startTime + (i * oneHour);
                    const open = 50000 + Math.random() * 10000;
                    const close = open + (Math.random() - 0.5) * 2000;
                    const high = Math.max(open, close) + Math.random() * 1000;
                    const low = Math.min(open, close) - Math.random() * 1000;
                    
                    data.push({
                        time: time,
                        open: open,
                        high: high,
                        low: low,
                        close: close
                    });
                }
                
                console.log('Sample data:', data.slice(0, 3));
                
                candlestickSeries.setData(data);
                chart.timeScale().fitContent();
                
                updateStatus(`Test data loaded: ${data.length} candles`);
                
            } catch (error) {
                console.error('Error loading test data:', error);
                updateStatus('Error loading test data: ' + error.message);
            }
        }
        
        function clearChart() {
            if (candlestickSeries) {
                candlestickSeries.setData([]);
                updateStatus('Chart cleared');
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                initChart();
            }, 100);
        });
    </script>
</body>
</html>
