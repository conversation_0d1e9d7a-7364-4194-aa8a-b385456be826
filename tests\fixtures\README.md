# Test Fixtures

This directory contains test data, mock objects, and shared fixtures used across the test suite.

## Purpose

Test fixtures provide:
- Sample data for testing
- Mock objects and responses
- Shared test configurations
- Database seed data for tests
- API response examples

## Structure

```
fixtures/
├── data/           # Sample data files (JSON, CSV, etc.)
├── mocks/          # Mock objects and responses
├── configs/        # Test configuration files
└── database/       # Database fixtures and seed data
```

## Usage

```python
# Example: Loading test data
import json
from pathlib import Path

def load_fixture(filename):
    fixture_path = Path(__file__).parent / "fixtures" / "data" / filename
    with open(fixture_path) as f:
        return json.load(f)

# In your test
def test_api_endpoint():
    test_data = load_fixture("sample_ohlcv.json")
    # Use test_data in your test
```

## Best Practices

1. Keep fixtures small and focused
2. Use descriptive filenames
3. Document complex fixtures
4. Avoid hardcoded values when possible
5. Clean up after tests that modify fixtures

## Adding New Fixtures

1. Create appropriate subdirectory if needed
2. Add fixture file with descriptive name
3. Document the fixture purpose
4. Update this README if adding new categories
