#!/usr/bin/env python3
"""
Fix OHLCV table structure by adding proper ID column
"""
import mysql.connector
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def fix_ohlcv_table():
    """Add proper ID column to ohlcv_data table"""
    print("🔧 Fixing OHLCV table structure...")

    try:
        with get_db_cursor() as cursor:
            # Check current structure and keys
            cursor.execute("DESCRIBE ohlcv_data")
            columns_info = cursor.fetchall()
            columns = [row['Field'] for row in columns_info]

            print("📊 Current table structure:")
            for col in columns_info:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''}")

            # Check for existing primary key
            cursor.execute("SHOW INDEX FROM ohlcv_data WHERE Key_name = 'PRIMARY'")
            primary_keys = cursor.fetchall()

            if primary_keys:
                print(f"🔍 Found existing primary key: {[pk['Column_name'] for pk in primary_keys]}")

            # Add ID column if it doesn't exist
            if 'id' not in columns:
                print("➕ Adding ID column...")
                if primary_keys:
                    # Drop existing primary key first
                    print("🗑️ Dropping existing primary key...")
                    cursor.execute("ALTER TABLE ohlcv_data DROP PRIMARY KEY")

                cursor.execute("ALTER TABLE ohlcv_data ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST")
                print("✅ ID column added successfully")
            else:
                print("✅ ID column already exists")
            
            # Check if we need to update the unique constraint
            cursor.execute("SHOW INDEX FROM ohlcv_data WHERE Key_name = 'unique_ohlcv'")
            unique_index = cursor.fetchall()
            
            if unique_index:
                print("🔄 Updating unique constraint...")
                cursor.execute("ALTER TABLE ohlcv_data DROP INDEX unique_ohlcv")
                cursor.execute("""
                    ALTER TABLE ohlcv_data 
                    ADD UNIQUE KEY unique_ohlcv (symbol, timeframe, timestamp, exchange, strategy_id)
                """)
                print("✅ Unique constraint updated")
            
            # Verify the structure
            print("\n📊 Updated OHLCV table structure:")
            cursor.execute("DESCRIBE ohlcv_data")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")
            
            # Check data count
            cursor.execute("SELECT COUNT(*) as count FROM ohlcv_data")
            count = cursor.fetchone()['count']
            print(f"\n📈 Total OHLCV records: {count}")
            
            print("✅ OHLCV table structure fixed successfully!")
            
    except Exception as e:
        print(f"❌ Error fixing table: {e}")
        raise

if __name__ == "__main__":
    fix_ohlcv_table()
