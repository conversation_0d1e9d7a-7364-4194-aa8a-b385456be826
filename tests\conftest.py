"""
Pytest configuration and fixtures
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import tempfile
import os
from datetime import datetime, timedelta

# Add backend to path
import sys
from pathlib import Path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.main import app
from app.core.database import Base, get_db
from app.core.config import settings
from app.models.ohlcv import OHLCVData
from app.models.indicators import IndicatorsData
from app.models.trades import ManualMarks, StrategyLog


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_db_engine():
    """Create test database engine"""
    # Use in-memory SQLite for testing
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False}
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_db_session(test_db_engine):
    """Create test database session"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db_engine)
    session = TestingSessionLocal()
    
    yield session
    
    session.close()


@pytest.fixture(scope="function")
def test_client(test_db_session):
    """Create test client with test database"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


@pytest.fixture
def sample_ohlcv_data():
    """Generate sample OHLCV data for testing"""
    base_time = datetime.now()
    data = []
    
    for i in range(100):
        timestamp = base_time + timedelta(hours=i)
        price = 50000 + (i * 10) + (i % 10) * 100  # Trending price with volatility
        
        data.append({
            'timestamp': timestamp,
            'open': price - 50,
            'high': price + 100,
            'low': price - 100,
            'close': price,
            'volume': 1000 + (i * 10)
        })
    
    return data


@pytest.fixture
def sample_ohlcv_records(test_db_session, sample_ohlcv_data):
    """Create sample OHLCV records in test database"""
    records = []
    
    for data in sample_ohlcv_data[:50]:  # Use first 50 records
        record = OHLCVData(
            symbol="BTCUSDT",
            timeframe="1h",
            timestamp=data['timestamp'],
            open=data['open'],
            high=data['high'],
            low=data['low'],
            close=data['close'],
            volume=data['volume']
        )
        test_db_session.add(record)
        records.append(record)
    
    test_db_session.commit()
    return records


@pytest.fixture
def sample_indicators_config():
    """Sample indicators configuration"""
    return {
        'rsi': {'period': 14},
        'macd': {'fast': 12, 'slow': 26, 'signal': 9},
        'ema': {'period': 20},
        'sma': {'period': 50}
    }


@pytest.fixture
def mock_binance_response():
    """Mock Binance API response"""
    base_time = int(datetime.now().timestamp() * 1000)
    
    return [
        [
            base_time + (i * 3600000),  # timestamp
            "50000.00",  # open
            "50100.00",  # high
            "49900.00",  # low
            "50050.00",  # close
            "1000.00",   # volume
            base_time + (i * 3600000) + 3599999,  # close time
            "50050000.00",  # quote asset volume
            100,  # number of trades
            "500.00",  # taker buy base asset volume
            "25025000.00",  # taker buy quote asset volume
            "0"  # ignore
        ]
        for i in range(10)
    ]


@pytest.fixture
def mock_exchange_error():
    """Mock exchange error response"""
    return {
        "code": -1121,
        "msg": "Invalid symbol."
    }


class MockBinanceClient:
    """Mock Binance client for testing"""
    
    def __init__(self, should_fail=False, rate_limited=False):
        self.should_fail = should_fail
        self.rate_limited = rate_limited
    
    async def get_klines(self, symbol, interval, limit=500, start_time=None, end_time=None):
        if self.should_fail:
            from app.core.exceptions import ExchangeError
            raise ExchangeError("Mock exchange error", "binance")
        
        if self.rate_limited:
            from app.core.exceptions import RateLimitError
            raise RateLimitError("binance", 60)
        
        # Return mock data
        base_time = datetime.now()
        return [
            {
                'timestamp': base_time + timedelta(hours=i),
                'open': 50000 + i,
                'high': 50100 + i,
                'low': 49900 + i,
                'close': 50000 + i,
                'volume': 1000
            }
            for i in range(min(limit, 10))
        ]
    
    async def test_connection(self):
        return not self.should_fail


@pytest.fixture
def mock_binance_client():
    """Mock Binance client fixture"""
    return MockBinanceClient()


@pytest.fixture
def mock_binance_client_error():
    """Mock Binance client that raises errors"""
    return MockBinanceClient(should_fail=True)


@pytest.fixture
def mock_binance_client_rate_limited():
    """Mock Binance client that is rate limited"""
    return MockBinanceClient(rate_limited=True)
