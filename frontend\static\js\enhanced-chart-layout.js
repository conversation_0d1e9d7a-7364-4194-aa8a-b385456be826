/**
 * Enhanced Chart Layout Manager
 * Advanced multi-panel chart system with dynamic resizing, layout persistence, and synchronization
 */

class EnhancedChartLayoutManager {
    constructor() {
        this.charts = new Map();
        this.panels = new Map();
        this.layouts = new Map();
        this.currentLayout = 'default';
        this.syncEnabled = true;
        this.resizeObserver = null;
        this.dragState = null;
        
        // Layout configuration
        this.defaultPanels = [
            { id: 'main', type: 'candlestick', height: 60, resizable: true },
            { id: 'volume', type: 'volume', height: 20, resizable: true },
            { id: 'indicators', type: 'indicators', height: 20, resizable: true }
        ];
        
        this.init();
    }

    init() {
        this.setupResizeObserver();
        this.loadSavedLayouts();
        this.setupEventListeners();
        this.createDefaultLayout();
    }

    setupResizeObserver() {
        if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(entries => {
                for (let entry of entries) {
                    const panelId = entry.target.dataset.panelId;
                    if (panelId && this.charts.has(panelId)) {
                        this.handlePanelResize(panelId, entry.contentRect);
                    }
                }
            });
        }
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.resizeAllCharts();
        }, 250));

        // Layout controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.layout-preset-btn')) {
                this.applyLayout(e.target.dataset.layout);
            }
            if (e.target.matches('.save-layout-btn')) {
                this.showSaveLayoutDialog();
            }
            if (e.target.matches('.panel-close-btn')) {
                this.removePanel(e.target.dataset.panelId);
            }
        });

        // Panel drag handles
        document.addEventListener('mousedown', (e) => {
            if (e.target.matches('.panel-resize-handle')) {
                this.startPanelResize(e);
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.dragState) {
                this.handlePanelDrag(e);
            }
        });

        document.addEventListener('mouseup', () => {
            if (this.dragState) {
                this.endPanelResize();
            }
        });
    }

    createDefaultLayout() {
        const container = document.getElementById('chart-container');
        if (!container) {
            console.error('Chart container not found');
            return;
        }

        // Clear existing content
        container.innerHTML = '';

        // Create panels based on default configuration
        this.defaultPanels.forEach(panelConfig => {
            this.createPanel(panelConfig);
        });

        // Initialize charts
        this.initializeCharts();
        
        // Save as default layout
        this.saveLayout('default');
    }

    createPanel(config) {
        const container = document.getElementById('chart-container');
        const panel = document.createElement('div');
        panel.className = 'chart-panel enhanced-panel';
        panel.dataset.panelId = config.id;
        panel.dataset.panelType = config.type;
        panel.style.height = `${config.height}%`;

        panel.innerHTML = `
            <div class="panel-header">
                <div class="panel-title">
                    <span class="panel-name">${this.getPanelDisplayName(config.type)}</span>
                    <div class="panel-indicators">
                        <span class="sync-indicator ${this.syncEnabled ? 'active' : ''}" 
                              title="Synchronized">⚡</span>
                    </div>
                </div>
                <div class="panel-controls">
                    <button class="panel-control-btn" data-action="settings" 
                            title="Panel Settings">⚙️</button>
                    <button class="panel-control-btn" data-action="fullscreen" 
                            title="Fullscreen">⛶</button>
                    <button class="panel-control-btn panel-close-btn" 
                            data-panel-id="${config.id}" title="Close Panel">✕</button>
                </div>
            </div>
            <div class="panel-content" id="chart-${config.id}"></div>
            ${config.resizable ? '<div class="panel-resize-handle" data-panel-id="' + config.id + '"></div>' : ''}
        `;

        container.appendChild(panel);
        this.panels.set(config.id, { element: panel, config: config });

        // Setup resize observer
        if (this.resizeObserver) {
            this.resizeObserver.observe(panel);
        }

        return panel;
    }

    initializeCharts() {
        this.panels.forEach((panel, panelId) => {
            const chartContainer = document.getElementById(`chart-${panelId}`);
            if (!chartContainer) return;

            const chartOptions = this.getChartOptions(panel.config.type);
            const chart = LightweightCharts.createChart(chartContainer, chartOptions);
            
            this.charts.set(panelId, {
                chart: chart,
                series: new Map(),
                type: panel.config.type,
                container: chartContainer
            });

            // Setup chart-specific series
            this.setupChartSeries(panelId, panel.config.type);

            // Setup synchronization
            if (this.syncEnabled) {
                this.setupChartSync(panelId);
            }
        });
    }

    getChartOptions(panelType) {
        const baseOptions = {
            layout: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
            },
            grid: {
                vertLines: { color: '#363c4e' },
                horzLines: { color: '#363c4e' },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            timeScale: {
                borderColor: '#485c7b',
                timeVisible: true,
                secondsVisible: false,
            },
            rightPriceScale: {
                borderColor: '#485c7b',
            },
            handleScroll: {
                mouseWheel: true,
                pressedMouseMove: true,
            },
            handleScale: {
                axisPressedMouseMove: true,
                mouseWheel: true,
                pinch: true,
            },
        };

        // Panel-specific options
        switch (panelType) {
            case 'volume':
                return {
                    ...baseOptions,
                    rightPriceScale: {
                        ...baseOptions.rightPriceScale,
                        scaleMargins: { top: 0.1, bottom: 0 },
                    },
                };
            case 'indicators':
                return {
                    ...baseOptions,
                    rightPriceScale: {
                        ...baseOptions.rightPriceScale,
                        scaleMargins: { top: 0.1, bottom: 0.1 },
                    },
                };
            default:
                return baseOptions;
        }
    }

    setupChartSeries(panelId, panelType) {
        const chartData = this.charts.get(panelId);
        if (!chartData) return;

        const { chart } = chartData;

        switch (panelType) {
            case 'candlestick':
            case 'main':
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                chartData.series.set('candlestick', candlestickSeries);
                break;

            case 'volume':
                const volumeSeries = chart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: { type: 'volume' },
                    priceScaleId: '',
                });
                chartData.series.set('volume', volumeSeries);
                break;

            case 'indicators':
                // Indicators will be added dynamically
                break;
        }
    }

    setupChartSync(panelId) {
        const chartData = this.charts.get(panelId);
        if (!chartData) return;

        const { chart } = chartData;

        // Sync crosshair
        chart.subscribeCrosshairMove(param => {
            if (!this.syncEnabled) return;

            this.charts.forEach((otherChartData, otherPanelId) => {
                if (otherPanelId !== panelId) {
                    otherChartData.chart.setCrosshairPosition(
                        param.point ? param.point.x : null,
                        param.time,
                        otherChartData.series.values().next().value
                    );
                }
            });
        });

        // Sync time scale
        chart.timeScale().subscribeVisibleTimeRangeChange(timeRange => {
            if (!this.syncEnabled) return;

            this.charts.forEach((otherChartData, otherPanelId) => {
                if (otherPanelId !== panelId) {
                    otherChartData.chart.timeScale().setVisibleRange(timeRange);
                }
            });
        });
    }

    addIndicatorToPanel(panelId, indicatorName, indicatorData, config) {
        const chartData = this.charts.get(panelId);
        if (!chartData) return;

        const { chart, series } = chartData;
        const seriesKey = `${indicatorName}_${Date.now()}`;

        // Determine series type based on indicator
        let indicatorSeries;
        switch (indicatorName.toUpperCase()) {
            case 'EMA':
            case 'SMA':
                indicatorSeries = chart.addLineSeries({
                    color: config.color || '#2196F3',
                    lineWidth: config.lineWidth || 2,
                    title: `${indicatorName}(${config.period || 20})`,
                });
                break;

            case 'BOLLINGER_BANDS':
                // Add multiple lines for Bollinger Bands
                const upperSeries = chart.addLineSeries({
                    color: config.upperColor || '#FF9800',
                    lineWidth: 1,
                    title: 'BB Upper',
                });
                const middleSeries = chart.addLineSeries({
                    color: config.middleColor || '#2196F3',
                    lineWidth: 1,
                    title: 'BB Middle',
                });
                const lowerSeries = chart.addLineSeries({
                    color: config.lowerColor || '#FF9800',
                    lineWidth: 1,
                    title: 'BB Lower',
                });

                series.set(`${seriesKey}_upper`, upperSeries);
                series.set(`${seriesKey}_middle`, middleSeries);
                series.set(`${seriesKey}_lower`, lowerSeries);

                // Set data for each line
                if (indicatorData.upper) upperSeries.setData(indicatorData.upper);
                if (indicatorData.middle) middleSeries.setData(indicatorData.middle);
                if (indicatorData.lower) lowerSeries.setData(indicatorData.lower);
                return;

            case 'MACD':
                const macdSeries = chart.addLineSeries({
                    color: config.macdColor || '#2196F3',
                    lineWidth: 2,
                    title: 'MACD',
                });
                const signalSeries = chart.addLineSeries({
                    color: config.signalColor || '#FF9800',
                    lineWidth: 2,
                    title: 'Signal',
                });
                const histogramSeries = chart.addHistogramSeries({
                    color: config.histogramColor || '#26a69a',
                    title: 'Histogram',
                });

                series.set(`${seriesKey}_macd`, macdSeries);
                series.set(`${seriesKey}_signal`, signalSeries);
                series.set(`${seriesKey}_histogram`, histogramSeries);

                if (indicatorData.macd) macdSeries.setData(indicatorData.macd);
                if (indicatorData.signal) signalSeries.setData(indicatorData.signal);
                if (indicatorData.histogram) histogramSeries.setData(indicatorData.histogram);
                return;

            default:
                indicatorSeries = chart.addLineSeries({
                    color: config.color || '#2196F3',
                    lineWidth: config.lineWidth || 2,
                    title: indicatorName,
                });
        }

        if (indicatorSeries && indicatorData) {
            indicatorSeries.setData(indicatorData);
            series.set(seriesKey, indicatorSeries);
        }
    }

    removeIndicatorFromPanel(panelId, seriesKey) {
        const chartData = this.charts.get(panelId);
        if (!chartData) return;

        const { chart, series } = chartData;
        const indicatorSeries = series.get(seriesKey);
        
        if (indicatorSeries) {
            chart.removeSeries(indicatorSeries);
            series.delete(seriesKey);
        }
    }

    startPanelResize(e) {
        e.preventDefault();
        const panelId = e.target.dataset.panelId;
        const panel = this.panels.get(panelId);
        
        if (!panel) return;

        this.dragState = {
            panelId: panelId,
            startY: e.clientY,
            startHeight: panel.element.offsetHeight,
            containerHeight: panel.element.parentElement.offsetHeight
        };

        document.body.style.cursor = 'ns-resize';
        document.body.style.userSelect = 'none';
    }

    handlePanelDrag(e) {
        if (!this.dragState) return;

        const deltaY = e.clientY - this.dragState.startY;
        const newHeight = this.dragState.startHeight + deltaY;
        const heightPercent = (newHeight / this.dragState.containerHeight) * 100;

        // Constrain height between 10% and 80%
        const constrainedHeight = Math.max(10, Math.min(80, heightPercent));

        const panel = this.panels.get(this.dragState.panelId);
        if (panel) {
            panel.element.style.height = `${constrainedHeight}%`;
            panel.config.height = constrainedHeight;
        }
    }

    endPanelResize() {
        if (!this.dragState) return;

        document.body.style.cursor = '';
        document.body.style.userSelect = '';

        // Resize chart to fit new panel size
        const panelId = this.dragState.panelId;
        this.resizeChart(panelId);

        // Save layout changes
        this.saveCurrentLayout();

        this.dragState = null;
    }

    handlePanelResize(panelId, rect) {
        // Debounced chart resize
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            this.resizeChart(panelId);
        }, 100);
    }

    resizeChart(panelId) {
        const chartData = this.charts.get(panelId);
        if (!chartData) return;

        const container = chartData.container;
        const rect = container.getBoundingClientRect();

        chartData.chart.applyOptions({
            width: rect.width,
            height: rect.height
        });
    }

    resizeAllCharts() {
        this.charts.forEach((chartData, panelId) => {
            this.resizeChart(panelId);
        });
    }

    toggleSync() {
        this.syncEnabled = !this.syncEnabled;
        
        // Update sync indicators
        document.querySelectorAll('.sync-indicator').forEach(indicator => {
            indicator.classList.toggle('active', this.syncEnabled);
        });

        // Re-setup synchronization
        if (this.syncEnabled) {
            this.charts.forEach((chartData, panelId) => {
                this.setupChartSync(panelId);
            });
        }
    }

    saveLayout(name) {
        const layout = {
            name: name,
            panels: [],
            syncEnabled: this.syncEnabled,
            timestamp: Date.now()
        };

        this.panels.forEach((panel, panelId) => {
            layout.panels.push({
                id: panelId,
                type: panel.config.type,
                height: panel.config.height,
                resizable: panel.config.resizable,
                visible: true
            });
        });

        this.layouts.set(name, layout);
        this.saveLayoutsToStorage();
    }

    saveCurrentLayout() {
        this.saveLayout(this.currentLayout);
    }

    applyLayout(layoutName) {
        const layout = this.layouts.get(layoutName);
        if (!layout) return;

        // Clear current panels
        this.clearAllPanels();

        // Create panels from layout
        layout.panels.forEach(panelConfig => {
            this.createPanel(panelConfig);
        });

        // Initialize charts
        this.initializeCharts();

        // Apply sync setting
        this.syncEnabled = layout.syncEnabled;
        this.toggleSync();

        this.currentLayout = layoutName;
    }

    clearAllPanels() {
        // Dispose charts
        this.charts.forEach(chartData => {
            chartData.chart.remove();
        });
        this.charts.clear();

        // Remove panels
        this.panels.forEach(panel => {
            if (this.resizeObserver) {
                this.resizeObserver.unobserve(panel.element);
            }
            panel.element.remove();
        });
        this.panels.clear();
    }

    removePanel(panelId) {
        const panel = this.panels.get(panelId);
        const chartData = this.charts.get(panelId);

        if (chartData) {
            chartData.chart.remove();
            this.charts.delete(panelId);
        }

        if (panel) {
            if (this.resizeObserver) {
                this.resizeObserver.unobserve(panel.element);
            }
            panel.element.remove();
            this.panels.delete(panelId);
        }

        this.saveCurrentLayout();
    }

    getPanelDisplayName(type) {
        const names = {
            'candlestick': 'Price Chart',
            'main': 'Price Chart',
            'volume': 'Volume',
            'indicators': 'Technical Indicators'
        };
        return names[type] || type.charAt(0).toUpperCase() + type.slice(1);
    }

    loadSavedLayouts() {
        try {
            const saved = localStorage.getItem('chartLayouts');
            if (saved) {
                const layouts = JSON.parse(saved);
                Object.entries(layouts).forEach(([name, layout]) => {
                    this.layouts.set(name, layout);
                });
            }
        } catch (error) {
            console.error('Error loading saved layouts:', error);
        }
    }

    saveLayoutsToStorage() {
        try {
            const layoutsObj = {};
            this.layouts.forEach((layout, name) => {
                layoutsObj[name] = layout;
            });
            localStorage.setItem('chartLayouts', JSON.stringify(layoutsObj));
        } catch (error) {
            console.error('Error saving layouts:', error);
        }
    }

    showSaveLayoutDialog() {
        // Implementation for save layout dialog
        const name = prompt('Enter layout name:');
        if (name && name.trim()) {
            this.saveLayout(name.trim());
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    getLayoutStats() {
        return {
            totalPanels: this.panels.size,
            totalCharts: this.charts.size,
            currentLayout: this.currentLayout,
            syncEnabled: this.syncEnabled,
            savedLayouts: this.layouts.size
        };
    }
}

// Global instance
let enhancedLayoutManager = null;

document.addEventListener('DOMContentLoaded', () => {
    enhancedLayoutManager = new EnhancedChartLayoutManager();
    window.enhancedLayoutManager = enhancedLayoutManager;
});
