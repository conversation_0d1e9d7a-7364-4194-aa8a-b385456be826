"""
Integration tests for OHLCV API endpoints
"""
import pytest
from unittest.mock import patch, AsyncMock
from datetime import datetime, timedelta
import json

import sys
from pathlib import Path
backend_dir = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.core.exceptions import ExchangeError, RateLimitError


class TestOHLCVFetchEndpoint:
    """Test OHLCV fetch endpoint"""
    
    def test_fetch_ohlcv_success(self, test_client, mock_binance_client):
        """Test successful OHLCV data fetch"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10
        }
        
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client):
            response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "total_fetched" in data["data"]
        assert data["data"]["symbol"] == "BTCUSDT"
        assert data["data"]["timeframe"] == "1h"
        assert data["data"]["exchange"] == "binance"
    
    def test_fetch_ohlcv_invalid_symbol(self, test_client):
        """Test fetch with invalid symbol"""
        request_data = {
            "symbol": "",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10
        }
        
        response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_fetch_ohlcv_invalid_timeframe(self, test_client):
        """Test fetch with invalid timeframe"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "2m",  # Invalid timeframe
            "exchange": "binance",
            "limit": 10
        }
        
        response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_fetch_ohlcv_invalid_exchange(self, test_client):
        """Test fetch with invalid exchange"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "invalid",
            "limit": 10
        }
        
        response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_fetch_ohlcv_invalid_limit(self, test_client):
        """Test fetch with invalid limit"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 0  # Invalid limit
        }
        
        response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_fetch_ohlcv_exchange_error(self, test_client, mock_binance_client_error):
        """Test fetch with exchange error"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10
        }
        
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client_error):
            response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        
        assert response.status_code == 502  # Bad Gateway
    
    def test_fetch_ohlcv_rate_limited(self, test_client, mock_binance_client_rate_limited):
        """Test fetch with rate limiting"""
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10
        }
        
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client_rate_limited):
            response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        
        assert response.status_code == 429  # Too Many Requests
    
    def test_fetch_ohlcv_with_time_range(self, test_client, mock_binance_client):
        """Test fetch with time range"""
        start_time = datetime.now() - timedelta(hours=24)
        end_time = datetime.now()
        
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat()
        }
        
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client):
            response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_fetch_ohlcv_invalid_time_range(self, test_client):
        """Test fetch with invalid time range (end before start)"""
        start_time = datetime.now()
        end_time = datetime.now() - timedelta(hours=1)
        
        request_data = {
            "symbol": "BTCUSDT",
            "timeframe": "1h",
            "exchange": "binance",
            "limit": 10,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat()
        }
        
        response = test_client.post("/api/v1/ohlcv/fetch", json=request_data)
        assert response.status_code == 422  # Validation error


class TestOHLCVDataEndpoint:
    """Test OHLCV data retrieval endpoint"""
    
    def test_get_ohlcv_data_success(self, test_client, sample_ohlcv_records):
        """Test successful OHLCV data retrieval"""
        response = test_client.get("/api/v1/ohlcv/data?symbol=BTCUSDT&timeframe=1h&limit=10")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]["ohlcv"]) > 0
        assert data["data"]["symbol"] == "BTCUSDT"
        assert data["data"]["timeframe"] == "1h"
    
    def test_get_ohlcv_data_not_found(self, test_client):
        """Test OHLCV data retrieval when no data exists"""
        response = test_client.get("/api/v1/ohlcv/data?symbol=NONEXISTENT&timeframe=1h&limit=10")
        
        assert response.status_code == 404
    
    def test_get_ohlcv_data_with_time_filter(self, test_client, sample_ohlcv_records):
        """Test OHLCV data retrieval with time filter"""
        start_time = datetime.now() - timedelta(hours=12)
        end_time = datetime.now()
        
        response = test_client.get(
            f"/api/v1/ohlcv/data?symbol=BTCUSDT&timeframe=1h&limit=10"
            f"&start_time={start_time.isoformat()}&end_time={end_time.isoformat()}"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True


class TestOHLCVConnectionEndpoint:
    """Test exchange connection endpoint"""
    
    def test_test_connection_binance_success(self, test_client, mock_binance_client):
        """Test successful Binance connection"""
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client):
            response = test_client.get("/api/v1/ohlcv/test-connection?exchange=binance")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["exchange"] == "binance"
    
    def test_test_connection_binance_failure(self, test_client, mock_binance_client_error):
        """Test failed Binance connection"""
        with patch('app.services.binance_client.BinanceClient', return_value=mock_binance_client_error):
            response = test_client.get("/api/v1/ohlcv/test-connection?exchange=binance")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is False
        assert data["exchange"] == "binance"
    
    def test_test_connection_invalid_exchange(self, test_client):
        """Test connection test with invalid exchange"""
        response = test_client.get("/api/v1/ohlcv/test-connection?exchange=invalid")
        
        assert response.status_code == 400


class TestOHLCVSymbolsEndpoint:
    """Test symbols endpoint"""
    
    def test_get_symbols_success(self, test_client, sample_ohlcv_records):
        """Test successful symbols retrieval"""
        response = test_client.get("/api/v1/ohlcv/symbols?exchange=binance")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "database_symbols" in data["data"]
        assert "exchange_symbols" in data["data"]
        assert data["data"]["exchange"] == "binance"
    
    def test_get_symbols_invalid_exchange(self, test_client):
        """Test symbols retrieval with invalid exchange"""
        response = test_client.get("/api/v1/ohlcv/symbols?exchange=invalid")
        
        assert response.status_code == 500  # Will fail when trying to create client
