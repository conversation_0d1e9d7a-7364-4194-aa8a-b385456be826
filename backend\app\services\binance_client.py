"""
Enhanced Binance API Client with rate limiting and better error handling
"""
import requests
import hmac
import hashlib
import time
import asyncio
from typing import List, Dict, Optional, Union
from datetime import datetime, timedelta
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from app.core.config import settings
from app.core.exceptions import ExchangeError, RateLimitError, AuthenticationError, ValidationError
from app.core.rate_limiter import exchange_rate_limiter

logger = logging.getLogger(__name__)

class BinanceClient:
    """Enhanced Binance Futures API client with rate limiting and error handling"""

    def __init__(self):
        self.config = settings.binance_config
        # Use public API URL for unauthenticated endpoints
        self.base_url = "https://api.binance.com"
        self.api_key = self.config.get("api_key", "")
        self.api_secret = self.config.get("api_secret", "")
        self.rate_limit = self.config.get("rate_limit", 1200)
        self.testnet = self.config.get("testnet", False)

        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Set headers
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'StrategyBuilder/1.0'
        })

        # Validate configuration
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate API configuration"""
        if not self.api_key:
            logger.warning("Binance API key not configured")
        if not self.api_secret:
            logger.warning("Binance API secret not configured")

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        if not self.api_secret:
            raise AuthenticationError("API secret not configured", "binance")

        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _make_request(self, endpoint: str, params: Optional[Dict] = None,
                           signed: bool = False, method: str = "GET") -> Dict:
        """Make API request with rate limiting and enhanced error handling"""
        if params is None:
            params = {}

        # Apply rate limiting
        await exchange_rate_limiter.wait_if_needed("binance", self.rate_limit)

        if signed:
            if not self.api_key or not self.api_secret:
                raise AuthenticationError("API credentials not configured", "binance")

            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])
            params['signature'] = self._generate_signature(query_string)

        url = f"{self.base_url}{endpoint}"

        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=params, timeout=30)
            elif method.upper() == "POST":
                response = self.session.post(url, data=params, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Handle rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))
                raise RateLimitError("binance", retry_after)

            # Handle authentication errors
            if response.status_code == 401:
                raise AuthenticationError("Invalid API credentials", "binance", response.status_code)

            # Handle other HTTP errors
            if not response.ok:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass

                raise ExchangeError(
                    f"Binance API error: {response.status_code} - {response.text}",
                    "binance",
                    response.status_code,
                    error_data
                )

            return response.json()

        except requests.exceptions.Timeout:
            raise ExchangeError("Request timeout", "binance")
        except requests.exceptions.ConnectionError:
            raise ExchangeError("Connection error", "binance")
        except requests.exceptions.RequestException as e:
            logger.error(f"Binance API request failed: {e}")
            raise ExchangeError(f"Request failed: {e}", "binance")
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 500,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None) -> List[Dict]:
        """
        Fetch kline/candlestick data with validation and error handling

        Args:
            symbol: Trading pair symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            limit: Number of klines to fetch (max 1500)
            start_time: Start time for data range
            end_time: End time for data range

        Returns:
            List of OHLCV data dictionaries

        Raises:
            ExchangeError: If API request fails
            ValidationError: If parameters are invalid
        """
        # Validate inputs
        self._validate_klines_params(symbol, interval, limit, start_time, end_time)

        params = {
            'symbol': symbol.upper().strip(),
            'interval': interval,
            'limit': min(limit, 1500)  # Binance limit
        }

        if start_time:
            params['startTime'] = int(start_time.timestamp() * 1000)
        if end_time:
            params['endTime'] = int(end_time.timestamp() * 1000)

        try:
            # Use public API endpoint for klines (no authentication required)
            data = await self._make_request('/api/v3/klines', params, signed=False)

            if not isinstance(data, list):
                raise ExchangeError("Invalid response format from Binance", "binance")

            # Convert to standardized format with validation
            ohlcv_data = []
            for i, kline in enumerate(data):
                try:
                    if len(kline) < 6:
                        logger.warning(f"Incomplete kline data at index {i}: {kline}")
                        continue

                    ohlcv_data.append({
                        'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5])
                    })
                except (ValueError, IndexError) as e:
                    logger.warning(f"Invalid kline data at index {i}: {kline}, error: {e}")
                    continue

            logger.info(f"Fetched {len(ohlcv_data)} valid klines for {symbol} {interval}")
            return ohlcv_data

        except ExchangeError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching Binance klines: {e}")
            raise ExchangeError(f"Failed to fetch klines: {e}", "binance")

    def _validate_klines_params(self, symbol: str, interval: str, limit: int,
                               start_time: Optional[datetime], end_time: Optional[datetime]) -> None:
        """Validate klines parameters"""
        from app.core.exceptions import ValidationError

        if not symbol or not symbol.strip():
            raise ValidationError("Symbol cannot be empty")

        valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
        if interval not in valid_intervals:
            raise ValidationError(f"Invalid interval. Must be one of: {valid_intervals}")

        if limit <= 0 or limit > 1500:
            raise ValidationError("Limit must be between 1 and 1500")

        if start_time and end_time and start_time >= end_time:
            raise ValidationError("Start time must be before end time")
    
    async def get_exchange_info(self) -> Dict:
        """Get exchange trading rules and symbol information"""
        try:
            return await self._make_request('/fapi/v1/exchangeInfo')
        except ExchangeError:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch Binance exchange info: {e}")
            raise ExchangeError(f"Failed to fetch exchange info: {e}", "binance")

    async def get_24hr_ticker(self, symbol: Optional[str] = None) -> Union[Dict, List[Dict]]:
        """Get 24hr ticker price change statistics"""
        params = {}
        if symbol:
            if not symbol.strip():
                raise ValidationError("Symbol cannot be empty")
            params['symbol'] = symbol.upper().strip()

        try:
            return await self._make_request('/fapi/v1/ticker/24hr', params)
        except ExchangeError:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch Binance ticker: {e}")
            raise ExchangeError(f"Failed to fetch ticker: {e}", "binance")

    async def test_connection(self) -> bool:
        """Test API connection with proper error handling"""
        try:
            await self._make_request('/api/v3/ping', signed=False)
            logger.info("Binance connection test successful")
            return True
        except ExchangeError as e:
            logger.warning(f"Binance connection test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during Binance connection test: {e}")
            return False

    async def get_server_time(self) -> Dict:
        """Get server time for synchronization"""
        try:
            return await self._make_request('/api/v3/time', signed=False)
        except ExchangeError:
            raise
        except Exception as e:
            raise ExchangeError(f"Failed to get server time: {e}", "binance")

    def get_rate_limit_status(self) -> Dict:
        """Get current rate limit status"""
        limiter = exchange_rate_limiter.get_limiter("binance", self.rate_limit)
        return {
            "remaining_requests": limiter.get_remaining_requests(),
            "reset_time": limiter.get_reset_time(),
            "max_requests": self.rate_limit,
            "time_window": 60
        }
