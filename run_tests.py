#!/usr/bin/env python3
"""
Simple test runner wrapper for Strategy Builder
Delegates to the comprehensive test runner in scripts/
"""
import sys
import subprocess
from pathlib import Path

def main():
    """Run the comprehensive test runner"""
    script_path = Path(__file__).parent / "scripts" / "run_tests.py"
    
    # Pass all arguments to the actual test runner
    cmd = [sys.executable, str(script_path)] + sys.argv[1:]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("\n👋 Test run interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
