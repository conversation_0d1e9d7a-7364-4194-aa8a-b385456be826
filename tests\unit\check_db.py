#!/usr/bin/env python3

import mysql.connector
import json
from datetime import datetime

def check_marks():
    try:
        # Connect to database
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='@Oppa121089',
            database='strategy_builder'
        )

        cursor = conn.cursor(dictionary=True)

        # Get all marks
        cursor.execute('SELECT * FROM manual_marks ORDER BY created_at DESC LIMIT 10')
        marks = cursor.fetchall()

        print('📊 Current marks in database:')
        print('=' * 80)
        for mark in marks:
            print(f'ID: {mark["id"]}')
            print(f'  Type: {mark["mark_type"]}')
            print(f'  Side: {mark["entry_side"]}')
            print(f'  Price: {mark["price"]}')
            print(f'  Timestamp: {mark["timestamp"]}')
            print(f'  Linked Trade ID: {mark["linked_trade_id"]}')
            print(f'  Symbol: {mark["symbol"]}')
            print(f'  Timeframe: {mark["timeframe"]}')
            print('-' * 40)

        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    check_marks()
