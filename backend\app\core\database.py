"""
Database configuration and connection management using PyMySQL only
"""
import pymysql
from pymysql.cursors import Dict<PERSON>urs<PERSON>
from contextlib import contextmanager
import logging
from typing import Generator, Optional, Dict, Any, List
import time
from datetime import datetime

from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseError(Exception):
    """Custom database error"""
    pass

class ConnectionError(DatabaseError):
    """Database connection error"""
    pass

# Database connection configuration
DB_CONFIG = {
    'host': settings.DB_HOST,
    'port': settings.DB_PORT,
    'user': settings.DB_USERNAME,
    'password': settings.DB_PASSWORD.get_secret_value() if hasattr(settings.DB_PASSWORD, 'get_secret_value') else str(settings.DB_PASSWORD),
    'database': settings.DB_DATABASE,
    'charset': 'utf8mb4',
    'autocommit': False,
    'connect_timeout': 30,
    'read_timeout': 30,
    'write_timeout': 30
}

def get_connection():
    """Get a new database connection"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        logger.error(f"Failed to create database connection: {e}")
        raise ConnectionError(f"Database connection failed: {e}")

@contextmanager
def get_db_connection():
    """Context manager for database connections"""
    connection = None
    try:
        connection = get_connection()
        yield connection
        connection.commit()
    except Exception as e:
        if connection:
            connection.rollback()
        logger.error(f"Database transaction failed: {e}")
        raise DatabaseError(f"Database operation failed: {e}")
    finally:
        if connection:
            connection.close()

@contextmanager
def get_db_cursor(dict_cursor=True):
    """Context manager for database cursor"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor_class = DictCursor if dict_cursor else pymysql.cursors.Cursor
        cursor = connection.cursor(cursor_class)
        yield cursor
        connection.commit()
    except Exception as e:
        if connection:
            connection.rollback()
        logger.error(f"Database operation failed: {e}")
        raise DatabaseError(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

async def init_db() -> None:
    """Initialize database and create tables"""
    try:
        # Test connection first
        if not await test_connection():
            raise ConnectionError("Cannot establish database connection")

        # Create tables
        await create_tables()
        logger.info("Database initialized successfully")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise DatabaseError(f"Database initialization failed: {e}")

async def test_connection() -> bool:
    """Test database connection using PyMySQL with retry logic"""
    max_retries = 3
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            connection = pymysql.connect(**DB_CONFIG)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()

            if result and result[0] == 1:
                logger.info("PyMySQL database connection successful")
                return True
            else:
                logger.error("Database connection test failed - unexpected result")
                return False

        except Exception as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error(f"PyMySQL database connection failed after {max_retries} attempts: {e}")
                return False

    return False

async def create_tables() -> None:
    """Create database tables if they don't exist (preserves existing data)"""

    # No cleanup - we want to preserve existing data
    # Only create tables if they don't exist

    tables_sql = [
        """
        CREATE TABLE IF NOT EXISTS strategies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            symbol VARCHAR(20) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            exchange VARCHAR(20) DEFAULT 'binance',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            INDEX idx_symbol_timeframe (symbol, timeframe),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS ohlcv_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            strategy_id INT,
            symbol VARCHAR(20) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            timestamp DATETIME NOT NULL,
            open DECIMAL(20, 8) NOT NULL,
            high DECIMAL(20, 8) NOT NULL,
            low DECIMAL(20, 8) NOT NULL,
            close DECIMAL(20, 8) NOT NULL,
            volume DECIMAL(20, 8) NOT NULL,
            exchange VARCHAR(20) DEFAULT 'binance',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_ohlcv (symbol, timeframe, timestamp, exchange, strategy_id),
            INDEX idx_symbol_timeframe (symbol, timeframe),
            INDEX idx_timestamp (timestamp),
            INDEX idx_strategy (strategy_id),
            FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS indicator_presets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            display_name VARCHAR(200) NOT NULL,
            description TEXT,
            indicators JSON NOT NULL,
            is_public BOOLEAN DEFAULT FALSE,
            tags JSON,
            usage_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_public (is_public),
            INDEX idx_usage (usage_count),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS indicators_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            strategy_id INT,
            symbol VARCHAR(20) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            timestamp DATETIME NOT NULL,
            indicator_name VARCHAR(50) NOT NULL,
            indicator_config JSON,
            indicator_values JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_indicator (symbol, timeframe, timestamp, indicator_name, strategy_id),
            INDEX idx_symbol_timeframe_indicator (symbol, timeframe, indicator_name),
            INDEX idx_strategy (strategy_id),
            FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS manual_marks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL DEFAULT 'BTCUSDT',
            timeframe VARCHAR(10) NOT NULL DEFAULT '15m',
            mark_type ENUM('ENTRY', 'EXIT') NOT NULL,
            entry_side ENUM('BUY', 'SELL') NULL,
            timestamp DATETIME NOT NULL,
            price DECIMAL(20, 8) NOT NULL,
            indicator_snapshot JSON,
            ohlcv_snapshot JSON,
            linked_trade_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_symbol_timeframe (symbol, timeframe),
            INDEX idx_timestamp (timestamp),
            INDEX idx_mark_type (mark_type),
            INDEX idx_entry_side (entry_side),
            INDEX idx_linked_trade (linked_trade_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        """
        CREATE TABLE IF NOT EXISTS strategy_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            strategy_id INT NOT NULL,
            symbol VARCHAR(20) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            mark_id INT NOT NULL,
            entry_side ENUM('BUY', 'SELL') NOT NULL,
            profit_pct DECIMAL(10, 4) NOT NULL,
            entry_ohlcv JSON,
            exit_ohlcv JSON,
            entry_indicator_snapshot JSON,
            exit_indicator_snapshot JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_symbol_timeframe (symbol, timeframe),
            INDEX idx_profit (profit_pct),
            INDEX idx_strategy (strategy_id),
            FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE,
            FOREIGN KEY (mark_id) REFERENCES manual_marks(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
    ]

    try:
        with get_db_cursor(dict_cursor=False) as cursor:
            # Create tables only if they don't exist (preserves existing data)
            logger.info("Creating database tables if they don't exist...")
            for table_sql in tables_sql:
                if table_sql.strip():
                    try:
                        cursor.execute(table_sql)
                        logger.info(f"Table ensured: {table_sql[:50]}...")
                    except Exception as e:
                        logger.error(f"Failed to create table: {table_sql[:50]}... Error: {e}")
                        raise DatabaseError(f"Table creation failed: {e}")

        logger.info("Database tables ensured successfully")
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise DatabaseError(f"Table creation failed: {e}")

async def health_check() -> dict:
    """Comprehensive database health check"""
    start_time = time.time()

    try:
        # Test basic connection
        connection_ok = await test_connection()

        response_time = time.time() - start_time

        return {
            "status": "healthy" if connection_ok else "unhealthy",
            "connection": connection_ok,
            "response_time_ms": round(response_time * 1000, 2),
            "database": settings.DB_DATABASE,
            "host": settings.DB_HOST
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "response_time_ms": round((time.time() - start_time) * 1000, 2)
        }
