#!/usr/bin/env python3
"""
Test script to verify trade linking logic and coordinate plotting
"""

import sqlite3
from datetime import datetime
import json

def test_linking_logic():
    """Test the linking logic with current database data"""
    
    # Connect to database
    conn = sqlite3.connect('trading_data.db')
    conn.row_factory = sqlite3.Row  # Enable column access by name
    cursor = conn.cursor()
    
    try:
        # Get all marks
        cursor.execute("""
            SELECT id, mark_type, entry_side, timestamp, price, linked_trade_id, symbol, timeframe
            FROM manual_marks 
            ORDER BY timestamp
        """)
        
        marks = cursor.fetchall()
        
        print("📊 Current marks in database:")
        print("=" * 80)
        
        entry_marks = []
        exit_marks = []
        
        for mark in marks:
            mark_dict = dict(mark)
            print(f"ID: {mark_dict['id']}")
            print(f"  Type: {mark_dict['mark_type']}")
            print(f"  Side: {mark_dict['entry_side']}")
            print(f"  Price: {mark_dict['price']}")
            print(f"  Timestamp: {mark_dict['timestamp']}")
            print(f"  Linked Trade ID: {mark_dict['linked_trade_id']}")
            print(f"  Symbol: {mark_dict['symbol']}")
            print(f"  Timeframe: {mark_dict['timeframe']}")
            
            # Convert timestamp for coordinate calculation
            if mark_dict['timestamp']:
                dt = datetime.fromisoformat(mark_dict['timestamp'].replace('Z', '+00:00'))
                unix_timestamp = int(dt.timestamp())
                print(f"  Unix Timestamp: {unix_timestamp}")
                print(f"  Readable Time: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print('-' * 40)
            
            # Categorize marks
            if mark_dict['mark_type'] == 'ENTRY':
                entry_marks.append(mark_dict)
            elif mark_dict['mark_type'] == 'EXIT':
                exit_marks.append(mark_dict)
        
        print("\n🔗 Testing linking logic:")
        print("=" * 80)
        
        # Test the linking logic
        linked_pairs = []
        for entry_mark in entry_marks:
            # Find exit mark with matching linked_trade_id
            matching_exit = None
            for exit_mark in exit_marks:
                if (exit_mark['linked_trade_id'] == entry_mark['id'] and
                    exit_mark['symbol'] == entry_mark['symbol'] and
                    exit_mark['timeframe'] == entry_mark['timeframe']):
                    matching_exit = exit_mark
                    break
            
            if matching_exit:
                print(f"✅ LINKED: Entry {entry_mark['id']} ({entry_mark['entry_side']}) -> Exit {matching_exit['id']}")
                print(f"   Entry: {entry_mark['timestamp']} @ ${entry_mark['price']}")
                print(f"   Exit:  {matching_exit['timestamp']} @ ${matching_exit['price']}")
                
                # Calculate coordinates
                entry_dt = datetime.fromisoformat(entry_mark['timestamp'].replace('Z', '+00:00'))
                exit_dt = datetime.fromisoformat(matching_exit['timestamp'].replace('Z', '+00:00'))
                entry_unix = int(entry_dt.timestamp())
                exit_unix = int(exit_dt.timestamp())
                
                print(f"   Coordinates: ({entry_unix}, {entry_mark['price']}) -> ({exit_unix}, {matching_exit['price']})")
                
                # Calculate P&L
                if entry_mark['entry_side'] == 'BUY':
                    pnl = matching_exit['price'] - entry_mark['price']
                else:  # SELL
                    pnl = entry_mark['price'] - matching_exit['price']
                
                pnl_pct = (pnl / entry_mark['price']) * 100
                print(f"   P&L: ${pnl:.2f} ({pnl_pct:.2f}%)")
                
                linked_pairs.append((entry_mark, matching_exit))
            else:
                print(f"❌ UNLINKED: Entry {entry_mark['id']} ({entry_mark['entry_side']}) has no matching exit")
        
        print(f"\n📈 Summary:")
        print(f"   Total Entry marks: {len(entry_marks)}")
        print(f"   Total Exit marks: {len(exit_marks)}")
        print(f"   Linked pairs: {len(linked_pairs)}")
        print(f"   Unlinked entries: {len(entry_marks) - len(linked_pairs)}")
        
        # Check for orphaned exits
        orphaned_exits = []
        for exit_mark in exit_marks:
            found_entry = False
            for entry_mark in entry_marks:
                if exit_mark['linked_trade_id'] == entry_mark['id']:
                    found_entry = True
                    break
            if not found_entry:
                orphaned_exits.append(exit_mark)
        
        if orphaned_exits:
            print(f"   Orphaned exits: {len(orphaned_exits)}")
            for exit_mark in orphaned_exits:
                print(f"     Exit {exit_mark['id']} (linked_trade_id: {exit_mark['linked_trade_id']})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    test_linking_logic()
