#!/usr/bin/env python3
"""
Database Setup Script for Strategy Builder
"""
import sys
import os
from pathlib import Path
import pymysql
from pymysql import Error

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

def create_database():
    """Create the database if it doesn't exist"""
    try:
        from app.core.config import settings
        
        print("🔧 Setting up Strategy Builder Database...")
        
        # Connect to MySQL server using PyMySQL (without specifying database)
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            charset='utf8mb4',
            autocommit=False
        )
        
        cursor = connection.cursor()
        
        # Create database
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {settings.DB_DATABASE} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ Database '{settings.DB_DATABASE}' created/verified")
        
        # Use the database
        cursor.execute(f"USE {settings.DB_DATABASE}")
        
        cursor.close()
        connection.close()
        
        print("✅ Database setup completed successfully!")
        return True
        
    except Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_tables():
    """Create database tables using SQLAlchemy"""
    try:
        from app.core.database import engine, Base
        from app.models import ohlcv, indicators, trades
        
        print("📋 Creating database tables...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        print("✅ Database tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def test_connection():
    """Test database connection"""
    try:
        from app.core.database import test_connection
        import asyncio
        
        print("🔍 Testing database connection...")
        
        # Run async function
        result = asyncio.run(test_connection())
        
        if result:
            print("✅ Database connection test successful!")
        else:
            print("❌ Database connection test failed!")
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Strategy Builder Database Setup")
    print("=" * 40)
    
    # Step 1: Create database
    if not create_database():
        print("❌ Failed to create database. Please check your MySQL connection and credentials.")
        sys.exit(1)
    
    # Step 2: Create tables
    if not create_tables():
        print("❌ Failed to create tables. Please check the database connection.")
        sys.exit(1)
    
    # Step 3: Test connection
    if not test_connection():
        print("❌ Database connection test failed.")
        sys.exit(1)
    
    print("\n🎉 Database setup completed successfully!")
    print("You can now start the Strategy Builder server.")

if __name__ == "__main__":
    main()
