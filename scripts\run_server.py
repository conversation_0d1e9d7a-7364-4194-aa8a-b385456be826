#!/usr/bin/env python3
"""
Strategy Builder Server Startup Script
"""
import sys
import os
import uvicorn
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

def main():
    """Run the FastAPI server"""
    try:
        # Import after adding to path
        from app.core.config import settings
        
        print("🚀 Starting Strategy Builder Server...")
        print(f"📊 Host: {settings.HOST}")
        print(f"🔌 Port: {settings.PORT}")
        print(f"🐛 Debug: {settings.DEBUG}")
        print(f"💾 Database: {settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_DATABASE}")
        print("=" * 50)
        
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info" if not settings.DEBUG else "debug"
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
