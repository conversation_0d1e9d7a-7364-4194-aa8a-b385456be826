#!/usr/bin/env python3
"""
Verify the enhanced multi-indicator system database migration
"""

import sys
import os
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.core.database import get_db_cursor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_migration():
    """Verify that the migration was successful"""
    
    try:
        with get_db_cursor(dict_cursor=False) as cursor:
            logger.info("🔍 Verifying migration...")
            
            # Check if new tables exist
            tables_to_check = [
                'indicator_defaults',
                'strategy_indicator_configs'
            ]
            
            for table in tables_to_check:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                result = cursor.fetchone()
                if result:
                    logger.info(f"✅ Table '{table}' exists")
                    
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    result = cursor.fetchone()
                    count = result[0] if result and len(result) > 0 else 0
                    logger.info(f"   - {count} rows")
                else:
                    logger.error(f"❌ Table '{table}' not found")
                    return False
            
            # Check if indicators_data table has new columns
            cursor.execute("DESCRIBE indicators_data")
            columns = [row[0] for row in cursor.fetchall()]
            logger.info(f"📋 indicators_data columns: {columns}")
            
            required_columns = ['indicator_config', 'indicator_values', 'updated_at', 'strategy_id']
            for col in required_columns:
                if col in columns:
                    logger.info(f"✅ Column 'indicators_data.{col}' exists")
                else:
                    logger.warning(f"⚠️  Column 'indicators_data.{col}' not found")
            
            # Check if default indicators were inserted
            cursor.execute("SELECT indicator_name, display_name FROM indicator_defaults")
            defaults = cursor.fetchall()
            logger.info(f"✅ {len(defaults)} default indicators configured:")
            for default in defaults:
                logger.info(f"   - {default[0]}: {default[1]}")
            
            # Test the new API endpoints by checking if we can query the data
            cursor.execute("SELECT COUNT(*) FROM strategies")
            result = cursor.fetchone()
            strategies_count = result[0] if result and len(result) > 0 else 0
            logger.info(f"📊 {strategies_count} strategies in database")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Migration verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    logger.info("🚀 Starting migration verification...")
    
    # Verify migration
    if verify_migration():
        logger.info("🎉 Migration verification completed successfully!")
        logger.info("")
        logger.info("✅ Enhanced multi-indicator system is ready!")
        logger.info("")
        logger.info("Available indicators:")
        logger.info("- EMA (Exponential Moving Average)")
        logger.info("- SMA (Simple Moving Average)")
        logger.info("- RSI (Relative Strength Index)")
        logger.info("- MACD (Moving Average Convergence Divergence)")
        logger.info("- Bollinger Bands")
        logger.info("- Stochastic Oscillator")
        logger.info("- Volume SMA")
        logger.info("- ATR (Average True Range)")
        logger.info("")
        logger.info("Next steps:")
        logger.info("1. Test the new indicator API endpoints")
        logger.info("2. Update the frontend to use the new configuration system")
        logger.info("3. Test indicator calculations with the new engine")
    else:
        logger.error("❌ Migration verification failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
