#!/usr/bin/env python3
"""
Check OHLCV data for specific timeframes used in marks
"""
import mysql.connector
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def check_specific_timeframe():
    """Check OHLCV data for specific timeframes"""
    print("🔍 Checking OHLCV data for mark timeframes...")
    
    try:
        with get_db_cursor() as cursor:
            # Check data around January 2024 (when marks were created)
            print("\n📊 OHLCV data around January 2024:")
            cursor.execute("""
                SELECT timestamp, open, high, low, close, volume
                FROM ohlcv_data 
                WHERE timestamp BETWEEN '2024-01-05 00:00:00' AND '2024-01-06 00:00:00'
                ORDER BY timestamp ASC
                LIMIT 10
            """)
            jan_data = cursor.fetchall()
            
            if jan_data:
                for row in jan_data:
                    print(f"  {row['timestamp']}: O:{row['open']} H:{row['high']} L:{row['low']} C:{row['close']} V:{row['volume']}")
            else:
                print("  No data found for January 2024")
            
            # Check the exact timestamps from recent marks
            print("\n📋 Checking data for exact mark timestamps:")
            mark_timestamps = [
                '2024-01-05 23:00:00',  # From mark ID 66
                '2024-01-10 23:45:00',  # From mark ID 65
                '2024-01-10 14:45:00'   # From mark ID 64
            ]
            
            for ts in mark_timestamps:
                cursor.execute("""
                    SELECT timestamp, open, high, low, close, volume
                    FROM ohlcv_data 
                    WHERE timestamp = %s
                """, (ts,))
                exact_data = cursor.fetchone()
                
                if exact_data:
                    print(f"  ✅ {ts}: V:{exact_data['volume']}")
                else:
                    # Check nearby data
                    cursor.execute("""
                        SELECT timestamp, open, high, low, close, volume
                        FROM ohlcv_data 
                        WHERE timestamp BETWEEN %s - INTERVAL 1 HOUR AND %s + INTERVAL 1 HOUR
                        ORDER BY ABS(TIMESTAMPDIFF(SECOND, timestamp, %s))
                        LIMIT 1
                    """, (ts, ts, ts))
                    nearby_data = cursor.fetchone()
                    
                    if nearby_data:
                        print(f"  🔍 {ts}: No exact match, closest is {nearby_data['timestamp']} V:{nearby_data['volume']}")
                    else:
                        print(f"  ❌ {ts}: No data found")
            
            # Check data range
            cursor.execute("SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM ohlcv_data")
            range_data = cursor.fetchone()
            print(f"\n📈 Data range: {range_data['earliest']} to {range_data['latest']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_specific_timeframe()
