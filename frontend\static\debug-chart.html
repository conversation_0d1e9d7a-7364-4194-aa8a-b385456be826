<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug TradingView Chart</title>
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #131722;
            color: #d1d4dc;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .debug-info {
            background: #1e222d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .chart-container {
            width: 100%;
            height: 600px;
            background: #131722;
            border: 1px solid #363c4e;
            border-radius: 8px;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #1e88e5;
        }
        
        .status {
            color: #26a69a;
        }
        
        .error {
            color: #ef5350;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>TradingView Chart Debug</h1>
        
        <div class="debug-info" id="debug-info">
            <div>Status: <span id="status" class="status">Initializing...</span></div>
            <div>Chart: <span id="chart-status">Not created</span></div>
            <div>Data: <span id="data-status">No data</span></div>
            <div>WebSocket: <span id="ws-status">Disconnected</span></div>
            <div>Errors: <span id="error-count">0</span></div>
            <div>Last Update: <span id="last-update">Never</span></div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="testChart()">Test Chart</button>
            <button class="btn" onclick="loadSampleData()">Load Sample Data</button>
            <button class="btn" onclick="testWebSocket()">Test WebSocket</button>
            <button class="btn" onclick="clearErrors()">Clear Errors</button>
        </div>
        
        <div class="chart-container" id="chart"></div>
        
        <div class="debug-info" id="console-output">
            <strong>Console Output:</strong>
            <div id="console-log"></div>
        </div>
    </div>

    <script src="js/tradingview-chart.js"></script>
    <script>
        let debugChart = null;
        let errorCount = 0;
        let consoleLog = [];
        
        // Override console methods to capture output
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            debug: console.debug
        };
        
        function logToDebug(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            consoleLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            if (consoleLog.length > 50) consoleLog.shift(); // Keep last 50 messages
            
            updateConsoleOutput();
            
            if (type === 'error') {
                errorCount++;
                document.getElementById('error-count').textContent = errorCount;
            }
            
            // Call original console method
            originalConsole[type](...args);
        }
        
        console.log = (...args) => logToDebug('log', ...args);
        console.error = (...args) => logToDebug('error', ...args);
        console.warn = (...args) => logToDebug('warn', ...args);
        console.debug = (...args) => logToDebug('debug', ...args);
        
        function updateConsoleOutput() {
            const output = document.getElementById('console-log');
            output.innerHTML = consoleLog.slice(-10).map(msg => `<div>${msg}</div>`).join('');
            output.scrollTop = output.scrollHeight;
        }
        
        function updateStatus(message, type = 'status') {
            document.getElementById('status').textContent = message;
            document.getElementById('status').className = type;
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }
        
        function testChart() {
            try {
                updateStatus('Creating chart...');
                document.getElementById('chart-status').textContent = 'Creating...';
                
                if (debugChart) {
                    debugChart.destroy();
                }
                
                debugChart = new TradingViewChart('chart', {
                    symbol: 'BTCUSDT',
                    interval: '1m',
                    theme: 'dark',
                    enableWebSocket: false // Disable WebSocket for testing
                });
                
                document.getElementById('chart-status').textContent = 'Created';
                updateStatus('Chart created successfully');
                
                // Test with sample data
                setTimeout(() => {
                    debugChart.loadSampleData();
                    document.getElementById('data-status').textContent = 'Sample data loaded';
                    updateStatus('Sample data loaded');
                }, 1000);
                
            } catch (error) {
                console.error('Error creating chart:', error);
                updateStatus('Error creating chart', 'error');
                document.getElementById('chart-status').textContent = 'Error';
            }
        }
        
        function loadSampleData() {
            if (!debugChart) {
                updateStatus('Chart not created', 'error');
                return;
            }
            
            try {
                debugChart.loadSampleData();
                document.getElementById('data-status').textContent = 'Sample data loaded';
                updateStatus('Sample data loaded');
            } catch (error) {
                console.error('Error loading sample data:', error);
                updateStatus('Error loading sample data', 'error');
            }
        }
        
        function testWebSocket() {
            if (!debugChart) {
                updateStatus('Chart not created', 'error');
                return;
            }
            
            try {
                debugChart.config.enableWebSocket = true;
                debugChart.connectWebSocket();
                document.getElementById('ws-status').textContent = 'Connecting...';
                updateStatus('Testing WebSocket connection...');
            } catch (error) {
                console.error('Error testing WebSocket:', error);
                updateStatus('WebSocket test failed', 'error');
                document.getElementById('ws-status').textContent = 'Error';
            }
        }
        
        function clearErrors() {
            errorCount = 0;
            consoleLog = [];
            document.getElementById('error-count').textContent = '0';
            updateConsoleOutput();
            updateStatus('Errors cleared');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('Page loaded, ready for testing');
            
            // Check if TradingView library is loaded
            if (typeof LightweightCharts !== 'undefined') {
                console.log('TradingView library loaded successfully');
                document.getElementById('chart-status').textContent = 'Library loaded';
            } else {
                console.error('TradingView library not loaded');
                document.getElementById('chart-status').textContent = 'Library missing';
            }
        });
        
        // Monitor WebSocket status
        setInterval(() => {
            if (debugChart && debugChart.websocket) {
                const wsState = debugChart.websocket.readyState;
                const states = ['Connecting', 'Open', 'Closing', 'Closed'];
                document.getElementById('ws-status').textContent = states[wsState] || 'Unknown';
            }
        }, 1000);
    </script>
</body>
</html>
