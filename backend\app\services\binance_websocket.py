"""
Binance WebSocket Client for Real-time Data Streaming
Supports candlestick data, ticker updates, and order book streams
"""
import asyncio
import json
import logging
import websockets
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import time
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class KlineData:
    """Kline/Candlestick data structure"""
    symbol: str
    open_time: int
    close_time: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    number_of_trades: int
    is_closed: bool
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format"""
        return {
            'symbol': self.symbol,
            'timestamp': self.open_time / 1000,  # Convert to seconds
            'open': self.open_price,
            'high': self.high_price,
            'low': self.low_price,
            'close': self.close_price,
            'volume': self.volume,
            'trades': self.number_of_trades,
            'is_closed': self.is_closed
        }


@dataclass
class TickerData:
    """24hr ticker data structure"""
    symbol: str
    price_change: float
    price_change_percent: float
    last_price: float
    volume: float
    high_price: float
    low_price: float
    
    def to_dict(self) -> Dict:
        """Convert to dictionary format"""
        return {
            'symbol': self.symbol,
            'price': self.last_price,
            'change': self.price_change,
            'change_percent': self.price_change_percent,
            'volume': self.volume,
            'high': self.high_price,
            'low': self.low_price
        }


class BinanceWebSocketClient:
    """
    Binance WebSocket client for real-time data streaming
    Supports multiple streams and automatic reconnection
    """
    
    def __init__(self, testnet: bool = False):
        self.testnet = testnet
        self.base_url = "wss://fstream.binance.com/ws/" if not testnet else "wss://stream.binancefuture.com/ws/"
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.callbacks: Dict[str, List[Callable]] = {}
        self.running = False
        self.reconnect_delay = 5
        self.max_reconnect_attempts = 10
        
    async def connect_kline_stream(self, symbol: str, interval: str, callback: Callable[[KlineData], None]):
        """
        Connect to kline/candlestick stream
        
        Args:
            symbol: Trading symbol (e.g., 'btcusdt')
            interval: Kline interval (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
            callback: Function to call when new kline data is received
        """
        stream_name = f"{symbol.lower()}@kline_{interval}"
        url = f"{self.base_url}{stream_name}"
        
        if stream_name not in self.callbacks:
            self.callbacks[stream_name] = []
        self.callbacks[stream_name].append(callback)
        
        await self._connect_stream(stream_name, url, self._handle_kline_message)
        
    async def connect_ticker_stream(self, symbol: str, callback: Callable[[TickerData], None]):
        """
        Connect to 24hr ticker stream
        
        Args:
            symbol: Trading symbol (e.g., 'btcusdt')
            callback: Function to call when ticker data is received
        """
        stream_name = f"{symbol.lower()}@ticker"
        url = f"{self.base_url}{stream_name}"
        
        if stream_name not in self.callbacks:
            self.callbacks[stream_name] = []
        self.callbacks[stream_name].append(callback)
        
        await self._connect_stream(stream_name, url, self._handle_ticker_message)
        
    async def connect_multiple_streams(self, streams: List[str], callback: Callable[[Dict], None]):
        """
        Connect to multiple streams using combined stream
        
        Args:
            streams: List of stream names (e.g., ['btcusdt@kline_1m', 'ethusdt@kline_1m'])
            callback: Function to call when data is received
        """
        stream_name = "combined"
        streams_param = "/".join(streams)
        url = f"wss://fstream.binance.com/stream?streams={streams_param}"
        
        if stream_name not in self.callbacks:
            self.callbacks[stream_name] = []
        self.callbacks[stream_name].append(callback)
        
        await self._connect_stream(stream_name, url, self._handle_combined_message)
        
    async def _connect_stream(self, stream_name: str, url: str, message_handler: Callable):
        """Connect to a WebSocket stream with reconnection logic"""
        reconnect_attempts = 0
        
        while reconnect_attempts < self.max_reconnect_attempts:
            try:
                logger.info(f"Connecting to {stream_name} stream: {url}")
                
                async with websockets.connect(
                    url,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ) as websocket:
                    self.connections[stream_name] = websocket
                    logger.info(f"Connected to {stream_name} stream")
                    reconnect_attempts = 0  # Reset on successful connection
                    
                    async for message in websocket:
                        try:
                            await message_handler(stream_name, message)
                        except Exception as e:
                            logger.error(f"Error handling message for {stream_name}: {e}")
                            
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"Connection closed for {stream_name}")
                break
            except Exception as e:
                reconnect_attempts += 1
                logger.error(f"Connection error for {stream_name} (attempt {reconnect_attempts}): {e}")
                
                if reconnect_attempts < self.max_reconnect_attempts:
                    logger.info(f"Reconnecting in {self.reconnect_delay} seconds...")
                    await asyncio.sleep(self.reconnect_delay)
                else:
                    logger.error(f"Max reconnection attempts reached for {stream_name}")
                    break
                    
        # Clean up
        if stream_name in self.connections:
            del self.connections[stream_name]
            
    async def _handle_kline_message(self, stream_name: str, message: str):
        """Handle kline/candlestick messages"""
        try:
            data = json.loads(message)
            kline_data = data.get('k')
            
            if not kline_data:
                return
                
            kline = KlineData(
                symbol=kline_data['s'],
                open_time=kline_data['t'],
                close_time=kline_data['T'],
                open_price=float(kline_data['o']),
                high_price=float(kline_data['h']),
                low_price=float(kline_data['l']),
                close_price=float(kline_data['c']),
                volume=float(kline_data['v']),
                number_of_trades=kline_data['n'],
                is_closed=kline_data['x']
            )
            
            # Call all registered callbacks
            for callback in self.callbacks.get(stream_name, []):
                try:
                    await callback(kline) if asyncio.iscoroutinefunction(callback) else callback(kline)
                except Exception as e:
                    logger.error(f"Error in kline callback: {e}")
                    
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in kline message: {e}")
        except Exception as e:
            logger.error(f"Error processing kline message: {e}")
            
    async def _handle_ticker_message(self, stream_name: str, message: str):
        """Handle ticker messages"""
        try:
            data = json.loads(message)
            
            ticker = TickerData(
                symbol=data['s'],
                price_change=float(data['p']),
                price_change_percent=float(data['P']),
                last_price=float(data['c']),
                volume=float(data['v']),
                high_price=float(data['h']),
                low_price=float(data['l'])
            )
            
            # Call all registered callbacks
            for callback in self.callbacks.get(stream_name, []):
                try:
                    await callback(ticker) if asyncio.iscoroutinefunction(callback) else callback(ticker)
                except Exception as e:
                    logger.error(f"Error in ticker callback: {e}")
                    
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in ticker message: {e}")
        except Exception as e:
            logger.error(f"Error processing ticker message: {e}")
            
    async def _handle_combined_message(self, stream_name: str, message: str):
        """Handle combined stream messages"""
        try:
            data = json.loads(message)
            
            # Call all registered callbacks with raw data
            for callback in self.callbacks.get(stream_name, []):
                try:
                    await callback(data) if asyncio.iscoroutinefunction(callback) else callback(data)
                except Exception as e:
                    logger.error(f"Error in combined stream callback: {e}")
                    
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in combined message: {e}")
        except Exception as e:
            logger.error(f"Error processing combined message: {e}")
            
    async def disconnect_stream(self, stream_name: str):
        """Disconnect from a specific stream"""
        if stream_name in self.connections:
            await self.connections[stream_name].close()
            del self.connections[stream_name]
            logger.info(f"Disconnected from {stream_name} stream")
            
    async def disconnect_all(self):
        """Disconnect from all streams"""
        for stream_name in list(self.connections.keys()):
            await self.disconnect_stream(stream_name)
        self.callbacks.clear()
        logger.info("Disconnected from all streams")
        
    def is_connected(self, stream_name: str) -> bool:
        """Check if a stream is connected"""
        return stream_name in self.connections and not self.connections[stream_name].closed
