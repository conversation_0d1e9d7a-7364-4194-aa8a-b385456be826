#!/usr/bin/env python3
"""
Check if volume data exists in OHLCV data
"""
import mysql.connector
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def check_ohlcv_volume():
    """Check if volume data exists in OHLCV data"""
    print("🔍 Checking volume data in OHLCV data...")
    
    try:
        with get_db_cursor() as cursor:
            # Check recent OHLCV data
            print("\n📊 Recent OHLCV data with volume:")
            cursor.execute("""
                SELECT symbol, timeframe, timestamp, open, high, low, close, volume
                FROM ohlcv_data 
                ORDER BY timestamp DESC 
                LIMIT 10
            """)
            recent_ohlcv = cursor.fetchall()
            
            if not recent_ohlcv:
                print("  No OHLCV data found in database")
                return
            
            for row in recent_ohlcv:
                print(f"  {row['timestamp']}: {row['symbol']} O:{row['open']} H:{row['high']} L:{row['low']} C:{row['close']} V:{row['volume']}")
            
            # Check volume statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN volume > 0 THEN 1 END) as records_with_volume,
                    AVG(volume) as avg_volume,
                    MAX(volume) as max_volume,
                    MIN(volume) as min_volume
                FROM ohlcv_data
            """)
            stats = cursor.fetchone()
            
            print(f"\n📈 Volume Statistics:")
            print(f"  Total records: {stats['total_records']}")
            print(f"  Records with volume > 0: {stats['records_with_volume']}")
            print(f"  Average volume: {stats['avg_volume']}")
            print(f"  Max volume: {stats['max_volume']}")
            print(f"  Min volume: {stats['min_volume']}")
            
            # Check specific time range that might be used for marks
            print(f"\n📊 Sample data from January 2024 (recent mark timeframe):")
            cursor.execute("""
                SELECT symbol, timestamp, open, high, low, close, volume
                FROM ohlcv_data 
                WHERE timestamp >= '2024-01-01' AND timestamp <= '2024-01-31'
                ORDER BY timestamp DESC 
                LIMIT 5
            """)
            jan_data = cursor.fetchall()
            
            for row in jan_data:
                print(f"  {row['timestamp']}: V:{row['volume']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_ohlcv_volume()
