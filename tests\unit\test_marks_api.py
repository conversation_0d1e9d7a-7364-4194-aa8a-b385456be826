"""
Comprehensive test suite for marks API endpoints
Tests entry mark creation, exit mark data handling, and error scenarios
"""

import pytest
import json
from datetime import datetime
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Import the FastAPI app
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app
from app.schemas.marks import MarkEntry, MarkExit

client = TestClient(app)

class TestMarksAPI:
    """Test suite for marks API endpoints"""
    
    @pytest.fixture
    def valid_entry_data(self):
        """Valid entry mark data for testing"""
        return {
            "timestamp": 1627849200.0,
            "price": 45000.50,
            "side": "buy",
            "quantity": 0.1,
            "notes": "Test entry mark",
            "ohlcv_data": {
                "time": 1627849200,
                "open": 44950.0,
                "high": 45100.0,
                "low": 44900.0,
                "close": 45000.0,
                "volume": 1500.0
            },
            "indicator_data": {
                "ema": {"ema_20": 44800.0, "ema_50": 44600.0},
                "rsi": {"rsi_14": 65.5},
                "macd": {"macd": 120.5, "signal": 110.2}
            }
        }
    
    @pytest.fixture
    def valid_exit_data(self):
        """Valid exit mark data for testing"""
        return {
            "entry_id": 1,
            "timestamp": 1627935600.0,
            "price": 46500.75,
            "quantity": 0.1,
            "notes": "Test exit mark",
            "ohlcv_data": {
                "time": 1627935600,
                "open": 46400.0,
                "high": 46600.0,
                "low": 46350.0,
                "close": 46500.0,
                "volume": 1200.0
            },
            "indicator_data": {
                "ema": {"ema_20": 45900.0, "ema_50": 45700.0},
                "rsi": {"rsi_14": 75.2},
                "macd": {"macd": 150.8, "signal": 140.5}
            }
        }

    def test_entry_mark_schema_validation(self, valid_entry_data):
        """Test entry mark schema validation"""
        # Test valid data
        entry = MarkEntry(**valid_entry_data)
        assert entry.side == "buy"
        assert entry.price == 45000.50
        assert entry.quantity == 0.1
        
        # Test invalid side
        invalid_data = valid_entry_data.copy()
        invalid_data["side"] = "invalid"
        with pytest.raises(ValueError):
            MarkEntry(**invalid_data)
        
        # Test negative quantity
        invalid_data = valid_entry_data.copy()
        invalid_data["quantity"] = -1
        with pytest.raises(ValueError):
            MarkEntry(**invalid_data)

    def test_exit_mark_schema_validation(self, valid_exit_data):
        """Test exit mark schema validation"""
        # Test valid data
        exit_mark = MarkExit(**valid_exit_data)
        assert exit_mark.entry_id == 1
        assert exit_mark.price == 46500.75
        assert exit_mark.quantity == 0.1
        
        # Test negative quantity
        invalid_data = valid_exit_data.copy()
        invalid_data["quantity"] = -1
        with pytest.raises(ValueError):
            MarkExit(**invalid_data)

    @patch('app.api.marks.get_db_cursor')
    def test_create_entry_mark_success(self, mock_cursor, valid_entry_data):
        """Test successful entry mark creation"""
        # Mock database cursor
        mock_cursor_instance = MagicMock()
        mock_cursor_instance.__enter__.return_value = mock_cursor_instance
        mock_cursor_instance.lastrowid = 123
        mock_cursor.return_value = mock_cursor_instance
        
        response = client.post("/api/v1/marks/entry", json=valid_entry_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Entry mark created successfully"
        assert data["data"]["id"] == 123
        assert data["data"]["entry_side"] == "BUY"

    @patch('app.api.marks.get_db_cursor')
    def test_create_entry_mark_invalid_side(self, mock_cursor):
        """Test entry mark creation with invalid side"""
        invalid_data = {
            "timestamp": 1627849200.0,
            "price": 45000.50,
            "side": "",  # Empty side
            "quantity": 0.1,
            "notes": "Test entry mark"
        }
        
        response = client.post("/api/v1/marks/entry", json=invalid_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Side field is required" in data["detail"]

    @patch('app.api.marks.get_db_cursor')
    def test_create_exit_mark_success(self, mock_cursor, valid_exit_data):
        """Test successful exit mark creation"""
        # Mock database cursor
        mock_cursor_instance = MagicMock()
        mock_cursor_instance.__enter__.return_value = mock_cursor_instance
        mock_cursor_instance.lastrowid = 456
        mock_cursor.return_value = mock_cursor_instance
        
        response = client.post("/api/v1/marks/exit", json=valid_exit_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Exit mark created successfully"
        assert data["data"]["id"] == 456
        assert data["data"]["linked_trade_id"] == 1

    def test_ohlcv_data_validation(self, valid_entry_data):
        """Test OHLCV data structure validation"""
        ohlcv_data = valid_entry_data["ohlcv_data"]
        
        # Check required fields
        required_fields = ["time", "open", "high", "low", "close", "volume"]
        for field in required_fields:
            assert field in ohlcv_data
            assert isinstance(ohlcv_data[field], (int, float))
        
        # Check logical constraints
        assert ohlcv_data["high"] >= ohlcv_data["open"]
        assert ohlcv_data["high"] >= ohlcv_data["close"]
        assert ohlcv_data["low"] <= ohlcv_data["open"]
        assert ohlcv_data["low"] <= ohlcv_data["close"]
        assert ohlcv_data["volume"] >= 0

    def test_indicator_data_validation(self, valid_entry_data):
        """Test indicator data structure validation"""
        indicator_data = valid_entry_data["indicator_data"]
        
        # Check EMA data
        assert "ema" in indicator_data
        ema_data = indicator_data["ema"]
        assert "ema_20" in ema_data
        assert "ema_50" in ema_data
        assert isinstance(ema_data["ema_20"], (int, float))
        assert isinstance(ema_data["ema_50"], (int, float))
        
        # Check RSI data
        assert "rsi" in indicator_data
        rsi_data = indicator_data["rsi"]
        assert "rsi_14" in rsi_data
        assert 0 <= rsi_data["rsi_14"] <= 100
        
        # Check MACD data
        assert "macd" in indicator_data
        macd_data = indicator_data["macd"]
        assert "macd" in macd_data
        assert "signal" in macd_data
        assert isinstance(macd_data["macd"], (int, float))
        assert isinstance(macd_data["signal"], (int, float))

    @patch('app.api.marks.get_db_cursor')
    def test_database_error_handling(self, mock_cursor, valid_entry_data):
        """Test database error handling"""
        # Mock database error
        mock_cursor.side_effect = Exception("Database connection failed")
        
        response = client.post("/api/v1/marks/entry", json=valid_entry_data)
        
        assert response.status_code == 500
        data = response.json()
        assert "Error creating entry mark" in data["detail"]

    def test_timestamp_handling(self, valid_entry_data):
        """Test timestamp format handling"""
        # Test Unix timestamp
        entry_data = valid_entry_data.copy()
        entry_data["timestamp"] = 1627849200.0
        entry = MarkEntry(**entry_data)
        assert entry.timestamp == 1627849200.0
        
        # Test timestamp validation in API
        response = client.post("/api/v1/marks/entry", json=entry_data)
        # Should not fail due to timestamp format

    def test_price_validation(self, valid_entry_data):
        """Test price validation"""
        # Test valid price
        entry_data = valid_entry_data.copy()
        entry_data["price"] = 45000.50
        entry = MarkEntry(**entry_data)
        assert entry.price == 45000.50
        
        # Test zero price (should be allowed)
        entry_data["price"] = 0.0
        entry = MarkEntry(**entry_data)
        assert entry.price == 0.0
        
        # Test negative price (should be allowed for some use cases)
        entry_data["price"] = -100.0
        entry = MarkEntry(**entry_data)
        assert entry.price == -100.0

    def test_comprehensive_data_structure(self, valid_entry_data, valid_exit_data):
        """Test comprehensive data structure for complete trade"""
        # Simulate a complete trade cycle
        entry_comprehensive = {
            "symbol": "BTCUSDT",
            "timeframe": "15m",
            "entry": {
                "timestamp": datetime.fromtimestamp(valid_entry_data["timestamp"]).isoformat(),
                "entry_side": "Buy",
                "price": valid_entry_data["price"],
                "ohlcv": valid_entry_data["ohlcv_data"],
                "indicators": valid_entry_data["indicator_data"]
            }
        }
        
        exit_comprehensive = {
            "symbol": "BTCUSDT",
            "timeframe": "15m",
            "entry": entry_comprehensive["entry"],
            "exit": {
                "timestamp": datetime.fromtimestamp(valid_exit_data["timestamp"]).isoformat(),
                "unix_timestamp": valid_exit_data["timestamp"],
                "price": valid_exit_data["price"],
                "ohlcv": valid_exit_data["ohlcv_data"],
                "indicators": valid_exit_data["indicator_data"],
                "data_quality": {
                    "ohlcv_available": True,
                    "indicators_available": True,
                    "timestamp_valid": True,
                    "price_valid": True
                }
            },
            "pnl": {
                "absolute": (valid_exit_data["price"] - valid_entry_data["price"]) * valid_exit_data["quantity"],
                "percentage": ((valid_exit_data["price"] - valid_entry_data["price"]) / valid_entry_data["price"]) * 100,
                "quantity": valid_exit_data["quantity"],
                "entry_price": valid_entry_data["price"],
                "exit_price": valid_exit_data["price"],
                "side": "buy"
            }
        }
        
        # Validate structure
        assert "entry" in exit_comprehensive
        assert "exit" in exit_comprehensive
        assert "pnl" in exit_comprehensive
        assert "data_quality" in exit_comprehensive["exit"]
        
        # Validate PnL calculation
        expected_pnl = (46500.75 - 45000.50) * 0.1
        assert abs(exit_comprehensive["pnl"]["absolute"] - expected_pnl) < 0.01

    @patch('app.api.marks.get_db_cursor')
    def test_side_case_handling(self, mock_cursor):
        """Test side field case handling"""
        # Mock database cursor
        mock_cursor_instance = MagicMock()
        mock_cursor_instance.__enter__.return_value = mock_cursor_instance
        mock_cursor_instance.lastrowid = 789
        mock_cursor.return_value = mock_cursor_instance
        
        test_cases = [
            {"side": "buy", "expected": "BUY"},
            {"side": "BUY", "expected": "BUY"},
            {"side": "Buy", "expected": "BUY"},
            {"side": "sell", "expected": "SELL"},
            {"side": "SELL", "expected": "SELL"},
            {"side": "Sell", "expected": "SELL"}
        ]
        
        for case in test_cases:
            entry_data = {
                "timestamp": 1627849200.0,
                "price": 45000.50,
                "side": case["side"],
                "quantity": 0.1,
                "notes": "Test case sensitivity"
            }
            
            response = client.post("/api/v1/marks/entry", json=entry_data)
            assert response.status_code == 200
            data = response.json()
            assert data["data"]["entry_side"] == case["expected"]

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
