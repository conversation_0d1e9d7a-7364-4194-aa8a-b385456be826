# Strategy Builder - Project Structure

This document describes the organized file and folder structure of the Strategy Builder project.

## 📁 Root Directory Structure

```
Strategy_builder/
├── backend/                 # FastAPI backend application
├── frontend/               # Web frontend (HTML, CSS, JS)
├── database/              # Database scripts and migrations
├── config/                # Configuration files
├── scripts/               # Utility and maintenance scripts
├── docs/                  # All documentation files
├── tests/                 # Comprehensive test suite
├── requirements.txt       # Python dependencies
├── start.py              # Quick start script
├── README.md             # Main project documentation
└── trading_data.db       # SQLite database file
```

## 🔧 Backend Structure

```
backend/
├── app/
│   ├── api/              # API endpoint routers
│   │   ├── indicators.py
│   │   ├── marks.py
│   │   ├── ohlcv.py
│   │   ├── strategies.py
│   │   ├── trades.py
│   │   └── websocket.py
│   ├── core/             # Core configuration and database
│   │   ├── config.py
│   │   ├── database.py
│   │   └── exceptions.py
│   ├── models/           # SQLAlchemy database models
│   │   ├── indicators.py
│   │   ├── ohlcv.py
│   │   └── trades.py
│   ├── schemas/          # Pydantic schemas for API
│   │   ├── indicators.py
│   │   ├── marks.py
│   │   ├── ohlcv.py
│   │   └── trades.py
│   ├── services/         # Business logic and external APIs
│   │   ├── binance_client.py
│   │   ├── indicator_config_manager.py
│   │   ├── mexc_client.py
│   │   └── multi_indicator_engine.py
│   ├── utils/            # Utility functions
│   └── main.py           # FastAPI application entry point
└── api/                  # Legacy API structure (to be cleaned up)
```

## 🎨 Frontend Structure

```
frontend/
├── static/
│   ├── css/              # Stylesheets
│   │   ├── main.css
│   │   ├── chart.css
│   │   └── multi-indicator-config.css
│   └── js/               # JavaScript modules
│       ├── chart-manager.js
│       ├── marking-tools.js
│       ├── multi-indicator-config.js
│       ├── advanced-indicator-plotter.js
│       └── tests/        # Frontend test files
└── templates/            # HTML templates
    ├── index.html
    └── chart.html
```

## 🗄️ Database Structure

```
database/
├── init_db.sql           # Initial database schema
├── migrations/           # Database migration scripts
│   ├── 001_initial_schema.sql
│   ├── 002_enhance_indicators_schema_fixed.sql
│   └── recreate_marks_table.sql
└── seeds/                # Sample data for development
```

## ⚙️ Scripts Directory

```
scripts/
├── check_marks_volume.py      # Volume data validation
├── check_ohlcv_volume.py      # OHLCV volume checks
├── check_specific_timeframe.py # Timeframe validation
├── check_table.py             # Database table checks
├── check_table_structure.py   # Table structure validation
├── debug_macd_issue.py        # MACD debugging
├── fix_database_constraints.py # Database constraint fixes
├── fix_ohlcv_table.py         # OHLCV table repairs
├── migrate_database.py        # Database migration runner
├── run_indicator_migration.py # Indicator migration
├── run_server.py              # Server startup script
├── run_tests.py               # Comprehensive test runner
├── setup_database.py          # Database setup
├── validate_config.py         # Configuration validation
└── verify_migration.py        # Migration verification
```

## 📚 Documentation Structure

```
docs/
├── DEPLOYMENT.md                           # Deployment guide
├── ENHANCED_TOOLTIP_DOCUMENTATION.md      # Tooltip feature docs
├── ENHANCED_TRADE_LINES_SUMMARY.md        # Trade lines feature
├── IMPLEMENTATION_COMPLETE_GUIDE.md       # Implementation guide
├── INFINITE_HISTORY_IMPLEMENTATION.md     # History feature
├── MULTI_INDICATOR_IMPLEMENTATION_SUMMARY.md # Multi-indicator docs
├── MULTI_INDICATOR_SYSTEM_ARCHITECTURE.md # Architecture docs
├── MULTI_PANEL_CHART_GUIDE.md            # Chart panel guide
├── PROJECT_STRUCTURE.md                   # This document
├── TRADE_LINES_IMPLEMENTATION_STATUS.md   # Trade lines status
├── TRADINGVIEW_BINANCE_INTEGRATION.md     # Integration guide
├── TRADINGVIEW_STYLE_GUIDE.md            # Style guide
└── requirement.md                         # Requirements document
```

## 🧪 Test Structure

```
tests/
├── __init__.py           # Test package initialization
├── conftest.py          # Pytest configuration and fixtures
├── unit/                # Unit tests for individual components
│   ├── __init__.py
│   ├── check_db.py      # Database connection tests
│   ├── test_db.py       # Database functionality tests
│   ├── test_indicators.py # Indicator tests
│   ├── test_marks_api.py  # Marks API tests
│   ├── test_pymysql.py    # MySQL connection tests
│   └── test_validators.py # Validation tests
├── integration/         # Integration tests for API endpoints
│   ├── __init__.py
│   ├── test_api.py      # General API tests
│   ├── test_api_volume.py # Volume API tests
│   ├── test_binance_api.py # Binance integration tests
│   ├── test_integration.py # Comprehensive integration tests
│   ├── test_linking.py   # Data linking tests
│   ├── test_ohlcv_api.py # OHLCV API tests
│   ├── test_system.py    # System tests
│   └── test_system_running.py # Running system tests
├── performance/         # Performance and load tests
│   └── test_performance.py
├── html/               # HTML-based test files for frontend
│   ├── README.md       # HTML tests documentation
│   ├── debug_volume_issue.html
│   ├── test_coordinates.html
│   ├── test_macd_fix.html
│   ├── test_tooltip.html
│   ├── test_trade_lines.html
│   └── test_trade_lines_functionality.html
└── fixtures/           # Test data and shared fixtures
    ├── README.md       # Fixtures documentation
    ├── data/           # Sample data files
    ├── mocks/          # Mock objects and responses
    ├── configs/        # Test configuration files
    └── database/       # Database fixtures and seed data
```

## 🚀 Running the Project

### Quick Start
```bash
python start.py
```

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Setup database
python scripts/setup_database.py

# Run server
python scripts/run_server.py
```

### Running Tests
```bash
# Run all tests
python scripts/run_tests.py --all

# Run specific test types
python scripts/run_tests.py --unit
python scripts/run_tests.py --integration
python scripts/run_tests.py --performance

# Run with coverage
python scripts/run_tests.py --coverage
```

## 📝 Key Files

- **start.py** - Main entry point for quick project setup
- **requirements.txt** - Python package dependencies
- **README.md** - Main project documentation
- **backend/app/main.py** - FastAPI application entry point
- **scripts/run_tests.py** - Comprehensive test runner
- **tests/conftest.py** - Pytest configuration and shared fixtures

## 🔄 Migration Notes

This structure was reorganized to:
1. Consolidate all test files into a unified `tests/` directory
2. Move all documentation to the `docs/` directory
3. Organize utility scripts in the `scripts/` directory
4. Separate HTML test files from Python tests
5. Create proper test fixtures and configuration structure

All file references and imports have been updated to reflect the new structure.
