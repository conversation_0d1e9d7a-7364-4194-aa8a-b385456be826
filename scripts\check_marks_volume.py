#!/usr/bin/env python3
"""
Check if volume data is being saved in manual marks
"""
import mysql.connector
import json
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def check_marks_volume():
    """Check if volume data is being saved in marks"""
    print("🔍 Checking volume data in manual marks...")
    
    try:
        with get_db_cursor() as cursor:
            # Check manual_marks table structure
            print("\n📋 MANUAL_MARKS table structure:")
            cursor.execute("DESCRIBE manual_marks")
            marks_columns = cursor.fetchall()
            for col in marks_columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")
            
            # Check recent marks
            print("\n📊 Recent marks with OHLCV data:")
            cursor.execute("""
                SELECT id, symbol, timeframe, mark_type, price, 
                       ohlcv_snapshot, 
                       JSON_EXTRACT(ohlcv_snapshot, '$.volume') as volume_data,
                       created_at
                FROM manual_marks 
                ORDER BY id DESC 
                LIMIT 5
            """)
            recent_marks = cursor.fetchall()
            
            if not recent_marks:
                print("  No marks found in database")
                return
            
            for mark in recent_marks:
                print(f"\n  Mark ID {mark['id']} ({mark['mark_type']}):")
                print(f"    Symbol: {mark['symbol']}")
                print(f"    Price: ${mark['price']}")
                print(f"    Volume from JSON: {mark['volume_data']}")
                
                # Parse OHLCV snapshot if available
                if mark['ohlcv_snapshot']:
                    try:
                        ohlcv_data = json.loads(mark['ohlcv_snapshot'])
                        print(f"    OHLCV Data: {ohlcv_data}")
                        if 'volume' in ohlcv_data:
                            print(f"    ✅ Volume found: {ohlcv_data['volume']}")
                        else:
                            print(f"    ❌ Volume missing from OHLCV data")
                    except json.JSONDecodeError:
                        print(f"    ❌ Invalid JSON in ohlcv_snapshot")
                else:
                    print(f"    ❌ No OHLCV snapshot data")
            
            # Check total marks count
            cursor.execute("SELECT COUNT(*) as total FROM manual_marks")
            total_result = cursor.fetchone()
            print(f"\n📈 Total marks in database: {total_result['total']}")
            
            # Check marks with volume data
            cursor.execute("""
                SELECT COUNT(*) as with_volume 
                FROM manual_marks 
                WHERE JSON_EXTRACT(ohlcv_snapshot, '$.volume') IS NOT NULL 
                AND JSON_EXTRACT(ohlcv_snapshot, '$.volume') > 0
            """)
            volume_result = cursor.fetchone()
            print(f"📊 Marks with volume data: {volume_result['with_volume']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_marks_volume()
