#!/usr/bin/env python3
"""
Test PyMySQL Database Connection
"""
import sys
import os
from pathlib import Path
import pymysql
from pymysql import Error

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_direct_pymysql():
    """Test direct PyMySQL connection"""
    print("🔍 Testing direct PyMySQL connection...")
    
    try:
        from app.core.config import settings
        
        # Test direct PyMySQL connection
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            charset='utf8mb4',
            autocommit=False
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION() as version")
        result = cursor.fetchone()
        
        print(f"✅ Direct PyMySQL connection successful!")
        print(f"📊 MySQL Version: {result[0]}")
        
        # Test basic operations
        cursor.execute("SELECT 1 + 1 as result")
        test_result = cursor.fetchone()
        print(f"🧮 Test calculation: 1 + 1 = {test_result[0]}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Error as e:
        print(f"❌ Direct PyMySQL connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sqlalchemy_pymysql():
    """Test SQLAlchemy with PyMySQL"""
    print("\n🔍 Testing SQLAlchemy with PyMySQL...")
    
    try:
        from app.core.database import engine
        
        # Test SQLAlchemy connection
        with engine.connect() as connection:
            result = connection.execute("SELECT VERSION() as version")
            row = result.fetchone()
            
            print(f"✅ SQLAlchemy + PyMySQL connection successful!")
            print(f"📊 MySQL Version: {row[0]}")
            
            # Test transaction
            trans = connection.begin()
            connection.execute("SELECT 'Transaction test' as test")
            trans.commit()
            print("✅ Transaction test successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ SQLAlchemy + PyMySQL connection failed: {e}")
        return False

def test_database_tables():
    """Test database tables existence"""
    print("\n🔍 Testing database tables...")
    
    try:
        from app.core.config import settings
        
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        table_names = [table[0] for table in tables]
        required_tables = ['ohlcv_data', 'indicators_data', 'manual_marks', 'strategy_log']
        
        print(f"📋 Found tables: {table_names}")
        
        missing_tables = []
        for table in required_tables:
            if table in table_names:
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                missing_tables.append(table)
        
        cursor.close()
        connection.close()
        
        if missing_tables:
            print(f"\n⚠️ Missing tables: {missing_tables}")
            print("💡 Run 'python scripts/setup_database.py' to create missing tables")
            return False
        else:
            print("\n✅ All required tables exist!")
            return True
        
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
        return False

def test_charset_and_collation():
    """Test database charset and collation"""
    print("\n🔍 Testing database charset and collation...")
    
    try:
        from app.core.config import settings
        
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # Check database charset
        cursor.execute(f"SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = '{settings.DB_DATABASE}'")
        result = cursor.fetchone()
        
        if result:
            charset, collation = result
            print(f"📊 Database charset: {charset}")
            print(f"📊 Database collation: {collation}")
            
            if charset == 'utf8mb4':
                print("✅ Charset is correctly set to utf8mb4")
            else:
                print(f"⚠️ Charset is {charset}, recommended: utf8mb4")
        
        # Test Unicode support
        cursor.execute("SELECT '🚀 PyMySQL Unicode Test 📊' as unicode_test")
        unicode_result = cursor.fetchone()
        print(f"🌐 Unicode test: {unicode_result[0]}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing charset: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 PyMySQL Database Connection Test")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Direct PyMySQL connection
    if not test_direct_pymysql():
        all_tests_passed = False
    
    # Test 2: SQLAlchemy with PyMySQL
    if not test_sqlalchemy_pymysql():
        all_tests_passed = False
    
    # Test 3: Database tables
    if not test_database_tables():
        all_tests_passed = False
    
    # Test 4: Charset and collation
    if not test_charset_and_collation():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 All PyMySQL tests passed!")
        print("✅ Database is ready for Strategy Builder")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("💡 Make sure MySQL is running and credentials are correct")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
