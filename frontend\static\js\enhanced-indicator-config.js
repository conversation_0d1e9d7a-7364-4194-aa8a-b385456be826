/**
 * Enhanced Multi-Indicator Configuration Manager
 * Advanced features: drag-and-drop, bulk operations, presets, real-time preview
 */

class EnhancedIndicatorConfigManager extends MultiIndicatorConfigManager {
    constructor() {
        super();
        this.presets = new Map();
        this.draggedElement = null;
        this.previewMode = false;
        this.bulkOperationMode = false;
        this.selectedIndicators = new Set();
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 20;
        
        this.initEnhancedFeatures();
    }

    initEnhancedFeatures() {
        this.loadPresets();
        this.setupAdvancedEventListeners();
        this.setupKeyboardShortcuts();
        this.initializeTooltips();
    }

    async loadPresets() {
        try {
            const response = await fetch('/api/v1/indicators/presets');
            const data = await response.json();
            
            if (data.success) {
                data.data.forEach(preset => {
                    this.presets.set(preset.name, preset);
                });
                this.renderPresetSelector();
            }
        } catch (error) {
            console.error('Error loading presets:', error);
        }
    }

    setupAdvancedEventListeners() {
        // Bulk operation toggle
        const bulkToggle = document.getElementById('bulk-operation-toggle');
        if (bulkToggle) {
            bulkToggle.addEventListener('change', (e) => {
                this.toggleBulkOperationMode(e.target.checked);
            });
        }

        // Preview mode toggle
        const previewToggle = document.getElementById('preview-mode-toggle');
        if (previewToggle) {
            previewToggle.addEventListener('change', (e) => {
                this.togglePreviewMode(e.target.checked);
            });
        }

        // Preset selector
        const presetSelector = document.getElementById('preset-selector');
        if (presetSelector) {
            presetSelector.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.applyPreset(e.target.value);
                }
            });
        }

        // Bulk operations
        document.getElementById('bulk-enable')?.addEventListener('click', () => {
            this.bulkEnableSelected();
        });

        document.getElementById('bulk-disable')?.addEventListener('click', () => {
            this.bulkDisableSelected();
        });

        document.getElementById('bulk-delete')?.addEventListener('click', () => {
            this.bulkDeleteSelected();
        });

        // Undo/Redo
        document.getElementById('undo-config')?.addEventListener('click', () => {
            this.undo();
        });

        document.getElementById('redo-config')?.addEventListener('click', () => {
            this.redo();
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'z':
                        e.preventDefault();
                        if (e.shiftKey) {
                            this.redo();
                        } else {
                            this.undo();
                        }
                        break;
                    case 's':
                        e.preventDefault();
                        this.saveAllConfigurations();
                        break;
                    case 'a':
                        if (this.bulkOperationMode) {
                            e.preventDefault();
                            this.selectAllIndicators();
                        }
                        break;
                }
            }
        });
    }

    initializeTooltips() {
        // Initialize tooltips for all elements with data-tooltip attribute
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            this.createTooltip(element);
        });
    }

    createTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'enhanced-tooltip';
        tooltip.textContent = element.dataset.tooltip;
        tooltip.style.display = 'none';
        document.body.appendChild(tooltip);

        element.addEventListener('mouseenter', (e) => {
            tooltip.style.display = 'block';
            tooltip.style.left = e.pageX + 10 + 'px';
            tooltip.style.top = e.pageY - 30 + 'px';
        });

        element.addEventListener('mouseleave', () => {
            tooltip.style.display = 'none';
        });

        element.addEventListener('mousemove', (e) => {
            tooltip.style.left = e.pageX + 10 + 'px';
            tooltip.style.top = e.pageY - 30 + 'px';
        });
    }

    renderIndicatorConfigs() {
        super.renderIndicatorConfigs();
        this.makePanelsDraggable();
        this.addBulkSelectionCheckboxes();
        this.addQuickActionButtons();
    }

    makePanelsDraggable() {
        const panels = document.querySelectorAll('.indicator-panel');
        panels.forEach(panel => {
            panel.draggable = true;
            panel.addEventListener('dragstart', this.handleDragStart.bind(this));
            panel.addEventListener('dragover', this.handleDragOver.bind(this));
            panel.addEventListener('drop', this.handleDrop.bind(this));
            panel.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
    }

    handleDragStart(e) {
        this.draggedElement = e.target.closest('.indicator-panel');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.draggedElement.outerHTML);
        this.draggedElement.classList.add('dragging');
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        
        const panel = e.target.closest('.indicator-panel');
        if (panel && panel !== this.draggedElement) {
            panel.classList.add('drag-over');
        }
    }

    handleDrop(e) {
        e.preventDefault();
        const targetPanel = e.target.closest('.indicator-panel');
        
        if (targetPanel && targetPanel !== this.draggedElement) {
            const container = targetPanel.parentNode;
            const draggedIndex = Array.from(container.children).indexOf(this.draggedElement);
            const targetIndex = Array.from(container.children).indexOf(targetPanel);
            
            if (draggedIndex < targetIndex) {
                container.insertBefore(this.draggedElement, targetPanel.nextSibling);
            } else {
                container.insertBefore(this.draggedElement, targetPanel);
            }
            
            this.updateDisplayOrder();
            this.saveState();
        }
        
        document.querySelectorAll('.drag-over').forEach(el => {
            el.classList.remove('drag-over');
        });
    }

    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        document.querySelectorAll('.drag-over').forEach(el => {
            el.classList.remove('drag-over');
        });
    }

    addBulkSelectionCheckboxes() {
        if (!this.bulkOperationMode) return;
        
        const panels = document.querySelectorAll('.indicator-panel');
        panels.forEach(panel => {
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'bulk-select-checkbox';
            checkbox.addEventListener('change', (e) => {
                const indicatorName = panel.dataset.indicator;
                if (e.target.checked) {
                    this.selectedIndicators.add(indicatorName);
                } else {
                    this.selectedIndicators.delete(indicatorName);
                }
                this.updateBulkOperationButtons();
            });
            
            const header = panel.querySelector('.indicator-header');
            header.insertBefore(checkbox, header.firstChild);
        });
    }

    addQuickActionButtons() {
        const panels = document.querySelectorAll('.indicator-panel');
        panels.forEach(panel => {
            const header = panel.querySelector('.indicator-header');
            const actions = document.createElement('div');
            actions.className = 'quick-actions';
            
            // Duplicate button
            const duplicateBtn = document.createElement('button');
            duplicateBtn.className = 'btn-quick-action btn-duplicate';
            duplicateBtn.innerHTML = '📋';
            duplicateBtn.title = 'Duplicate indicator';
            duplicateBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.duplicateIndicator(panel.dataset.indicator);
            });
            
            // Reset button
            const resetBtn = document.createElement('button');
            resetBtn.className = 'btn-quick-action btn-reset';
            resetBtn.innerHTML = '🔄';
            resetBtn.title = 'Reset to defaults';
            resetBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.resetIndicatorToDefaults(panel.dataset.indicator);
            });
            
            actions.appendChild(duplicateBtn);
            actions.appendChild(resetBtn);
            header.appendChild(actions);
        });
    }

    toggleBulkOperationMode(enabled) {
        this.bulkOperationMode = enabled;
        const container = document.getElementById('indicator-config-container');
        
        if (enabled) {
            container.classList.add('bulk-mode');
            this.addBulkSelectionCheckboxes();
        } else {
            container.classList.remove('bulk-mode');
            this.selectedIndicators.clear();
            document.querySelectorAll('.bulk-select-checkbox').forEach(cb => cb.remove());
        }
        
        this.updateBulkOperationButtons();
    }

    togglePreviewMode(enabled) {
        this.previewMode = enabled;
        
        if (enabled) {
            // Enable real-time preview
            document.querySelectorAll('.config-row input').forEach(input => {
                input.addEventListener('input', this.debounce(() => {
                    this.previewChanges();
                }, 300));
            });
        } else {
            // Disable preview listeners
            document.querySelectorAll('.config-row input').forEach(input => {
                input.removeEventListener('input', this.previewChanges);
            });
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async previewChanges() {
        if (!this.previewMode) return;
        
        const currentConfig = this.gatherCurrentConfiguration();
        
        // Send preview request to backend
        try {
            const response = await fetch('/api/v1/indicators/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    strategy_id: this.currentStrategy,
                    indicators_config: currentConfig,
                    preview_mode: true
                })
            });
            
            const data = await response.json();
            if (data.success) {
                // Update chart with preview data
                this.updateChartPreview(data.data);
            }
        } catch (error) {
            console.error('Error previewing changes:', error);
        }
    }

    updateChartPreview(previewData) {
        // Update the chart with preview data
        if (window.chartManager) {
            window.chartManager.updateIndicatorsPreview(previewData);
        }
    }

    saveState() {
        const state = {
            configs: JSON.parse(JSON.stringify(this.currentConfigs)),
            order: this.getCurrentDisplayOrder(),
            timestamp: Date.now()
        };
        
        this.undoStack.push(state);
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        
        this.redoStack = []; // Clear redo stack when new action is performed
        this.updateUndoRedoButtons();
    }

    undo() {
        if (this.undoStack.length === 0) return;
        
        const currentState = {
            configs: JSON.parse(JSON.stringify(this.currentConfigs)),
            order: this.getCurrentDisplayOrder(),
            timestamp: Date.now()
        };
        
        this.redoStack.push(currentState);
        const previousState = this.undoStack.pop();
        
        this.currentConfigs = previousState.configs;
        this.restoreDisplayOrder(previousState.order);
        this.renderIndicatorConfigs();
        this.updateUndoRedoButtons();
    }

    redo() {
        if (this.redoStack.length === 0) return;
        
        const currentState = {
            configs: JSON.parse(JSON.stringify(this.currentConfigs)),
            order: this.getCurrentDisplayOrder(),
            timestamp: Date.now()
        };
        
        this.undoStack.push(currentState);
        const nextState = this.redoStack.pop();
        
        this.currentConfigs = nextState.configs;
        this.restoreDisplayOrder(nextState.order);
        this.renderIndicatorConfigs();
        this.updateUndoRedoButtons();
    }

    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-config');
        const redoBtn = document.getElementById('redo-config');
        
        if (undoBtn) undoBtn.disabled = this.undoStack.length === 0;
        if (redoBtn) redoBtn.disabled = this.redoStack.length === 0;
    }

    getCurrentDisplayOrder() {
        const panels = document.querySelectorAll('.indicator-panel');
        return Array.from(panels).map(panel => panel.dataset.indicator);
    }

    restoreDisplayOrder(order) {
        const container = document.getElementById('indicator-config-container');
        const panels = Array.from(container.children);
        
        order.forEach((indicatorName, index) => {
            const panel = panels.find(p => p.dataset.indicator === indicatorName);
            if (panel) {
                container.appendChild(panel);
            }
        });
    }

    updateDisplayOrder() {
        const panels = document.querySelectorAll('.indicator-panel');
        panels.forEach((panel, index) => {
            const indicatorName = panel.dataset.indicator;
            if (this.currentConfigs[indicatorName]) {
                this.currentConfigs[indicatorName].display_order = index;
            }
        });
    }
}

    bulkEnableSelected() {
        this.selectedIndicators.forEach(indicatorName => {
            if (this.currentConfigs[indicatorName]) {
                this.currentConfigs[indicatorName].is_enabled = true;
            }
        });
        this.renderIndicatorConfigs();
        this.saveState();
    }

    bulkDisableSelected() {
        this.selectedIndicators.forEach(indicatorName => {
            if (this.currentConfigs[indicatorName]) {
                this.currentConfigs[indicatorName].is_enabled = false;
            }
        });
        this.renderIndicatorConfigs();
        this.saveState();
    }

    bulkDeleteSelected() {
        if (confirm(`Delete ${this.selectedIndicators.size} selected indicators?`)) {
            this.selectedIndicators.forEach(indicatorName => {
                delete this.currentConfigs[indicatorName];
            });
            this.selectedIndicators.clear();
            this.renderIndicatorConfigs();
            this.saveState();
        }
    }

    selectAllIndicators() {
        const checkboxes = document.querySelectorAll('.bulk-select-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = true;
            const panel = cb.closest('.indicator-panel');
            this.selectedIndicators.add(panel.dataset.indicator);
        });
        this.updateBulkOperationButtons();
    }

    updateBulkOperationButtons() {
        const hasSelection = this.selectedIndicators.size > 0;
        document.getElementById('bulk-enable')?.toggleAttribute('disabled', !hasSelection);
        document.getElementById('bulk-disable')?.toggleAttribute('disabled', !hasSelection);
        document.getElementById('bulk-delete')?.toggleAttribute('disabled', !hasSelection);
    }

    duplicateIndicator(indicatorName) {
        const originalConfig = this.currentConfigs[indicatorName];
        if (!originalConfig) return;

        let newName = `${indicatorName}_copy`;
        let counter = 1;
        while (this.currentConfigs[newName]) {
            newName = `${indicatorName}_copy_${counter}`;
            counter++;
        }

        this.currentConfigs[newName] = {
            ...JSON.parse(JSON.stringify(originalConfig)),
            display_order: Object.keys(this.currentConfigs).length
        };

        this.renderIndicatorConfigs();
        this.saveState();
    }

    resetIndicatorToDefaults(indicatorName) {
        const defaults = this.indicatorDefaults[indicatorName];
        if (!defaults) return;

        if (confirm(`Reset ${indicatorName} to default settings?`)) {
            this.currentConfigs[indicatorName] = {
                ...this.currentConfigs[indicatorName],
                config: JSON.parse(JSON.stringify(defaults.default_config))
            };
            this.renderIndicatorConfigs();
            this.saveState();
        }
    }

    renderPresetSelector() {
        const selector = document.getElementById('preset-selector');
        if (!selector) return;

        selector.innerHTML = '<option value="">Select a preset...</option>';

        this.presets.forEach((preset, name) => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = preset.display_name || name;
            selector.appendChild(option);
        });
    }

    async applyPreset(presetName) {
        const preset = this.presets.get(presetName);
        if (!preset) return;

        if (confirm(`Apply preset "${preset.display_name || presetName}"? This will replace current configuration.`)) {
            this.saveState(); // Save current state before applying preset

            this.currentConfigs = JSON.parse(JSON.stringify(preset.indicators));
            this.renderIndicatorConfigs();

            // Show success message
            this.showMessage('Preset applied successfully!', 'success');
        }
    }

    async savePreset(name, displayName, description) {
        const preset = {
            name: name,
            display_name: displayName,
            description: description,
            indicators: JSON.parse(JSON.stringify(this.currentConfigs)),
            created_at: new Date().toISOString()
        };

        try {
            const response = await fetch('/api/v1/indicators/presets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(preset)
            });

            const data = await response.json();
            if (data.success) {
                this.presets.set(name, preset);
                this.renderPresetSelector();
                this.showMessage('Preset saved successfully!', 'success');
            } else {
                this.showMessage('Failed to save preset: ' + data.error, 'error');
            }
        } catch (error) {
            console.error('Error saving preset:', error);
            this.showMessage('Error saving preset', 'error');
        }
    }

    gatherCurrentConfiguration() {
        const config = {};

        Object.keys(this.currentConfigs).forEach(indicatorName => {
            const panel = document.querySelector(`[data-indicator="${indicatorName}"]`);
            if (panel) {
                const inputs = panel.querySelectorAll('.config-row input');
                const indicatorConfig = {};

                inputs.forEach(input => {
                    const paramName = input.dataset.param;
                    if (paramName) {
                        indicatorConfig[paramName] = input.type === 'number' ?
                            parseFloat(input.value) : input.value;
                    }
                });

                config[indicatorName] = {
                    ...this.currentConfigs[indicatorName],
                    config: indicatorConfig
                };
            }
        });

        return config;
    }

    showMessage(text, type = 'info', duration = 3000) {
        const messageContainer = document.getElementById('config-messages') || this.createMessageContainer();

        const message = document.createElement('div');
        message.className = `config-message ${type}`;
        message.textContent = text;
        message.style.display = 'block';

        messageContainer.appendChild(message);

        setTimeout(() => {
            message.remove();
        }, duration);
    }

    createMessageContainer() {
        const container = document.createElement('div');
        container.id = 'config-messages';
        container.className = 'config-messages-container';

        const configPanel = document.querySelector('.indicator-config-panel');
        if (configPanel) {
            configPanel.insertBefore(container, configPanel.firstChild);
        }

        return container;
    }

    exportConfiguration() {
        const config = this.gatherCurrentConfiguration();
        const exportData = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            strategy_id: this.currentStrategy,
            indicators: config
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `indicator-config-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async importConfiguration(file) {
        try {
            const text = await file.text();
            const importData = JSON.parse(text);

            if (importData.version && importData.indicators) {
                if (confirm('Import configuration? This will replace current settings.')) {
                    this.saveState();
                    this.currentConfigs = importData.indicators;
                    this.renderIndicatorConfigs();
                    this.showMessage('Configuration imported successfully!', 'success');
                }
            } else {
                this.showMessage('Invalid configuration file format', 'error');
            }
        } catch (error) {
            console.error('Error importing configuration:', error);
            this.showMessage('Error importing configuration', 'error');
        }
    }
}

// Initialize enhanced configuration manager
let enhancedConfigManager = null;

document.addEventListener('DOMContentLoaded', () => {
    enhancedConfigManager = new EnhancedIndicatorConfigManager();
    window.enhancedConfigManager = enhancedConfigManager;
});
