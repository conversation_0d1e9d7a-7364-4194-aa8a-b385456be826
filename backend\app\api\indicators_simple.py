"""
Technical Indicators API Endpoints (Simplified)
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.core.data_access import OHLCVDataAccess, IndicatorsDataAccess
from app.services.indicators import IndicatorsService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/calculate")
async def calculate_indicators(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    config: Dict[str, Any] = Body(..., description="Indicators configuration"),
    limit: int = Query(500, description="Number of candles to process", ge=1, le=2000)
):
    """Calculate technical indicators for given OHLCV data"""
    try:
        # Get OHLCV data from database
        ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
            symbol=symbol.upper(),
            timeframe=timeframe,
            limit=limit
        )
        
        if not ohlcv_data:
            raise HTTPException(status_code=404, detail="No OHLCV data found")
        
        # Convert to format expected by indicators service
        formatted_data = []
        for record in ohlcv_data:
            formatted_data.append({
                'timestamp': record['timestamp'],
                'open': float(record['open']),
                'high': float(record['high']),
                'low': float(record['low']),
                'close': float(record['close']),
                'volume': float(record['volume'])
            })
        
        # Calculate indicators
        indicators_result = IndicatorsService.calculate_all_indicators(formatted_data, config)
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "config": config,
                "data_points": len(formatted_data),
                "indicators": indicators_result
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data")
async def get_indicators_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    indicator_name: str = Query(..., description="Indicator name"),
    limit: int = Query(500, description="Number of records", ge=1, le=2000)
):
    """Get stored indicator data"""
    try:
        indicators_data = IndicatorsDataAccess.get_indicators(
            symbol=symbol.upper(),
            timeframe=timeframe,
            indicator_name=indicator_name,
            limit=limit
        )
        
        if not indicators_data:
            raise HTTPException(status_code=404, detail="No indicator data found")
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "indicator_name": indicator_name,
                "count": len(indicators_data),
                "indicators": indicators_data
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/available")
async def get_available_indicators():
    """Get list of available indicators"""
    return {
        "success": True,
        "data": {
            "indicators": [
                {
                    "name": "rsi",
                    "description": "Relative Strength Index",
                    "parameters": {"period": "int (default: 14)"}
                },
                {
                    "name": "macd",
                    "description": "Moving Average Convergence Divergence",
                    "parameters": {
                        "fast": "int (default: 12)",
                        "slow": "int (default: 26)",
                        "signal": "int (default: 9)"
                    }
                },
                {
                    "name": "ema",
                    "description": "Exponential Moving Average",
                    "parameters": {"period": "int (default: 20)"}
                },
                {
                    "name": "sma",
                    "description": "Simple Moving Average",
                    "parameters": {"period": "int (default: 50)"}
                },
                {
                    "name": "bollinger_bands",
                    "description": "Bollinger Bands",
                    "parameters": {
                        "period": "int (default: 20)",
                        "std": "float (default: 2.0)"
                    }
                }
            ]
        }
    }
