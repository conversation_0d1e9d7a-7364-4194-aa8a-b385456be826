"""
MEXC API Client for fetching OHLCV data
"""
import requests
import hmac
import hashlib
import time
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class MEXCClient:
    """MEXC Futures API client"""
    
    def __init__(self):
        self.config = settings.mexc_config
        self.base_url = self.config["base_url"]
        self.api_key = self.config["api_key"]
        self.api_secret = self.config["api_secret"]
        self.session = requests.Session()
        self.session.headers.update({
            'ApiKey': self.api_key,
            'Content-Type': 'application/json'
        })
    
    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """Make API request with error handling"""
        if params is None:
            params = {}
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            params['signature'] = self._generate_signature(query_string)
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"MEXC API request failed: {e}")
            raise Exception(f"MEXC API error: {e}")
    
    def get_klines(self, symbol: str, interval: str, limit: int = 500,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[Dict]:
        """
        Fetch kline/candlestick data
        
        Args:
            symbol: Trading pair symbol (e.g., 'BTC_USDT')
            interval: Kline interval (Min1, Min5, Min15, Min30, Min60, Hour4, Hour8, Day1, Week1, Month1)
            limit: Number of klines to fetch (max 2000)
            start_time: Start time for data range
            end_time: End time for data range
        
        Returns:
            List of OHLCV data dictionaries
        """
        # Convert timeframe to MEXC format
        interval_map = {
            '1m': 'Min1', '3m': 'Min3', '5m': 'Min5', '15m': 'Min15', '30m': 'Min30',
            '1h': 'Min60', '2h': 'Hour2', '4h': 'Hour4', '8h': 'Hour8',
            '1d': 'Day1', '1w': 'Week1', '1M': 'Month1'
        }
        
        mexc_interval = interval_map.get(interval, interval)
        
        params = {
            'symbol': symbol.replace('USDT', '_USDT'),  # Convert to MEXC format
            'interval': mexc_interval,
            'limit': min(limit, 2000)  # MEXC limit
        }
        
        if start_time:
            params['start'] = int(start_time.timestamp())
        if end_time:
            params['end'] = int(end_time.timestamp())
        
        try:
            response = self._make_request('/api/v1/contract/kline', params)
            
            if response.get('success') and response.get('data'):
                data = response['data']
                
                # Convert to standardized format
                ohlcv_data = []
                for kline in data:
                    ohlcv_data.append({
                        'timestamp': datetime.fromtimestamp(kline['time']),
                        'open': float(kline['open']),
                        'high': float(kline['high']),
                        'low': float(kline['low']),
                        'close': float(kline['close']),
                        'volume': float(kline['vol'])
                    })
                
                logger.info(f"Fetched {len(ohlcv_data)} klines for {symbol} {interval}")
                return ohlcv_data
            else:
                logger.error(f"MEXC API returned error: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to fetch MEXC klines: {e}")
            raise
    
    def get_contracts(self) -> Dict:
        """Get contract information"""
        try:
            return self._make_request('/api/v1/contract/detail')
        except Exception as e:
            logger.error(f"Failed to fetch MEXC contracts: {e}")
            raise
    
    def get_ticker(self, symbol: Optional[str] = None) -> Dict:
        """Get ticker information"""
        params = {}
        if symbol:
            params['symbol'] = symbol.replace('USDT', '_USDT')
        
        try:
            return self._make_request('/api/v1/contract/ticker', params)
        except Exception as e:
            logger.error(f"Failed to fetch MEXC ticker: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """Test API connection"""
        try:
            response = self._make_request('/api/v1/contract/ping')
            logger.info("MEXC connection test successful")
            return True
        except Exception as e:
            logger.error(f"MEXC connection test failed: {e}")
            return False
