#!/usr/bin/env python3
"""
Comprehensive test runner for Strategy Builder
"""
import sys
import os
import subprocess
import time
from pathlib import Path
import argparse

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

def run_command(command, description, timeout=300):
    """Run a command with timeout and error handling"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Command: {' '.join(command)}")
    print()
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=Path(__file__).parent.parent
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        if result.returncode == 0:
            print("✅ PASSED")
            if result.stdout:
                print("\nOutput:")
                print(result.stdout)
        else:
            print("❌ FAILED")
            if result.stderr:
                print("\nError:")
                print(result.stderr)
            if result.stdout:
                print("\nOutput:")
                print(result.stdout)
        
        return result.returncode == 0, duration
        
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT after {timeout} seconds")
        return False, timeout
    except Exception as e:
        print(f"💥 ERROR: {e}")
        return False, 0

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'pytest-cov',
        'fastapi',
        'sqlalchemy',
        'pandas',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available")
    return True

def run_unit_tests():
    """Run unit tests"""
    command = [
        sys.executable, "-m", "pytest",
        "tests/unit/",
        "-v",
        "--tb=short",
        "--durations=10"
    ]
    
    return run_command(command, "Running Unit Tests")

def run_integration_tests():
    """Run integration tests"""
    command = [
        sys.executable, "-m", "pytest",
        "tests/integration/",
        "-v",
        "--tb=short",
        "--durations=10"
    ]
    
    return run_command(command, "Running Integration Tests")

def run_performance_tests():
    """Run performance tests"""
    command = [
        sys.executable, "-m", "pytest",
        "tests/performance/",
        "-v",
        "--tb=short",
        "-s"  # Don't capture output for performance metrics
    ]
    
    return run_command(command, "Running Performance Tests")

def run_coverage_tests():
    """Run tests with coverage"""
    command = [
        sys.executable, "-m", "pytest",
        "tests/unit/",
        "tests/integration/",
        "--cov=backend/app",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--cov-fail-under=80"
    ]
    
    return run_command(command, "Running Coverage Tests")

def run_linting():
    """Run code linting"""
    print("\n🔍 Running Code Quality Checks...")
    
    # Check if flake8 is available
    try:
        import flake8
        command = [
            sys.executable, "-m", "flake8",
            "backend/app/",
            "--max-line-length=120",
            "--ignore=E203,W503"
        ]
        return run_command(command, "Running Flake8 Linting")
    except ImportError:
        print("⚠️  flake8 not installed, skipping linting")
        return True, 0

def run_type_checking():
    """Run type checking with mypy"""
    try:
        import mypy
        command = [
            sys.executable, "-m", "mypy",
            "backend/app/",
            "--ignore-missing-imports",
            "--no-strict-optional"
        ]
        return run_command(command, "Running Type Checking")
    except ImportError:
        print("⚠️  mypy not installed, skipping type checking")
        return True, 0

def run_security_check():
    """Run security checks"""
    try:
        import bandit
        command = [
            sys.executable, "-m", "bandit",
            "-r", "backend/app/",
            "-f", "json"
        ]
        return run_command(command, "Running Security Check")
    except ImportError:
        print("⚠️  bandit not installed, skipping security check")
        return True, 0

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="Strategy Builder Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--coverage", action="store_true", help="Run coverage tests")
    parser.add_argument("--lint", action="store_true", help="Run linting only")
    parser.add_argument("--type-check", action="store_true", help="Run type checking only")
    parser.add_argument("--security", action="store_true", help="Run security check only")
    parser.add_argument("--all", action="store_true", help="Run all tests and checks")
    parser.add_argument("--fast", action="store_true", help="Run fast tests only (unit + integration)")
    
    args = parser.parse_args()
    
    print("🚀 Strategy Builder Test Runner")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    results = []
    total_duration = 0
    
    # Determine what to run
    if args.unit or args.fast or args.all or not any(vars(args).values()):
        success, duration = run_unit_tests()
        results.append(("Unit Tests", success))
        total_duration += duration
    
    if args.integration or args.fast or args.all:
        success, duration = run_integration_tests()
        results.append(("Integration Tests", success))
        total_duration += duration
    
    if args.performance or args.all:
        success, duration = run_performance_tests()
        results.append(("Performance Tests", success))
        total_duration += duration
    
    if args.coverage or args.all:
        success, duration = run_coverage_tests()
        results.append(("Coverage Tests", success))
        total_duration += duration
    
    if args.lint or args.all:
        success, duration = run_linting()
        results.append(("Linting", success))
        total_duration += duration
    
    if args.type_check or args.all:
        success, duration = run_type_checking()
        results.append(("Type Checking", success))
        total_duration += duration
    
    if args.security or args.all:
        success, duration = run_security_check()
        results.append(("Security Check", success))
        total_duration += duration
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal Duration: {total_duration:.2f} seconds")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print(f"\n💥 {failed} test suite(s) failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
