<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional TradingView Chart - Binance Live Data</title>
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Trebuchet MS', Arial, sans-serif;
            background: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        .trading-interface {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Top Toolbar */
        .top-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
            border-bottom: 1px solid #363c4e;
            min-height: 50px;
        }

        .symbol-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .symbol-name {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
        }

        .symbol-price {
            font-size: 16px;
            font-weight: 600;
            color: #26a69a;
        }

        .symbol-change {
            font-size: 14px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }

        .symbol-change.positive {
            color: #26a69a;
            background: rgba(38, 166, 154, 0.1);
        }

        .symbol-change.negative {
            color: #ef5350;
            background: rgba(239, 83, 80, 0.1);
        }

        .toolbar-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .symbol-selector {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .symbol-input {
            padding: 6px 12px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
            min-width: 120px;
        }

        .timeframe-buttons {
            display: flex;
            gap: 2px;
            background: #333;
            border-radius: 4px;
            padding: 2px;
        }

        .timeframe-btn {
            padding: 6px 12px;
            border: none;
            background: transparent;
            color: #b2b5be;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .timeframe-btn:hover {
            background: #444;
            color: #fff;
        }

        .timeframe-btn.active {
            background: #2962ff;
            color: white;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef5350;
        }

        .status-dot.connected {
            background: #26a69a;
        }

        /* Chart Container */
        .chart-container {
            flex: 1;
            position: relative;
            background: #131722;
        }

        #tradingview-chart {
            width: 100%;
            height: 100%;
        }

        /* Bottom Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 16px;
            background: #1e222d;
            border-top: 1px solid #363c4e;
            font-size: 12px;
            min-height: 32px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status.error {
            color: #ef5350;
        }

        .status.info {
            color: #26a69a;
        }

        #crosshair-info {
            font-family: 'Courier New', monospace;
            color: #b2b5be;
        }

        /* Loading Overlay */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(19, 23, 34, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #363c4e;
            border-top: 3px solid #2962ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .top-toolbar {
                flex-direction: column;
                gap: 10px;
                padding: 12px;
            }

            .symbol-info {
                justify-content: center;
            }

            .toolbar-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .timeframe-buttons {
                flex-wrap: wrap;
            }

            .status-bar {
                flex-direction: column;
                gap: 5px;
                text-align: center;
            }
        }

        /* Professional Styling */
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #2962ff;
            color: white;
        }

        .btn-primary:hover {
            background: #1e88e5;
        }

        .btn-secondary {
            background: #444;
            color: #b2b5be;
        }

        .btn-secondary:hover {
            background: #555;
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="trading-interface">
        <!-- Top Toolbar -->
        <div class="top-toolbar">
            <div class="symbol-info">
                <div class="symbol-name" id="symbol-display">BTCUSDT</div>
                <div class="symbol-price" id="symbol-price">$0.00</div>
                <div class="symbol-change positive" id="symbol-change">+0.00%</div>
            </div>
            
            <div class="toolbar-controls">
                <div class="symbol-selector">
                    <input type="text" class="symbol-input" id="symbol-input" placeholder="Symbol" value="BTCUSDT">
                    <button class="btn btn-primary" id="change-symbol">Change</button>
                </div>
                
                <div class="timeframe-buttons">
                    <button class="timeframe-btn active" data-interval="1m">1m</button>
                    <button class="timeframe-btn" data-interval="5m">5m</button>
                    <button class="timeframe-btn" data-interval="15m">15m</button>
                    <button class="timeframe-btn" data-interval="1h">1h</button>
                    <button class="timeframe-btn" data-interval="4h">4h</button>
                    <button class="timeframe-btn" data-interval="1d">1d</button>
                </div>
                
                <div class="connection-status">
                    <div class="status-dot" id="connection-dot"></div>
                    <span id="connection-text">Connecting...</span>
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container">
            <div id="tradingview-chart"></div>
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-spinner"></div>
            </div>
        </div>

        <!-- Bottom Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <div id="chart-status" class="status info">Initializing chart...</div>
                <div id="crosshair-info"></div>
            </div>
            <div class="status-right">
                <span>Powered by Binance API & TradingView Charts</span>
            </div>
        </div>
    </div>

    <script src="js/tradingview-chart.js"></script>
    <script>
        // Enhanced demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loading overlay after chart initialization
            setTimeout(() => {
                document.getElementById('loading-overlay').classList.add('hidden');
            }, 2000);

            // Symbol change functionality
            document.getElementById('change-symbol').addEventListener('click', () => {
                const newSymbol = document.getElementById('symbol-input').value.trim().toUpperCase();
                if (newSymbol && window.tradingViewChart) {
                    window.tradingViewChart.changeSymbol(newSymbol);
                    document.getElementById('symbol-display').textContent = newSymbol;
                    document.getElementById('loading-overlay').classList.remove('hidden');
                    setTimeout(() => {
                        document.getElementById('loading-overlay').classList.add('hidden');
                    }, 1500);
                }
            });

            // Enter key support for symbol input
            document.getElementById('symbol-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('change-symbol').click();
                }
            });

            // Timeframe buttons
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    // Update active state
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    
                    // Change interval
                    const interval = btn.dataset.interval;
                    if (window.tradingViewChart) {
                        window.tradingViewChart.changeInterval(interval);
                        document.getElementById('loading-overlay').classList.remove('hidden');
                        setTimeout(() => {
                            document.getElementById('loading-overlay').classList.add('hidden');
                        }, 1500);
                    }
                });
            });

            // Connection status updates
            const updateConnectionStatus = (connected) => {
                const dot = document.getElementById('connection-dot');
                const text = document.getElementById('connection-text');
                
                if (connected) {
                    dot.classList.add('connected');
                    text.textContent = 'Live Data';
                } else {
                    dot.classList.remove('connected');
                    text.textContent = 'Disconnected';
                }
            };

            // Simulate connection status (replace with actual WebSocket status)
            setTimeout(() => updateConnectionStatus(true), 3000);

            // Sample price updates (replace with real data)
            const updatePriceDisplay = () => {
                const symbol = document.getElementById('symbol-display').textContent;
                if (symbol.includes('BTC')) {
                    const price = 45000 + (Math.random() - 0.5) * 1000;
                    const change = (Math.random() - 0.5) * 2;
                    document.getElementById('symbol-price').textContent = `$${price.toFixed(2)}`;
                    document.getElementById('symbol-change').textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                    document.getElementById('symbol-change').className = `symbol-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            };

            // Update price every 5 seconds
            setInterval(updatePriceDisplay, 5000);
            updatePriceDisplay();
        });
    </script>
</body>
</html>
