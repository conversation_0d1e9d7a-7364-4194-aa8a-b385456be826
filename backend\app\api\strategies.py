"""
Strategy Management API
Handles strategy creation, selection, and data linking
"""
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from app.core.database import get_db_cursor, DatabaseError

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models
class StrategyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    symbol: str = Field(..., min_length=1, max_length=20)
    timeframe: str = Field(..., min_length=1, max_length=10)
    exchange: str = Field(default="binance", max_length=20)

class StrategyResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    symbol: str
    timeframe: str
    exchange: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    data_count: Optional[int] = 0

class StrategyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    is_active: Optional[bool] = None

@router.post("/create", response_model=StrategyResponse)
async def create_strategy(strategy: StrategyCreate):
    """Create a new trading strategy"""
    try:
        with get_db_cursor() as cursor:
            # Check if strategy name already exists
            cursor.execute(
                "SELECT id FROM strategies WHERE name = %s",
                (strategy.name,)
            )
            if cursor.fetchone():
                raise HTTPException(status_code=400, detail="Strategy name already exists")
            
            # Insert new strategy
            cursor.execute("""
                INSERT INTO strategies (name, description, symbol, timeframe, exchange)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                strategy.name,
                strategy.description,
                strategy.symbol.upper(),
                strategy.timeframe,
                strategy.exchange
            ))
            
            strategy_id = cursor.lastrowid
            
            # Get the created strategy
            cursor.execute("""
                SELECT id, name, description, symbol, timeframe, exchange, 
                       created_at, updated_at, is_active
                FROM strategies WHERE id = %s
            """, (strategy_id,))
            
            result = cursor.fetchone()
            if not result:
                raise HTTPException(status_code=500, detail="Failed to create strategy")
            
            return StrategyResponse(**result, data_count=0)
            
    except DatabaseError as e:
        logger.error(f"Database error creating strategy: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error creating strategy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/list", response_model=List[StrategyResponse])
async def list_strategies(
    active_only: bool = Query(True, description="Return only active strategies"),
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    timeframe: Optional[str] = Query(None, description="Filter by timeframe")
):
    """List all strategies with optional filtering"""
    try:
        with get_db_cursor() as cursor:
            # Build query with filters
            where_conditions = []
            params = []
            
            if active_only:
                where_conditions.append("s.is_active = %s")
                params.append(True)
            
            if symbol:
                where_conditions.append("s.symbol = %s")
                params.append(symbol.upper())
            
            if timeframe:
                where_conditions.append("s.timeframe = %s")
                params.append(timeframe)
            
            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            query = f"""
                SELECT s.id, s.name, s.description, s.symbol, s.timeframe, s.exchange,
                       s.created_at, s.updated_at, s.is_active,
                       COUNT(o.timestamp) as data_count
                FROM strategies s
                LEFT JOIN ohlcv_data o ON s.id = o.strategy_id
                {where_clause}
                GROUP BY s.id, s.name, s.description, s.symbol, s.timeframe, s.exchange,
                         s.created_at, s.updated_at, s.is_active
                ORDER BY s.updated_at DESC
            """
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            return [StrategyResponse(**result) for result in results]
            
    except DatabaseError as e:
        logger.error(f"Database error listing strategies: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error listing strategies: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(strategy_id: int):
    """Get a specific strategy by ID"""
    try:
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT s.id, s.name, s.description, s.symbol, s.timeframe, s.exchange,
                       s.created_at, s.updated_at, s.is_active,
                       COUNT(o.timestamp) as data_count
                FROM strategies s
                LEFT JOIN ohlcv_data o ON s.id = o.strategy_id
                WHERE s.id = %s
                GROUP BY s.id, s.name, s.description, s.symbol, s.timeframe, s.exchange,
                         s.created_at, s.updated_at, s.is_active
            """, (strategy_id,))
            
            result = cursor.fetchone()
            if not result:
                raise HTTPException(status_code=404, detail="Strategy not found")
            
            return StrategyResponse(**result)
            
    except DatabaseError as e:
        logger.error(f"Database error getting strategy: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error getting strategy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(strategy_id: int, strategy_update: StrategyUpdate):
    """Update a strategy"""
    try:
        with get_db_cursor() as cursor:
            # Check if strategy exists
            cursor.execute("SELECT id FROM strategies WHERE id = %s", (strategy_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Strategy not found")
            
            # Build update query
            update_fields = []
            params = []
            
            if strategy_update.name is not None:
                # Check if new name already exists
                cursor.execute(
                    "SELECT id FROM strategies WHERE name = %s AND id != %s",
                    (strategy_update.name, strategy_id)
                )
                if cursor.fetchone():
                    raise HTTPException(status_code=400, detail="Strategy name already exists")
                
                update_fields.append("name = %s")
                params.append(strategy_update.name)
            
            if strategy_update.description is not None:
                update_fields.append("description = %s")
                params.append(strategy_update.description)
            
            if strategy_update.is_active is not None:
                update_fields.append("is_active = %s")
                params.append(strategy_update.is_active)
            
            if not update_fields:
                raise HTTPException(status_code=400, detail="No fields to update")
            
            # Add updated_at
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(strategy_id)
            
            # Execute update
            cursor.execute(f"""
                UPDATE strategies 
                SET {', '.join(update_fields)}
                WHERE id = %s
            """, params)
            
            # Return updated strategy
            return await get_strategy(strategy_id)
            
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Database error updating strategy: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error updating strategy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/{strategy_id}")
async def delete_strategy(strategy_id: int):
    """Delete a strategy and all associated data"""
    try:
        with get_db_cursor() as cursor:
            # Check if strategy exists
            cursor.execute("SELECT id FROM strategies WHERE id = %s", (strategy_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Strategy not found")
            
            # Delete strategy (CASCADE will handle related data)
            cursor.execute("DELETE FROM strategies WHERE id = %s", (strategy_id,))
            
            return {"message": "Strategy deleted successfully"}
            
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Database error deleting strategy: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error deleting strategy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{strategy_id}/data-summary")
async def get_strategy_data_summary(strategy_id: int):
    """Get data summary for a strategy"""
    try:
        with get_db_cursor() as cursor:
            # Check if strategy exists
            cursor.execute("SELECT name, symbol, timeframe FROM strategies WHERE id = %s", (strategy_id,))
            strategy = cursor.fetchone()
            if not strategy:
                raise HTTPException(status_code=404, detail="Strategy not found")
            
            # Get OHLCV data summary
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_candles,
                    MIN(timestamp) as earliest_date,
                    MAX(timestamp) as latest_date,
                    COUNT(DISTINCT DATE(timestamp)) as trading_days
                FROM ohlcv_data 
                WHERE strategy_id = %s
            """, (strategy_id,))
            
            ohlcv_summary = cursor.fetchone() or {
                'total_candles': 0, 'earliest_date': None, 
                'latest_date': None, 'trading_days': 0
            }
            
            # Get indicators summary
            cursor.execute("""
                SELECT 
                    indicator_name,
                    COUNT(*) as count
                FROM indicators_data 
                WHERE strategy_id = %s
                GROUP BY indicator_name
            """, (strategy_id,))
            
            indicators_summary = cursor.fetchall()
            
            # Get trades summary
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_trades,
                    AVG(profit_pct) as avg_profit_pct,
                    SUM(CASE WHEN profit_pct > 0 THEN 1 ELSE 0 END) as winning_trades
                FROM strategy_log 
                WHERE strategy_id = %s
            """, (strategy_id,))
            
            trades_summary = cursor.fetchone() or {
                'total_trades': 0, 'avg_profit_pct': 0, 'winning_trades': 0
            }
            
            return {
                'strategy': strategy,
                'ohlcv_data': ohlcv_summary,
                'indicators': indicators_summary,
                'trades': trades_summary
            }
            
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Database error getting strategy summary: {e}")
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(f"Error getting strategy summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
