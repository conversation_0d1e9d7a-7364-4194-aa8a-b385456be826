#!/usr/bin/env python3
"""
Configuration Validation Script
"""
import sys
import os
from pathlib import Path
import pymysql
from pymysql import Error

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

def validate_env_file():
    """Validate .env file exists and has required variables"""
    print("🔍 Validating .env file...")
    
    env_file = Path(__file__).parent.parent / ".env"
    
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Read .env file
    env_vars = {}
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    # Required variables
    required_vars = [
        'DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_PASSWORD', 'DB_DATABASE',
        'BINANCE_API_KEY', 'BINANCE_API_SECRET',
        'MEXC_API_KEY', 'MEXC_API_SECRET'
    ]
    
    missing_vars = []
    for var in required_vars:
        if var not in env_vars or not env_vars[var]:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing or empty environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ .env file validation passed")
    return True

def validate_database_connection():
    """Validate database connection"""
    print("🔍 Validating database connection...")
    
    try:
        from app.core.config import settings
        
        # Test connection using PyMySQL
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            charset='utf8mb4',
            autocommit=False
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Error as e:
        print(f"❌ Database connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def validate_exchange_apis():
    """Validate exchange API connections"""
    print("🔍 Validating exchange API connections...")
    
    try:
        from app.services.binance_client import BinanceClient
        from app.services.mexc_client import MEXCClient
        
        # Test Binance
        try:
            binance = BinanceClient()
            binance_ok = binance.test_connection()
            if binance_ok:
                print("✅ Binance API connection successful")
            else:
                print("⚠️ Binance API connection failed")
        except Exception as e:
            print(f"⚠️ Binance API error: {e}")
            binance_ok = False
        
        # Test MEXC
        try:
            mexc = MEXCClient()
            mexc_ok = mexc.test_connection()
            if mexc_ok:
                print("✅ MEXC API connection successful")
            else:
                print("⚠️ MEXC API connection failed")
        except Exception as e:
            print(f"⚠️ MEXC API error: {e}")
            mexc_ok = False
        
        if binance_ok or mexc_ok:
            print("✅ At least one exchange API is working")
            return True
        else:
            print("⚠️ No exchange APIs are working (this is not critical)")
            return True  # Not critical for basic functionality
            
    except Exception as e:
        print(f"❌ Error testing exchange APIs: {e}")
        return False

def validate_database_tables():
    """Validate database tables exist"""
    print("🔍 Validating database tables...")
    
    try:
        from app.core.config import settings
        
        connection = pymysql.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            charset='utf8mb4',
            autocommit=False
        )
        
        cursor = connection.cursor()
        
        # Check if required tables exist
        required_tables = ['ohlcv_data', 'indicators_data', 'manual_marks', 'strategy_log']
        
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        cursor.close()
        connection.close()
        
        if missing_tables:
            print(f"⚠️ Missing database tables: {', '.join(missing_tables)}")
            print("💡 Run 'python scripts/setup_database.py' to create tables")
            return False
        else:
            print("✅ All required database tables exist")
            return True
            
    except Exception as e:
        print(f"❌ Error checking database tables: {e}")
        return False

def main():
    """Main validation function"""
    print("🔧 Strategy Builder Configuration Validation")
    print("=" * 50)
    
    all_good = True
    
    # Validate .env file
    if not validate_env_file():
        all_good = False
    
    print()
    
    # Validate database connection
    if not validate_database_connection():
        all_good = False
    
    print()
    
    # Validate database tables
    if not validate_database_tables():
        all_good = False
    
    print()
    
    # Validate exchange APIs
    if not validate_exchange_apis():
        all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 All validations passed! Your configuration is ready.")
        print("🚀 You can now start the Strategy Builder server.")
    else:
        print("⚠️ Some validations failed. Please fix the issues above.")
        print("💡 Check the README.md for setup instructions.")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
