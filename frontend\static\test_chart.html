<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Test</title>
    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        #chart { width: 800px; height: 400px; margin: 20px; }
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { margin: 20px; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>TradingView Chart Test</h1>
    <div class="status" id="status">Loading...</div>
    <div id="chart"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const statusEl = document.getElementById('status');
            
            try {
                // Check if library is loaded
                if (typeof LightweightCharts === 'undefined') {
                    statusEl.textContent = 'Error: TradingView library not loaded';
                    statusEl.style.background = '#ffcccc';
                    return;
                }
                
                statusEl.textContent = 'TradingView library loaded successfully';
                statusEl.style.background = '#ccffcc';
                
                // Create chart
                const chart = LightweightCharts.createChart(document.getElementById('chart'), {
                    width: 800,
                    height: 400,
                    layout: {
                        backgroundColor: '#1a1a1a',
                        textColor: '#ffffff',
                    },
                });
                
                // Check if addCandlestickSeries exists
                if (typeof chart.addCandlestickSeries !== 'function') {
                    statusEl.textContent = 'Error: addCandlestickSeries method not available';
                    statusEl.style.background = '#ffcccc';
                    return;
                }
                
                // Create candlestick series
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#4bffb5',
                    downColor: '#ff4976',
                    borderDownColor: '#ff4976',
                    borderUpColor: '#4bffb5',
                    wickDownColor: '#ff4976',
                    wickUpColor: '#4bffb5',
                });
                
                // Add sample data
                const sampleData = [
                    { time: '2023-01-01', open: 100, high: 110, low: 95, close: 105 },
                    { time: '2023-01-02', open: 105, high: 115, low: 100, close: 108 },
                    { time: '2023-01-03', open: 108, high: 112, low: 103, close: 107 },
                    { time: '2023-01-04', open: 107, high: 120, low: 105, close: 118 },
                    { time: '2023-01-05', open: 118, high: 125, low: 115, close: 122 },
                ];
                
                candlestickSeries.setData(sampleData);
                
                statusEl.textContent = 'Chart created successfully with sample data!';
                statusEl.style.background = '#ccffcc';
                
            } catch (error) {
                console.error('Chart test error:', error);
                statusEl.textContent = 'Error: ' + error.message;
                statusEl.style.background = '#ffcccc';
            }
        });
    </script>
</body>
</html>
