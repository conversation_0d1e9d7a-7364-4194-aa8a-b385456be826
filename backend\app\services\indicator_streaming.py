"""
Real-time Indicator Streaming Service
Provides efficient streaming updates for indicators with minimal recalculation
"""

import logging
import asyncio
import json
from typing import Dict, List, Any, Optional, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
import weakref

from app.services.multi_indicator_engine import MultiIndicatorEngine
from app.services.indicator_cache import CacheManager
from app.core.data_access import OHLCVDataAccess

logger = logging.getLogger(__name__)

@dataclass
class StreamSubscription:
    """Subscription for indicator streaming"""
    symbol: str
    timeframe: str
    indicators: Dict[str, Dict[str, Any]]
    callback: Callable
    last_update: datetime = field(default_factory=datetime.now)
    active: bool = True

@dataclass
class IndicatorUpdate:
    """Indicator update data"""
    symbol: str
    timeframe: str
    timestamp: datetime
    indicator_name: str
    values: Dict[str, Any]
    is_final: bool = True  # False for preliminary updates

class IndicatorStreamingService:
    """
    Manages real-time indicator streaming with efficient incremental updates
    """
    
    def __init__(self, max_history_size: int = 1000):
        self.subscriptions: Dict[str, StreamSubscription] = {}
        self.max_history_size = max_history_size
        self.update_queue = asyncio.Queue()
        self.running = False
        self.update_task = None
        
        # Keep recent OHLCV data for incremental calculations
        self.ohlcv_buffers: Dict[str, deque] = {}
        
        # Track which indicators support incremental updates
        self.incremental_indicators = {
            'EMA', 'SMA', 'RSI', 'MACD', 'BOLLINGER_BANDS'
        }
    
    async def start(self):
        """Start the streaming service"""
        if self.running:
            return
        
        self.running = True
        self.update_task = asyncio.create_task(self._process_updates())
        logger.info("Indicator streaming service started")
    
    async def stop(self):
        """Stop the streaming service"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("Indicator streaming service stopped")
    
    def subscribe(self, subscription_id: str, symbol: str, timeframe: str,
                  indicators: Dict[str, Dict[str, Any]], callback: Callable) -> str:
        """
        Subscribe to indicator updates
        
        Args:
            subscription_id: Unique identifier for subscription
            symbol: Trading symbol
            timeframe: Chart timeframe
            indicators: Dictionary of indicator configurations
            callback: Function to call with updates
            
        Returns:
            Subscription ID
        """
        subscription = StreamSubscription(
            symbol=symbol.upper(),
            timeframe=timeframe,
            indicators=indicators,
            callback=callback
        )
        
        self.subscriptions[subscription_id] = subscription
        
        # Initialize OHLCV buffer for this symbol/timeframe
        buffer_key = f"{symbol.upper()}:{timeframe}"
        if buffer_key not in self.ohlcv_buffers:
            self.ohlcv_buffers[buffer_key] = deque(maxlen=self.max_history_size)
            # Load recent historical data
            asyncio.create_task(self._initialize_buffer(buffer_key, symbol, timeframe))
        
        logger.info(f"Created indicator subscription: {subscription_id}")
        return subscription_id
    
    def unsubscribe(self, subscription_id: str):
        """Remove a subscription"""
        if subscription_id in self.subscriptions:
            self.subscriptions[subscription_id].active = False
            del self.subscriptions[subscription_id]
            logger.info(f"Removed indicator subscription: {subscription_id}")
    
    async def _initialize_buffer(self, buffer_key: str, symbol: str, timeframe: str):
        """Initialize OHLCV buffer with recent historical data"""
        try:
            # Get recent data (last 500 candles should be enough for most indicators)
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)  # Adjust based on timeframe
            
            ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_time,
                end_time=end_time,
                limit=500
            )
            
            buffer = self.ohlcv_buffers[buffer_key]
            for candle in ohlcv_data:
                buffer.append(candle)
            
            logger.info(f"Initialized buffer for {buffer_key} with {len(buffer)} candles")
            
        except Exception as e:
            logger.error(f"Error initializing buffer for {buffer_key}: {e}")
    
    async def process_new_candle(self, symbol: str, timeframe: str, candle_data: Dict[str, Any]):
        """Process new OHLCV candle and trigger indicator updates"""
        buffer_key = f"{symbol.upper()}:{timeframe}"
        
        # Add to buffer
        if buffer_key in self.ohlcv_buffers:
            self.ohlcv_buffers[buffer_key].append(candle_data)
        
        # Queue update for processing
        await self.update_queue.put({
            'type': 'new_candle',
            'symbol': symbol.upper(),
            'timeframe': timeframe,
            'candle': candle_data,
            'timestamp': datetime.now()
        })
    
    async def process_candle_update(self, symbol: str, timeframe: str, candle_data: Dict[str, Any]):
        """Process updated OHLCV candle (same timestamp, different values)"""
        buffer_key = f"{symbol.upper()}:{timeframe}"
        
        # Update last candle in buffer
        if buffer_key in self.ohlcv_buffers and self.ohlcv_buffers[buffer_key]:
            buffer = self.ohlcv_buffers[buffer_key]
            if buffer[-1]['timestamp'] == candle_data['timestamp']:
                buffer[-1] = candle_data
        
        # Queue update for processing
        await self.update_queue.put({
            'type': 'candle_update',
            'symbol': symbol.upper(),
            'timeframe': timeframe,
            'candle': candle_data,
            'timestamp': datetime.now(),
            'is_final': False  # Preliminary update
        })
    
    async def _process_updates(self):
        """Main update processing loop"""
        while self.running:
            try:
                # Wait for update with timeout
                update = await asyncio.wait_for(self.update_queue.get(), timeout=1.0)
                await self._handle_update(update)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing update: {e}")
    
    async def _handle_update(self, update: Dict[str, Any]):
        """Handle a single update"""
        symbol = update['symbol']
        timeframe = update['timeframe']
        
        # Find relevant subscriptions
        relevant_subs = [
            sub for sub in self.subscriptions.values()
            if sub.active and sub.symbol == symbol and sub.timeframe == timeframe
        ]
        
        if not relevant_subs:
            return
        
        # Calculate indicators for all relevant subscriptions
        for subscription in relevant_subs:
            try:
                await self._calculate_and_notify(subscription, update)
            except Exception as e:
                logger.error(f"Error calculating indicators for subscription: {e}")
    
    async def _calculate_and_notify(self, subscription: StreamSubscription, update: Dict[str, Any]):
        """Calculate indicators and notify subscriber"""
        buffer_key = f"{subscription.symbol}:{subscription.timeframe}"
        buffer = self.ohlcv_buffers.get(buffer_key)
        
        if not buffer or len(buffer) < 2:
            return
        
        # Convert buffer to list for calculation
        ohlcv_data = list(buffer)
        
        # Check cache first for efficiency
        cache_results = {}
        for indicator_name, config in subscription.indicators.items():
            cached_data = await CacheManager.get_cached_indicator(
                subscription.symbol, subscription.timeframe, indicator_name, config
            )
            if cached_data:
                cache_results[indicator_name] = cached_data
        
        # Calculate indicators that aren't cached or need updates
        indicators_to_calculate = {}
        for indicator_name, config in subscription.indicators.items():
            if (indicator_name not in cache_results or 
                self._needs_recalculation(indicator_name, update)):
                indicators_to_calculate[indicator_name] = config
        
        if indicators_to_calculate:
            # Use incremental calculation if possible
            if self._can_use_incremental(indicators_to_calculate, update):
                new_results = await self._calculate_incremental(
                    subscription, indicators_to_calculate, ohlcv_data, update
                )
            else:
                # Full recalculation
                new_results = MultiIndicatorEngine.calculate_all_indicators(
                    ohlcv_data, indicators_to_calculate
                )
            
            # Cache new results
            for indicator_name, result in new_results.items():
                await CacheManager.cache_indicator_result(
                    subscription.symbol, subscription.timeframe, 
                    indicator_name, subscription.indicators[indicator_name], result
                )
            
            # Merge with cached results
            cache_results.update(new_results)
        
        # Create indicator updates
        indicator_updates = []
        for indicator_name, result in cache_results.items():
            if result and 'timestamps' in result:
                # Get the latest value
                latest_values = {}
                for key, values in result.items():
                    if key != 'timestamps' and values:
                        latest_values[key] = values[-1] if isinstance(values, list) else values
                
                indicator_update = IndicatorUpdate(
                    symbol=subscription.symbol,
                    timeframe=subscription.timeframe,
                    timestamp=update['timestamp'],
                    indicator_name=indicator_name,
                    values=latest_values,
                    is_final=update.get('is_final', True)
                )
                indicator_updates.append(indicator_update)
        
        # Notify subscriber
        if indicator_updates:
            try:
                # Use weak reference to avoid keeping callback alive
                callback = subscription.callback
                if callback:
                    await self._safe_callback(callback, indicator_updates)
                subscription.last_update = datetime.now()
            except Exception as e:
                logger.error(f"Error in subscriber callback: {e}")
    
    async def _safe_callback(self, callback: Callable, updates: List[IndicatorUpdate]):
        """Safely call subscriber callback"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(updates)
            else:
                callback(updates)
        except Exception as e:
            logger.error(f"Callback error: {e}")
    
    def _needs_recalculation(self, indicator_name: str, update: Dict[str, Any]) -> bool:
        """Determine if indicator needs recalculation"""
        # Always recalculate for new candles
        if update['type'] == 'new_candle':
            return True
        
        # For candle updates, only recalculate indicators that use current candle
        if update['type'] == 'candle_update':
            # Most indicators need recalculation on candle updates
            return indicator_name.upper() in self.incremental_indicators
        
        return False
    
    def _can_use_incremental(self, indicators: Dict[str, Dict[str, Any]], 
                           update: Dict[str, Any]) -> bool:
        """Check if incremental calculation can be used"""
        # For now, use full calculation for simplicity
        # In production, implement incremental calculation for supported indicators
        return False
    
    async def _calculate_incremental(self, subscription: StreamSubscription,
                                   indicators: Dict[str, Dict[str, Any]],
                                   ohlcv_data: List[Dict[str, Any]],
                                   update: Dict[str, Any]) -> Dict[str, Any]:
        """Perform incremental indicator calculation"""
        # Placeholder for incremental calculation logic
        # This would implement efficient updates for indicators like EMA, SMA, etc.
        return MultiIndicatorEngine.calculate_all_indicators(ohlcv_data, indicators)
    
    def get_subscription_stats(self) -> Dict[str, Any]:
        """Get streaming service statistics"""
        active_subs = sum(1 for sub in self.subscriptions.values() if sub.active)
        buffer_sizes = {key: len(buffer) for key, buffer in self.ohlcv_buffers.items()}
        
        return {
            'active_subscriptions': active_subs,
            'total_subscriptions': len(self.subscriptions),
            'buffer_count': len(self.ohlcv_buffers),
            'buffer_sizes': buffer_sizes,
            'queue_size': self.update_queue.qsize(),
            'running': self.running
        }

# Global streaming service instance
streaming_service = IndicatorStreamingService()

class StreamingManager:
    """High-level streaming management interface"""
    
    @staticmethod
    async def start_streaming():
        """Start the streaming service"""
        await streaming_service.start()
    
    @staticmethod
    async def stop_streaming():
        """Stop the streaming service"""
        await streaming_service.stop()
    
    @staticmethod
    def subscribe_to_indicators(subscription_id: str, symbol: str, timeframe: str,
                              indicators: Dict[str, Dict[str, Any]], 
                              callback: Callable) -> str:
        """Subscribe to indicator updates"""
        return streaming_service.subscribe(subscription_id, symbol, timeframe, indicators, callback)
    
    @staticmethod
    def unsubscribe_from_indicators(subscription_id: str):
        """Unsubscribe from indicator updates"""
        streaming_service.unsubscribe(subscription_id)
    
    @staticmethod
    async def process_market_update(symbol: str, timeframe: str, candle_data: Dict[str, Any]):
        """Process new market data"""
        await streaming_service.process_new_candle(symbol, timeframe, candle_data)
    
    @staticmethod
    def get_streaming_stats() -> Dict[str, Any]:
        """Get streaming service statistics"""
        return streaming_service.get_subscription_stats()
