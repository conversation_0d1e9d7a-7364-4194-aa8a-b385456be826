"""
Technical Indicators Service using pandas-ta
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging

# Try to import pandas_ta, fallback to manual implementation if it fails
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError as e:
    logging.warning(f"pandas_ta import failed: {e}. Using manual indicator implementations.")
    PANDAS_TA_AVAILABLE = False

logger = logging.getLogger(__name__)

class IndicatorsService:
    """Technical indicators calculation service"""
    
    @staticmethod
    def prepare_dataframe(ohlcv_data: List[Dict]) -> pd.DataFrame:
        """Convert OHLCV data to pandas DataFrame"""
        df = pd.DataFrame(ohlcv_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        df = df.astype(float)
        return df
    
    @staticmethod
    def calculate_rsi(df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate RSI (Relative Strength Index)"""
        try:
            if PANDAS_TA_AVAILABLE:
                rsi = ta.rsi(df['close'], length=period)
                return rsi.fillna(0)
            else:
                # Manual RSI calculation
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.fillna(50)  # Use 50 as neutral value
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return pd.Series([50] * len(df), index=df.index)
    
    @staticmethod
    def calculate_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        try:
            if PANDAS_TA_AVAILABLE:
                macd_data = ta.macd(df['close'], fast=fast, slow=slow, signal=signal)
                return {
                    'macd': macd_data[f'MACD_{fast}_{slow}_{signal}'].fillna(0),
                    'signal': macd_data[f'MACDs_{fast}_{slow}_{signal}'].fillna(0),
                    'histogram': macd_data[f'MACDh_{fast}_{slow}_{signal}'].fillna(0)
                }
            else:
                # Manual MACD calculation
                ema_fast = df['close'].ewm(span=fast).mean()
                ema_slow = df['close'].ewm(span=slow).mean()
                macd_line = ema_fast - ema_slow
                signal_line = macd_line.ewm(span=signal).mean()
                histogram = macd_line - signal_line
                return {
                    'macd': macd_line.fillna(0),
                    'signal': signal_line.fillna(0),
                    'histogram': histogram.fillna(0)
                }
        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return {
                'macd': pd.Series([0] * len(df), index=df.index),
                'signal': pd.Series([0] * len(df), index=df.index),
                'histogram': pd.Series([0] * len(df), index=df.index)
            }
    
    @staticmethod
    def calculate_ema(df: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate EMA (Exponential Moving Average)"""
        try:
            if PANDAS_TA_AVAILABLE:
                ema = ta.ema(df['close'], length=period)
                return ema.fillna(df['close'])
            else:
                # Manual EMA calculation
                ema = df['close'].ewm(span=period).mean()
                return ema.fillna(df['close'])
        except Exception as e:
            logger.error(f"Error calculating EMA: {e}")
            return df['close']
    
    @staticmethod
    def calculate_sma(df: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate SMA (Simple Moving Average)"""
        try:
            if PANDAS_TA_AVAILABLE:
                sma = ta.sma(df['close'], length=period)
                return sma.fillna(df['close'])
            else:
                # Manual SMA calculation
                sma = df['close'].rolling(window=period).mean()
                return sma.fillna(df['close'])
        except Exception as e:
            logger.error(f"Error calculating SMA: {e}")
            return df['close']
    
    @staticmethod
    def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std: float = 2) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands"""
        try:
            bb = ta.bbands(df['close'], length=period, std=std)
            return {
                'upper': bb[f'BBU_{period}_{std}'].fillna(df['close']),
                'middle': bb[f'BBM_{period}_{std}'].fillna(df['close']),
                'lower': bb[f'BBL_{period}_{std}'].fillna(df['close'])
            }
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return {
                'upper': df['close'],
                'middle': df['close'],
                'lower': df['close']
            }
    
    @classmethod
    def calculate_all_indicators(cls, ohlcv_data: List[Dict], 
                               indicators_config: Dict[str, Dict] = None) -> Dict[str, Any]:
        """
        Calculate all requested indicators
        
        Args:
            ohlcv_data: List of OHLCV dictionaries
            indicators_config: Configuration for indicators with parameters
        
        Returns:
            Dictionary containing all calculated indicators
        """
        if not ohlcv_data:
            return {}
        
        # Default configuration
        if indicators_config is None:
            indicators_config = {
                'rsi': {'period': 14},
                'macd': {'fast': 12, 'slow': 26, 'signal': 9},
                'ema': {'period': 20},
                'sma': {'period': 50},
                'bollinger_bands': {'period': 20, 'std': 2}
            }
        
        try:
            df = cls.prepare_dataframe(ohlcv_data)
            results = {}
            
            # Calculate RSI
            if 'rsi' in indicators_config:
                config = indicators_config['rsi']
                results['rsi'] = cls.calculate_rsi(df, config.get('period', 14)).tolist()
            
            # Calculate MACD
            if 'macd' in indicators_config:
                config = indicators_config['macd']
                macd_data = cls.calculate_macd(
                    df, 
                    config.get('fast', 12),
                    config.get('slow', 26),
                    config.get('signal', 9)
                )
                results['macd'] = {
                    'macd': macd_data['macd'].tolist(),
                    'signal': macd_data['signal'].tolist(),
                    'histogram': macd_data['histogram'].tolist()
                }
            
            # Calculate EMA
            if 'ema' in indicators_config:
                config = indicators_config['ema']
                results['ema'] = cls.calculate_ema(df, config.get('period', 20)).tolist()
            
            # Calculate SMA
            if 'sma' in indicators_config:
                config = indicators_config['sma']
                results['sma'] = cls.calculate_sma(df, config.get('period', 50)).tolist()
            
            # Calculate Bollinger Bands
            if 'bollinger_bands' in indicators_config:
                config = indicators_config['bollinger_bands']
                bb_data = cls.calculate_bollinger_bands(
                    df,
                    config.get('period', 20),
                    config.get('std', 2)
                )
                results['bollinger_bands'] = {
                    'upper': bb_data['upper'].tolist(),
                    'middle': bb_data['middle'].tolist(),
                    'lower': bb_data['lower'].tolist()
                }
            
            # Add timestamps for reference
            results['timestamps'] = [ts.isoformat() for ts in df.index]
            
            logger.info(f"Calculated indicators for {len(df)} data points")
            return results
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}
    
    @classmethod
    def get_indicator_value_at_timestamp(cls, ohlcv_data: List[Dict], 
                                       timestamp: str, 
                                       indicators_config: Dict[str, Dict] = None) -> Dict[str, Any]:
        """
        Get indicator values at a specific timestamp
        
        Args:
            ohlcv_data: List of OHLCV dictionaries
            timestamp: ISO timestamp string
            indicators_config: Configuration for indicators
        
        Returns:
            Dictionary containing indicator values at the specified timestamp
        """
        try:
            indicators = cls.calculate_all_indicators(ohlcv_data, indicators_config)
            
            if not indicators or 'timestamps' not in indicators:
                return {}
            
            # Find the index for the timestamp
            timestamps = indicators['timestamps']
            target_timestamp = pd.to_datetime(timestamp).isoformat()
            
            try:
                index = timestamps.index(target_timestamp)
            except ValueError:
                # Find closest timestamp
                target_dt = pd.to_datetime(timestamp)
                timestamps_dt = [pd.to_datetime(ts) for ts in timestamps]
                closest_index = min(range(len(timestamps_dt)), 
                                  key=lambda i: abs(timestamps_dt[i] - target_dt))
                index = closest_index
            
            # Extract values at the index
            result = {}
            for indicator, values in indicators.items():
                if indicator == 'timestamps':
                    continue
                
                if isinstance(values, list):
                    result[indicator] = values[index] if index < len(values) else None
                elif isinstance(values, dict):
                    result[indicator] = {}
                    for key, value_list in values.items():
                        result[indicator][key] = value_list[index] if index < len(value_list) else None
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting indicator value at timestamp: {e}")
            return {}
