"""
Batch OHLCV Data Fetcher Service
Handles intelligent batch fetching of OHLCV data with duplicate prevention
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import asyncio

from app.core.data_access import OHLCVDataAccess
from app.core.exceptions import ExchangeError, ValidationError
from app.services.binance_client import BinanceClient
from app.services.mexc_client import MEXCClient

logger = logging.getLogger(__name__)

class BatchOHLCVFetcher:
    """Intelligent batch OHLCV data fetcher"""
    
    def __init__(self):
        self.max_batch_size = 1000  # Maximum candles per request
        self.batch_delay = 0.1  # Delay between batches to respect rate limits
    
    async def fetch_complete_range(self, symbol: str, timeframe: str, exchange: str,
                                 start_time: datetime, end_time: datetime,
                                 strategy_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch complete date range using intelligent batch processing
        
        Args:
            symbol: Trading pair symbol
            timeframe: Timeframe (1m, 5m, 15m, 1h, 4h, 1d)
            exchange: Exchange name (binance, mexc)
            start_time: Start datetime
            end_time: End datetime
            strategy_id: Optional strategy ID
            
        Returns:
            Dict with success status, total fetched count, and batch details
        """
        try:
            logger.info(f"Starting batch fetch for {symbol} {timeframe} from {start_time} to {end_time}")
            
            # Step 1: Check existing data
            existing_data = OHLCVDataAccess.check_existing_data_range(
                symbol, timeframe, start_time, end_time, strategy_id
            )
            
            logger.info(f"Existing data check: {existing_data}")
            
            # Step 2: Get exchange client
            client = await self._get_exchange_client(exchange)
            
            # Step 3: Calculate time intervals for batching
            interval_minutes = self._get_interval_minutes(timeframe)
            total_expected_candles = self._calculate_expected_candles(
                start_time, end_time, interval_minutes
            )
            
            logger.info(f"Expected total candles: {total_expected_candles}")
            
            # Step 4: Generate batch ranges
            batch_ranges = self._generate_batch_ranges(
                start_time, end_time, interval_minutes, self.max_batch_size
            )
            
            logger.info(f"Generated {len(batch_ranges)} batch ranges")
            
            # Step 5: Process batches
            total_fetched = 0
            total_inserted = 0
            batch_results = []
            
            for i, batch_range in enumerate(batch_ranges):
                logger.info(f"Processing batch {i+1}/{len(batch_ranges)}: {batch_range['start']} to {batch_range['end']}")
                
                try:
                    # Check if this batch range already has data
                    batch_existing = OHLCVDataAccess.check_existing_data_range(
                        symbol, timeframe, batch_range['start'], batch_range['end'], strategy_id
                    )
                    
                    if batch_existing['exists'] and batch_existing['count'] >= batch_range['expected_count'] * 0.95:
                        logger.info(f"Batch {i+1} already has sufficient data ({batch_existing['count']} candles), skipping")
                        batch_results.append({
                            'batch': i+1,
                            'start': batch_range['start'],
                            'end': batch_range['end'],
                            'status': 'skipped',
                            'reason': 'data_exists',
                            'existing_count': batch_existing['count']
                        })
                        continue
                    
                    # Fetch data from exchange
                    ohlcv_data = await client.get_klines(
                        symbol=symbol,
                        interval=timeframe,
                        limit=self.max_batch_size,
                        start_time=batch_range['start'],
                        end_time=batch_range['end']
                    )
                    
                    if not ohlcv_data:
                        logger.warning(f"No data returned for batch {i+1}")
                        batch_results.append({
                            'batch': i+1,
                            'start': batch_range['start'],
                            'end': batch_range['end'],
                            'status': 'no_data',
                            'fetched': 0,
                            'inserted': 0
                        })
                        continue
                    
                    # Insert data
                    inserted_count = 0
                    for candle in ohlcv_data:
                        try:
                            rows_affected = OHLCVDataAccess.insert_ohlcv(
                                symbol=symbol,
                                timeframe=timeframe,
                                timestamp=candle['timestamp'],
                                open_price=candle['open'],
                                high=candle['high'],
                                low=candle['low'],
                                close=candle['close'],
                                volume=candle['volume'],
                                strategy_id=strategy_id,
                                exchange=exchange
                            )
                            inserted_count += rows_affected
                        except Exception as e:
                            logger.warning(f"Failed to insert candle: {e}")
                    
                    total_fetched += len(ohlcv_data)
                    total_inserted += inserted_count
                    
                    batch_results.append({
                        'batch': i+1,
                        'start': batch_range['start'],
                        'end': batch_range['end'],
                        'status': 'success',
                        'fetched': len(ohlcv_data),
                        'inserted': inserted_count,
                        'duplicates': len(ohlcv_data) - inserted_count
                    })
                    
                    logger.info(f"Batch {i+1} completed: fetched {len(ohlcv_data)}, inserted {inserted_count}")
                    
                    # Rate limiting delay
                    if i < len(batch_ranges) - 1:  # Don't delay after last batch
                        await asyncio.sleep(self.batch_delay)
                        
                except Exception as e:
                    logger.error(f"Error processing batch {i+1}: {e}")
                    batch_results.append({
                        'batch': i+1,
                        'start': batch_range['start'],
                        'end': batch_range['end'],
                        'status': 'error',
                        'error': str(e)
                    })
            
            # Step 6: Final validation
            final_data_check = OHLCVDataAccess.check_existing_data_range(
                symbol, timeframe, start_time, end_time, strategy_id
            )
            
            result = {
                'success': True,
                'symbol': symbol,
                'timeframe': timeframe,
                'exchange': exchange,
                'date_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat()
                },
                'initial_data': existing_data,
                'final_data': final_data_check,
                'batch_summary': {
                    'total_batches': len(batch_ranges),
                    'successful_batches': len([b for b in batch_results if b['status'] == 'success']),
                    'skipped_batches': len([b for b in batch_results if b['status'] == 'skipped']),
                    'failed_batches': len([b for b in batch_results if b['status'] == 'error'])
                },
                'data_summary': {
                    'total_fetched': total_fetched,
                    'total_inserted': total_inserted,
                    'total_duplicates': total_fetched - total_inserted,
                    'final_count': final_data_check['count']
                },
                'batch_details': batch_results
            }
            
            logger.info(f"Batch fetch completed: {result['data_summary']}")
            return result
            
        except Exception as e:
            logger.error(f"Batch fetch failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'symbol': symbol,
                'timeframe': timeframe,
                'exchange': exchange
            }
    
    async def _get_exchange_client(self, exchange: str):
        """Get exchange client instance"""
        if exchange.lower() == 'binance':
            return BinanceClient()
        elif exchange.lower() == 'mexc':
            return MEXCClient()
        else:
            raise ValidationError(f"Unsupported exchange: {exchange}")
    
    def _get_interval_minutes(self, timeframe: str) -> int:
        """Convert timeframe to minutes"""
        timeframe_map = {
            '1m': 1,
            '3m': 3,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '2h': 120,
            '4h': 240,
            '6h': 360,
            '8h': 480,
            '12h': 720,
            '1d': 1440,
            '3d': 4320,
            '1w': 10080,
            '1M': 43200  # Approximate
        }
        return timeframe_map.get(timeframe, 60)  # Default to 1 hour
    
    def _calculate_expected_candles(self, start_time: datetime, end_time: datetime, 
                                  interval_minutes: int) -> int:
        """Calculate expected number of candles in the time range"""
        total_minutes = (end_time - start_time).total_seconds() / 60
        return int(total_minutes / interval_minutes)
    
    def _generate_batch_ranges(self, start_time: datetime, end_time: datetime,
                             interval_minutes: int, batch_size: int) -> List[Dict[str, Any]]:
        """Generate batch time ranges for fetching"""
        ranges = []
        current_start = start_time
        
        # Calculate time span for each batch
        batch_duration_minutes = batch_size * interval_minutes
        batch_duration = timedelta(minutes=batch_duration_minutes)
        
        while current_start < end_time:
            current_end = min(current_start + batch_duration, end_time)
            expected_count = min(
                batch_size,
                int((current_end - current_start).total_seconds() / 60 / interval_minutes)
            )
            
            ranges.append({
                'start': current_start,
                'end': current_end,
                'expected_count': expected_count
            })
            
            current_start = current_end
        
        return ranges
