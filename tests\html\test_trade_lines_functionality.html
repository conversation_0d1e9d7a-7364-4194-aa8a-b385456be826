<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Lines Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1e53e5;
        }
        
        button.success {
            background: #4caf50;
        }
        
        button.danger {
            background: #f44336;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #26a69a; }
        .log-error { color: #ef5350; }
        .log-warning { color: #ff9800; }
        .log-info { color: #2196f3; }
        
        .feature-list {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .feature-list li {
            margin: 8px 0;
        }
        
        .color-demo {
            display: inline-block;
            width: 20px;
            height: 3px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .green-line { background: #4caf50; }
        .red-line { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Trade Lines Functionality Test</h1>
        
        <div class="test-section">
            <h2>Current Implementation Status</h2>
            <p>The trade line functionality is already implemented in the marking tools. Here's what it does:</p>
            
            <div class="feature-list">
                <h3>✅ Implemented Features:</h3>
                <ul>
                    <li><span class="color-demo green-line"></span><strong>Green Lines</strong>: BUY entries (connects entry price to exit price)</li>
                    <li><span class="color-demo red-line"></span><strong>Red Lines</strong>: SELL entries (connects entry price to exit price)</li>
                    <li><strong>Auto-linking</strong>: Exit marks automatically link to entries via <code>linked_trade_id</code></li>
                    <li><strong>Real-time Updates</strong>: Lines are redrawn when marks are added/removed</li>
                    <li><strong>Clean Management</strong>: Lines are properly cleaned up when marks are deleted</li>
                    <li><strong>Solid Lines</strong>: 2px width, solid style, no crosshair markers</li>
                </ul>
            </div>
            
            <div class="controls">
                <button id="check-implementation">Check Implementation</button>
                <button id="test-with-sample-data" class="success">Test with Sample Data</button>
                <button id="verify-on-chart">Verify on Chart</button>
                <button id="clear-log">Clear Log</button>
            </div>
            
            <div class="status" id="log-output">
                <div class="log-info">🔄 Ready to test trade lines functionality...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>How It Works</h2>
            <ol>
                <li><strong>Entry Creation</strong>: User creates an entry mark (BUY/SELL)</li>
                <li><strong>Exit Creation</strong>: User creates an exit mark linked to the entry via <code>linked_trade_id</code></li>
                <li><strong>Line Drawing</strong>: System automatically draws a line from entry price to exit price</li>
                <li><strong>Color Coding</strong>: Green for BUY entries, Red for SELL entries</li>
                <li><strong>Auto-cleanup</strong>: Lines are removed when marks are deleted</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>Code Implementation</h2>
            <p>The implementation is in <code>frontend/static/js/marking-tools.js</code>:</p>
            <ul>
                <li><code>createTradeLines()</code> - Main function that creates all trade lines</li>
                <li><code>createTradeLine(entryMark, exitMark)</code> - Creates individual line between entry and exit</li>
                <li><code>clearTradeLines()</code> - Cleans up all existing lines</li>
                <li><code>tradeLines Map</code> - Stores line series for management</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logOutput.appendChild(entry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function checkImplementation() {
            log('🔍 Checking trade lines implementation...');
            
            // Check if we're on the main page with marking tools
            if (typeof window.markingTools !== 'undefined' && window.markingTools) {
                log('✅ Marking tools found!', 'success');
                
                const markingTools = window.markingTools;
                
                // Check if trade lines map exists
                if (markingTools.tradeLines) {
                    log(`📊 Trade lines map exists with ${markingTools.tradeLines.size} lines`, 'success');
                } else {
                    log('❌ Trade lines map not found', 'error');
                }
                
                // Check if methods exist
                const methods = ['createTradeLines', 'createTradeLine', 'clearTradeLines'];
                methods.forEach(method => {
                    if (typeof markingTools[method] === 'function') {
                        log(`✅ Method ${method}() exists`, 'success');
                    } else {
                        log(`❌ Method ${method}() missing`, 'error');
                    }
                });
                
                // Check marks
                if (markingTools.marks) {
                    log(`📋 Marks map exists with ${markingTools.marks.size} marks`, 'info');
                    
                    let entryCount = 0;
                    let exitCount = 0;
                    let linkedCount = 0;
                    
                    markingTools.marks.forEach(mark => {
                        if (mark.mark_type === 'ENTRY') entryCount++;
                        if (mark.mark_type === 'EXIT') exitCount++;
                        if (mark.linked_trade_id) linkedCount++;
                    });
                    
                    log(`  - Entry marks: ${entryCount}`, 'info');
                    log(`  - Exit marks: ${exitCount}`, 'info');
                    log(`  - Linked marks: ${linkedCount}`, 'info');
                    
                    if (entryCount > 0 && exitCount > 0) {
                        log('💡 You should see trade lines on the chart if entries and exits are linked', 'info');
                    }
                }
                
            } else {
                log('❌ Marking tools not found', 'error');
                log('💡 This test should be run on the main trading page', 'warning');
            }
        }
        
        async function testWithSampleData() {
            log('🧪 Testing with sample data...');
            
            try {
                // Create a test entry
                log('📝 Creating test entry mark...');
                
                const entryResponse = await fetch('/api/v1/trades/mark', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        symbol: 'BTCUSDT',
                        timeframe: '15m',
                        mark_type: 'entry',
                        entry_side: 'buy',
                        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        price: 45000.50
                    })
                });
                
                const entryResult = await entryResponse.json();
                
                if (entryResult.success) {
                    const entryId = entryResult.data.mark_id;
                    log(`✅ Test entry created: ID ${entryId}`, 'success');
                    
                    // Create a test exit
                    log('📝 Creating test exit mark...');
                    
                    const exitResponse = await fetch('/api/v1/trades/mark', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            symbol: 'BTCUSDT',
                            timeframe: '15m',
                            mark_type: 'exit',
                            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                            price: 45500.75,
                            linked_trade_id: entryId
                        })
                    });
                    
                    const exitResult = await exitResponse.json();
                    
                    if (exitResult.success) {
                        const exitId = exitResult.data.mark_id;
                        log(`✅ Test exit created: ID ${exitId}`, 'success');
                        log(`🔗 Linked entry ${entryId} to exit ${exitId}`, 'success');
                        
                        const profit = ((45500.75 - 45000.50) / 45000.50 * 100).toFixed(2);
                        log(`📈 Profit: ${profit}% (GREEN line should appear)`, 'success');
                        
                        log('🎉 Test complete! Check the main chart for the trade line', 'success');
                        
                        // Store IDs for cleanup
                        window.testEntryId = entryId;
                        window.testExitId = exitId;
                        
                    } else {
                        log(`❌ Failed to create exit: ${exitResult.message}`, 'error');
                    }
                } else {
                    log(`❌ Failed to create entry: ${entryResult.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Error testing with sample data: ${error.message}`, 'error');
            }
        }
        
        function verifyOnChart() {
            log('🔍 Verifying trade lines on chart...');
            
            if (typeof window.markingTools !== 'undefined' && window.markingTools) {
                const markingTools = window.markingTools;
                
                log(`📊 Current trade lines: ${markingTools.tradeLines.size}`, 'info');
                
                if (markingTools.tradeLines.size > 0) {
                    log('✅ Trade lines are active!', 'success');
                    
                    markingTools.tradeLines.forEach((lineSeries, lineId) => {
                        log(`  - Line: ${lineId}`, 'success');
                    });
                    
                    log('💡 Look at the chart - you should see colored lines connecting entry and exit points', 'info');
                } else {
                    log('⚠️ No trade lines found', 'warning');
                    log('💡 Create some entry and exit marks to see trade lines', 'info');
                }
                
                // Trigger a refresh to ensure lines are drawn
                log('🔄 Refreshing chart markers and trade lines...');
                markingTools.refreshChartMarkers();
                log('✅ Refresh complete', 'success');
                
            } else {
                log('❌ Cannot verify - marking tools not available', 'error');
                log('💡 Run this on the main trading page', 'warning');
            }
        }
        
        // Event listeners
        document.getElementById('check-implementation').addEventListener('click', checkImplementation);
        document.getElementById('test-with-sample-data').addEventListener('click', testWithSampleData);
        document.getElementById('verify-on-chart').addEventListener('click', verifyOnChart);
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('log-output').innerHTML = '<div class="log-info">🔄 Log cleared...</div>';
        });
        
        log('🚀 Trade lines test ready');
        log('💡 The trade line functionality is already implemented and should be working');
    </script>
</body>
</html>
