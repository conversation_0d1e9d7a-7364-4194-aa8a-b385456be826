-- Strategy Builder Database Initialization Script
-- Run this script to create the database and user

-- Create database
CREATE DATABASE IF NOT EXISTS strategy_builder 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE strategy_builder;

-- Create user (optional - adjust as needed)
-- CREATE USER IF NOT EXISTS 'strategy_user'@'localhost' IDENTIFIED BY 'strategy_password';
-- GRANT ALL PRIVILEGES ON strategy_builder.* TO 'strategy_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Note: Tables will be created automatically by SQLAlchemy when the application starts
-- This script is mainly for database and user creation
