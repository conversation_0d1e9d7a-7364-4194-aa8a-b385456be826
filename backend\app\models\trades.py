"""
Trade Management Models
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, JSON, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base

class MarkType(enum.Enum):
    """Trade mark types"""
    ENTRY = "entry"
    EXIT = "exit"

class EntrySide(enum.Enum):
    """Trade entry sides"""
    BUY = "buy"
    SELL = "sell"

class ManualMarks(Base):
    """Manual trade marks model"""
    __tablename__ = "manual_marks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    mark_type = Column(Enum(MarkType), nullable=False)
    entry_side = Column(Enum(EntrySide), nullable=True)  # Only for entry marks
    timestamp = Column(DateTime, nullable=False)
    price = Column(Float, nullable=False)
    indicator_snapshot = Column(JSON, nullable=True)  # Store indicator values at mark time
    ohlcv_snapshot = Column(JSON, nullable=True)  # Store OHLCV data at mark time
    linked_trade_id = Column(Integer, nullable=True)  # Link entry and exit marks
    created_at = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<ManualMarks(id={self.id}, symbol='{self.symbol}', mark_type='{self.mark_type}', price={self.price})>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'mark_type': self.mark_type.value if self.mark_type else None,
            'entry_side': self.entry_side.value if self.entry_side else None,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'price': self.price,
            'indicator_snapshot': self.indicator_snapshot,
            'ohlcv_snapshot': self.ohlcv_snapshot,
            'linked_trade_id': self.linked_trade_id
        }

class StrategyLog(Base):
    """Strategy log for completed trades"""
    __tablename__ = "strategy_log"
    
    strategy_id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    entry_id = Column(Integer, ForeignKey('manual_marks.id'), nullable=False)
    exit_id = Column(Integer, ForeignKey('manual_marks.id'), nullable=False)
    entry_side = Column(Enum(EntrySide), nullable=False)
    profit_pct = Column(Float, nullable=False)
    entry_ohlcv = Column(JSON, nullable=True)
    exit_ohlcv = Column(JSON, nullable=True)
    entry_indicator_snapshot = Column(JSON, nullable=True)
    exit_indicator_snapshot = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    entry_mark = relationship("ManualMarks", foreign_keys=[entry_id])
    exit_mark = relationship("ManualMarks", foreign_keys=[exit_id])
    
    def __repr__(self):
        return f"<StrategyLog(id={self.strategy_id}, symbol='{self.symbol}', profit_pct={self.profit_pct})>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'entry_id': self.entry_id,
            'exit_id': self.exit_id,
            'entry_side': self.entry_side.value if self.entry_side else None,
            'profit_pct': self.profit_pct,
            'entry_ohlcv': self.entry_ohlcv,
            'exit_ohlcv': self.exit_ohlcv,
            'entry_indicator_snapshot': self.entry_indicator_snapshot,
            'exit_indicator_snapshot': self.exit_indicator_snapshot,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
