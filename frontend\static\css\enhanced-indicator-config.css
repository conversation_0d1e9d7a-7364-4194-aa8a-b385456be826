/* Enhanced Multi-Indicator Configuration Styles */

/* Enhanced Control Panel */
.enhanced-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
    border-bottom: 1px solid #363c4e;
    border-radius: 8px 8px 0 0;
}

.config-mode-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.mode-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #d1d4dc;
}

.mode-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4CAF50;
}

.preset-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

#preset-selector {
    background: #1e1e1e;
    border: 1px solid #555;
    border-radius: 4px;
    color: #d1d4dc;
    padding: 6px 12px;
    font-size: 14px;
    min-width: 200px;
}

.btn-preset {
    padding: 6px 12px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.3s ease;
}

.btn-preset:hover {
    background: #1976D2;
}

.btn-preset.save {
    background: #4CAF50;
}

.btn-preset.save:hover {
    background: #45a049;
}

/* Undo/Redo Controls */
.history-controls {
    display: flex;
    gap: 5px;
}

.btn-history {
    width: 32px;
    height: 32px;
    background: #333;
    color: #d1d4dc;
    border: 1px solid #555;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-history:hover:not(:disabled) {
    background: #444;
    border-color: #666;
}

.btn-history:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Bulk Operation Mode */
.bulk-mode .indicator-panel {
    border-left: 3px solid #FF9800;
}

.bulk-select-checkbox {
    width: 18px;
    height: 18px;
    margin-right: 12px;
    accent-color: #FF9800;
}

.bulk-operations {
    display: none;
    padding: 12px 16px;
    background: #2a2a2a;
    border-bottom: 1px solid #444;
    gap: 10px;
}

.bulk-mode .bulk-operations {
    display: flex;
}

.btn-bulk {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s ease;
}

.btn-bulk:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-bulk.enable {
    background: #4CAF50;
    color: white;
}

.btn-bulk.disable {
    background: #FF9800;
    color: white;
}

.btn-bulk.delete {
    background: #f44336;
    color: white;
}

.btn-bulk:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Drag and Drop */
.indicator-panel.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
}

.indicator-panel.drag-over {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.drag-handle {
    cursor: grab;
    padding: 4px;
    color: #888;
    font-size: 16px;
    margin-right: 8px;
}

.drag-handle:hover {
    color: #d1d4dc;
}

.drag-handle:active {
    cursor: grabbing;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 4px;
    margin-left: auto;
}

.btn-quick-action {
    width: 28px;
    height: 28px;
    background: transparent;
    border: 1px solid #555;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.btn-quick-action:hover {
    opacity: 1;
    background: #444;
    border-color: #666;
}

.btn-duplicate {
    color: #2196F3;
}

.btn-reset {
    color: #FF9800;
}

/* Enhanced Tooltips */
.enhanced-tooltip {
    position: absolute;
    background: #1e1e1e;
    color: #d1d4dc;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    border: 1px solid #555;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 10000;
    max-width: 250px;
    word-wrap: break-word;
    pointer-events: none;
}

.enhanced-tooltip::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

/* Preview Mode Indicators */
.preview-mode .indicator-panel {
    border-left: 3px solid #2196F3;
}

.preview-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #2196F3;
    border-radius: 50%;
    animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
}

/* Message Container */
.config-messages-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.config-message {
    margin-bottom: 10px;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.config-message.success {
    background: #4CAF50;
    color: white;
    border-left: 4px solid #45a049;
}

.config-message.error {
    background: #f44336;
    color: white;
    border-left: 4px solid #da190b;
}

.config-message.info {
    background: #2196F3;
    color: white;
    border-left: 4px solid #1976D2;
}

/* Import/Export Controls */
.import-export-controls {
    display: flex;
    gap: 10px;
    margin-left: 20px;
}

.btn-import-export {
    padding: 6px 12px;
    background: #666;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.3s ease;
}

.btn-import-export:hover {
    background: #777;
}

.file-input-hidden {
    display: none;
}

/* Enhanced Panel Animations */
.indicator-panel {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.indicator-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.indicator-panel.expanded {
    transform: translateY(0);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .enhanced-config-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .config-mode-controls {
        justify-content: space-between;
    }
    
    .preset-controls {
        flex-wrap: wrap;
    }
    
    #preset-selector {
        min-width: 150px;
    }
    
    .quick-actions {
        display: none;
    }
    
    .config-messages-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .enhanced-tooltip {
        background: #0d1117;
        border-color: #30363d;
    }
    
    .config-message {
        box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    }
}
