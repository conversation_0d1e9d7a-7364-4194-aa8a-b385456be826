<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infinite History TradingView Chart - Binance Data</title>
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Trebuchet MS', Arial, sans-serif;
            background: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        .demo-interface {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Header */
        .demo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
            border-bottom: 1px solid #363c4e;
            min-height: 60px;
        }

        .demo-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .title-text {
            font-size: 20px;
            font-weight: 700;
            color: #2962ff;
        }

        .subtitle {
            font-size: 14px;
            color: #b2b5be;
        }

        .demo-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .symbol-input {
            padding: 8px 12px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
            min-width: 120px;
        }

        .timeframe-buttons {
            display: flex;
            gap: 2px;
            background: #333;
            border-radius: 4px;
            padding: 2px;
        }

        .timeframe-btn {
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: #b2b5be;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .timeframe-btn:hover {
            background: #444;
            color: #fff;
        }

        .timeframe-btn.active {
            background: #2962ff;
            color: white;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #2962ff;
            color: white;
        }

        .btn-primary:hover {
            background: #1e88e5;
        }

        /* Chart Container */
        .chart-container {
            flex: 1;
            position: relative;
            background: #131722;
        }

        #infinite-chart {
            width: 100%;
            height: 100%;
        }

        /* Info Panel */
        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(30, 34, 45, 0.9);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #363c4e;
            min-width: 300px;
            z-index: 100;
        }

        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #2962ff;
            font-size: 16px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .info-label {
            color: #b2b5be;
        }

        .info-value {
            color: #d1d4dc;
            font-weight: 600;
        }

        .loading-indicator {
            color: #26a69a;
            font-weight: 600;
        }

        /* Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: #1e222d;
            border-top: 1px solid #363c4e;
            font-size: 12px;
            min-height: 36px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status.error {
            color: #ef5350;
        }

        .status.info {
            color: #26a69a;
        }

        .instructions {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid #2962ff;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 12px;
            color: #d1d4dc;
        }

        .instructions strong {
            color: #2962ff;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .demo-header {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }

            .info-panel {
                position: relative;
                top: 0;
                left: 0;
                margin: 10px;
                width: calc(100% - 20px);
            }
        }
    </style>
</head>
<body>
    <div class="demo-interface">
        <!-- Header -->
        <div class="demo-header">
            <div class="demo-title">
                <div>
                    <div class="title-text">Infinite History Chart</div>
                    <div class="subtitle">Scroll left to load more historical data</div>
                </div>
            </div>
            
            <div class="demo-controls">
                <input type="text" class="symbol-input" id="symbol-input" placeholder="Symbol" value="BTCUSDT">
                <button class="btn btn-primary" id="change-symbol">Change</button>
                
                <div class="timeframe-buttons">
                    <button class="timeframe-btn active" data-interval="1m">1m</button>
                    <button class="timeframe-btn" data-interval="5m">5m</button>
                    <button class="timeframe-btn" data-interval="15m">15m</button>
                    <button class="timeframe-btn" data-interval="1h">1h</button>
                    <button class="timeframe-btn" data-interval="4h">4h</button>
                    <button class="timeframe-btn" data-interval="1d">1d</button>
                </div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container">
            <div id="infinite-chart"></div>
            
            <!-- Info Panel -->
            <div class="info-panel" id="info-panel">
                <h3>📊 Infinite History Demo</h3>
                
                <div class="instructions">
                    <strong>How to use:</strong> Scroll left on the chart to automatically load more historical data. 
                    The chart will seamlessly fetch older candles from Binance API.
                </div>
                
                <div class="info-item">
                    <span class="info-label">Symbol:</span>
                    <span class="info-value" id="current-symbol">BTCUSDT</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Timeframe:</span>
                    <span class="info-value" id="current-timeframe">1m</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Total Candles:</span>
                    <span class="info-value" id="total-candles">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Data Source:</span>
                    <span class="info-value" id="data-source">Binance API</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value" id="loading-status">Initializing...</span>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <div id="chart-status" class="status info">Ready to load infinite history...</div>
            </div>
            <div>
                <span>Powered by TradingView Lightweight Charts & Binance API</span>
            </div>
        </div>
    </div>

    <script src="js/tradingview-chart.js"></script>
    <script>
        // Infinite History Demo
        let infiniteChart = null;

        document.addEventListener('DOMContentLoaded', () => {
            initializeInfiniteChart();
            setupEventListeners();
        });

        function initializeInfiniteChart() {
            const initChart = () => {
                if (typeof LightweightCharts !== 'undefined') {
                    infiniteChart = new TradingViewChart('infinite-chart', {
                        symbol: 'BTCUSDT',
                        interval: '1m',
                        theme: 'dark',
                        enableWebSocket: true,
                        enableInfiniteHistory: true
                    });
                    
                    window.infiniteChart = infiniteChart;
                    
                    // Load initial data
                    infiniteChart.loadHistoricalData();
                    
                    // Update info panel
                    updateInfoPanel();
                    
                    console.log('Infinite history chart initialized');
                } else {
                    console.log('Waiting for TradingView library...');
                    setTimeout(initChart, 100);
                }
            };
            
            initChart();
        }

        function setupEventListeners() {
            // Symbol change
            document.getElementById('change-symbol').addEventListener('click', () => {
                const newSymbol = document.getElementById('symbol-input').value.trim().toUpperCase();
                if (newSymbol && infiniteChart) {
                    infiniteChart.changeSymbol(newSymbol);
                    document.getElementById('current-symbol').textContent = newSymbol;
                    updateInfoPanel();
                }
            });

            // Enter key support
            document.getElementById('symbol-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('change-symbol').click();
                }
            });

            // Timeframe buttons
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    
                    const interval = btn.dataset.interval;
                    if (infiniteChart) {
                        infiniteChart.changeInterval(interval);
                        document.getElementById('current-timeframe').textContent = interval;
                        updateInfoPanel();
                    }
                });
            });
        }

        function updateInfoPanel() {
            if (infiniteChart && infiniteChart.dataFeed) {
                const totalCandles = infiniteChart.dataFeed.getData().length;
                document.getElementById('total-candles').textContent = totalCandles.toLocaleString();
                
                const hasMoreData = infiniteChart.dataFeed.hasMoreData;
                const status = hasMoreData ? 'Ready (scroll left for more)' : 'All data loaded';
                document.getElementById('loading-status').textContent = status;
            }
        }

        // Update info panel periodically
        setInterval(updateInfoPanel, 2000);
    </script>
</body>
</html>
