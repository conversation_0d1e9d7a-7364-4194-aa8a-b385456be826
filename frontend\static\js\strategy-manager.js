/**
 * Strategy Management System
 * Handles strategy creation, selection, and data linking
 */

class StrategyManager {
    constructor() {
        this.currentStrategy = null;
        this.strategies = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStrategies();
        this.setDefaultDates();
    }

    setupEventListeners() {
        // Strategy creation
        document.getElementById('create-strategy').addEventListener('click', () => {
            this.createStrategy();
        });

        // Strategy selection
        document.getElementById('strategy-select').addEventListener('change', (e) => {
            this.selectStrategy(e.target.value);
        });

        // Enhanced fetch data with strategy linking
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchDataWithStrategy();
        });

        // Load data from database
        document.getElementById('loadData').addEventListener('click', () => {
            this.loadDataFromDatabase();
        });

        // Enter key support for strategy name
        document.getElementById('new-strategy-name').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.createStrategy();
            }
        });
    }

    setDefaultDates() {
        // Set default end date to now
        const now = new Date();
        const endDate = now.toISOString().slice(0, 16);
        document.getElementById('end-date').value = endDate;

        // Set default start date to 30 days ago
        const startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        document.getElementById('start-date').value = startDate.toISOString().slice(0, 16);
    }

    async loadStrategies() {
        try {
            const response = await fetch('/api/v1/strategies/list');
            const strategies = await response.json();
            
            this.strategies = strategies;
            this.updateStrategyDropdown();
            
            console.log(`Loaded ${strategies.length} strategies`);
        } catch (error) {
            console.error('Error loading strategies:', error);
            this.showMessage('Error loading strategies', 'error');
        }
    }

    updateStrategyDropdown() {
        const select = document.getElementById('strategy-select');
        
        // Clear existing options except the first one
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add strategy options
        this.strategies.forEach(strategy => {
            const option = document.createElement('option');
            option.value = strategy.id;
            option.textContent = `${strategy.name} (${strategy.symbol}/${strategy.timeframe}) - ${strategy.data_count} candles`;
            select.appendChild(option);
        });
    }

    async createStrategy() {
        const nameInput = document.getElementById('new-strategy-name');
        const name = nameInput.value.trim();
        
        if (!name) {
            this.showMessage('Please enter a strategy name', 'error');
            return;
        }

        const symbol = document.getElementById('symbol').value.trim().toUpperCase();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;

        if (!symbol) {
            this.showMessage('Please enter a symbol', 'error');
            return;
        }

        try {
            const response = await fetch('/api/v1/strategies/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    description: `Strategy for ${symbol} on ${timeframe} timeframe`,
                    symbol: symbol,
                    timeframe: timeframe,
                    exchange: exchange
                })
            });

            if (response.ok) {
                const strategy = await response.json();
                this.showMessage(`Strategy "${strategy.name}" created successfully!`, 'success');
                
                // Clear input and reload strategies
                nameInput.value = '';
                await this.loadStrategies();
                
                // Auto-select the new strategy
                document.getElementById('strategy-select').value = strategy.id;
                this.selectStrategy(strategy.id);
                
            } else {
                const error = await response.json();
                this.showMessage(`Error creating strategy: ${error.detail}`, 'error');
            }
        } catch (error) {
            console.error('Error creating strategy:', error);
            this.showMessage('Error creating strategy', 'error');
        }
    }

    selectStrategy(strategyId) {
        if (!strategyId) {
            this.currentStrategy = null;
            this.updateLoadButton(false);
            return;
        }

        this.currentStrategy = this.strategies.find(s => s.id == strategyId);
        if (this.currentStrategy) {
            // Update form fields to match strategy
            document.getElementById('symbol').value = this.currentStrategy.symbol;
            document.getElementById('timeframe').value = this.currentStrategy.timeframe;
            document.getElementById('exchange').value = this.currentStrategy.exchange;
            
            // Update header if chart exists
            if (window.professionalChart) {
                document.getElementById('header-symbol').textContent = this.currentStrategy.symbol;
                document.getElementById('main-symbol-input').value = this.currentStrategy.symbol;
            }

            // Check if strategy has data to enable load button
            this.updateLoadButton(this.currentStrategy.data_count > 0);
            
            this.showMessage(`Selected strategy: ${this.currentStrategy.name}`, 'info');
        }
    }

    async fetchDataWithStrategy() {
        if (!this.currentStrategy) {
            this.showMessage('Please select or create a strategy first', 'error');
            return;
        }

        const symbol = document.getElementById('symbol').value.trim().toUpperCase();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        if (!symbol || !startDate || !endDate) {
            this.showMessage('Please fill in all required fields', 'error');
            return;
        }

        // Update data count display
        this.updateDataCountDisplay('Fetching data...');

        // Validate date range
        if (new Date(startDate) >= new Date(endDate)) {
            this.showMessage('Start date must be before end date', 'error');
            return;
        }

        try {
            this.showMessage('Fetching data from exchange...', 'info');
            document.getElementById('fetchData').disabled = true;

            const response = await fetch('/api/v1/ohlcv/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    symbol: symbol,
                    timeframe: timeframe,
                    exchange: exchange,
                    start_time: new Date(startDate).toISOString(),
                    end_time: new Date(endDate).toISOString(),
                    strategy_id: this.currentStrategy.id
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showFetchResult(result.message, 'success');
                
                // Reload strategies to update data count
                await this.loadStrategies();
                
                // Update current strategy data count
                const updatedStrategy = this.strategies.find(s => s.id === this.currentStrategy.id);
                if (updatedStrategy) {
                    this.currentStrategy = updatedStrategy;
                    this.updateLoadButton(true);
                }
                
                // Update chart if available
                if (window.professionalChart && result.data && result.data.ohlcv) {
                    this.loadDataToChart(result.data.ohlcv);
                    const dataCount = result.data.ohlcv.length;

                    // Enhanced message for batch fetching
                    let displayMessage = `${dataCount} candles in database`;

                    if (result.data.batch_result) {
                        const batchSummary = result.data.batch_result.batch_summary;
                        const dataSummary = result.data.batch_result.data_summary;

                        if (dataSummary.total_fetched > 0) {
                            displayMessage += ` | Fetched ${dataSummary.total_fetched} in ${batchSummary.total_batches} batches`;
                            displayMessage += ` | ${dataSummary.total_inserted} new, ${dataSummary.total_duplicates} duplicates`;
                        }

                        if (batchSummary.skipped_batches > 0) {
                            displayMessage += ` | ${batchSummary.skipped_batches} batches skipped`;
                        }
                    }

                    this.updateDataCountDisplay(displayMessage);
                } else if (result.data && result.data.data_count) {
                    this.updateDataCountDisplay(`${result.data.data_count} candles fetched and saved`);
                }
                
            } else {
                this.showFetchResult(`Error: ${result.error || result.message}`, 'error');
            }
            
        } catch (error) {
            console.error('Error fetching data:', error);
            this.showFetchResult('Error fetching data from exchange', 'error');
        } finally {
            document.getElementById('fetchData').disabled = false;
        }
    }

    async loadDataFromDatabase() {
        if (!this.currentStrategy) {
            this.showMessage('Please select a strategy first', 'error');
            return;
        }

        if (this.currentStrategy.data_count === 0) {
            this.showMessage('No data available for this strategy. Fetch data first.', 'error');
            return;
        }

        try {
            this.showMessage('Loading data from database...', 'info');
            
            const response = await fetch(`/api/v1/ohlcv/data?symbol=${this.currentStrategy.symbol}&timeframe=${this.currentStrategy.timeframe}&strategy_id=${this.currentStrategy.id}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.ohlcv) {
                const ohlcvData = result.data.ohlcv;
                console.log('📊 Sample raw OHLCV data from database:', ohlcvData.slice(-3));
                console.log('📊 Volume in raw data:', ohlcvData.slice(-3).map(item => ({ timestamp: item.timestamp, volume: item.volume })));

                this.loadDataToChart(ohlcvData);
                const dataCount = ohlcvData.length;
                this.showMessage(`Loaded ${dataCount} candles from database`, 'success');
                this.updateDataCountDisplay(`${dataCount} candles loaded from database`);
                console.log('Strategy Manager: Loaded', dataCount, 'candles from database');
            } else {
                this.showMessage('No data found in database', 'error');
                this.updateDataCountDisplay('No data found in database');
            }
            
        } catch (error) {
            console.error('Error loading data from database:', error);
            this.showMessage('Error loading data from database', 'error');
            this.updateDataCountDisplay('Error loading data');
        }
    }

    loadDataToChart(ohlcvData) {
        if (!window.professionalChart) {
            console.warn('Chart not available');
            return;
        }

        console.log('🔄 Processing OHLCV data for chart...');
        console.log('📊 Input data sample:', ohlcvData.slice(-2));

        try {
            // Process data for chart (include volume for OHLCV extraction)
            console.log('🔄 Processing raw OHLCV data for chart...');
            console.log('📊 Raw item sample:', ohlcvData.slice(-2));
            console.log('📊 Raw volume values:', ohlcvData.slice(-2).map(item => ({ timestamp: item.timestamp, volume: item.volume, volumeType: typeof item.volume })));

            const candleData = ohlcvData.map(item => {
                const processedItem = {
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    open: parseFloat(item.open),
                    high: parseFloat(item.high),
                    low: parseFloat(item.low),
                    close: parseFloat(item.close),
                    volume: parseFloat(item.volume) // Include volume for marking tools
                };

                // Debug volume processing
                if (item.volume !== undefined && item.volume !== null) {
                    console.log(`📊 Volume processing: ${item.volume} (${typeof item.volume}) -> ${processedItem.volume} (${typeof processedItem.volume})`);
                }

                return processedItem;
            }).sort((a, b) => a.time - b.time);

            console.log('📊 Processed candleData sample:', candleData.slice(-2));
            console.log('📊 Processed volume values:', candleData.slice(-2).map(c => ({ time: c.time, volume: c.volume })));

            // Get theme-appropriate volume colors
            const volumeColors = window.chartThemeManager ?
                window.chartThemeManager.getVolumeColors() :
                { up: '#26a69a80', down: '#ef535080' };

            const volumeData = ohlcvData.map(item => ({
                time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                value: parseFloat(item.volume),
                color: parseFloat(item.close) >= parseFloat(item.open) ? volumeColors.up : volumeColors.down,
            })).sort((a, b) => a.time - b.time);

            // Update chart
            window.professionalChart.candlestickSeries.setData(candleData);
            window.professionalChart.volumeSeries.setData(volumeData);

            // Store candleData with volume for marking tools access
            window.professionalChart.currentData = candleData;
            console.log('📊 Stored candleData with volume:', candleData.slice(-3));
            console.log('📊 Volume values in stored data:', candleData.slice(-3).map(c => c.volume));

            // Apply chart configuration first, then position to earliest data
            if (window.professionalChart.applyChartConfiguration) {
                window.professionalChart.applyChartConfiguration();
            }

            // Position chart to show earliest data instead of latest (with delay to ensure config is applied)
            setTimeout(() => {
                console.log('🔍 Strategy Manager: About to position chart to earliest data');
                console.log('📊 Candle data sample:', {
                    first: candleData[0],
                    firstDate: new Date(candleData[0].time * 1000).toISOString(),
                    last: candleData[candleData.length - 1],
                    lastDate: new Date(candleData[candleData.length - 1].time * 1000).toISOString(),
                    total: candleData.length
                });

                if (window.professionalChart.positionToEarliestData) {
                    console.log('✅ Calling positionToEarliestData method');
                    window.professionalChart.positionToEarliestData(candleData);
                } else {
                    console.log('❌ positionToEarliestData method not available, using fitContent');
                    // Fallback to fitContent if positioning method not available
                    window.professionalChart.chart.timeScale().fitContent();
                }
            }, 100);

            // Pass data to indicators manager
            if (window.indicatorsManager) {
                console.log('Passing data to indicators manager:', ohlcvData.length, 'items');
                console.log('Sample data item:', ohlcvData[0]);
                window.indicatorsManager.setCurrentData(ohlcvData);
            }



            console.log(`Loaded ${candleData.length} candles to chart`);

        } catch (error) {
            console.error('Error loading data to chart:', error);
            this.showMessage('Error displaying data on chart', 'error');
        }
    }

    updateLoadButton(hasData) {
        const loadButton = document.getElementById('loadData');
        loadButton.disabled = !hasData;
        
        if (hasData) {
            loadButton.textContent = 'Load from DB';
            loadButton.title = 'Load data from database';
        } else {
            loadButton.textContent = 'No Data';
            loadButton.title = 'No data available. Fetch data first.';
        }
    }

    showMessage(message, type = 'info') {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        console.log(`Strategy Manager: ${message}`);
    }

    showFetchResult(message, type = 'info') {
        const resultElement = document.getElementById('fetch-result');
        if (resultElement) {
            resultElement.textContent = message;
            resultElement.className = `fetch-result ${type}`;
            resultElement.style.display = 'block';
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                resultElement.style.display = 'none';
            }, 10000);
        }
        
        this.showMessage(message, type);
    }

    getCurrentStrategy() {
        return this.currentStrategy;
    }

    getStrategies() {
        return this.strategies;
    }

    updateDataCountDisplay(message) {
        const dataCountDisplay = document.getElementById('data-count-display');
        if (dataCountDisplay) {
            dataCountDisplay.textContent = message;
            console.log('Data count display updated:', message);
        }
    }
}

// Global strategy manager instance
window.strategyManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.strategyManager = new StrategyManager();
    console.log('Strategy Manager initialized');
});
