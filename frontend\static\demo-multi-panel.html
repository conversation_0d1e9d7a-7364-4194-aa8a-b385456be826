<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Panel Trading Chart Demo</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multi-panel.css">
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            font-family: 'Trebuchet MS', Arial, sans-serif;
        }
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            color: #d1d4dc;
        }
        .demo-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }
        .demo-btn {
            padding: 10px 20px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .demo-btn:hover {
            background: #1e88e5;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>Multi-Panel Trading Chart Demo</h1>
            <p>Professional TradingView-style interface with synchronized panels</p>
        </div>

        <div class="demo-controls">
            <button id="loadSampleData" class="demo-btn">Load Sample Data</button>
            <button id="loadBTCData" class="demo-btn">Load BTC Sample</button>
            <button id="loadETHData" class="demo-btn">Load ETH Sample</button>
            <button id="toggleVolume" class="demo-btn">Toggle Volume</button>
            <button id="toggleRSI" class="demo-btn">Toggle RSI</button>
            <button id="toggleMACD" class="demo-btn">Toggle MACD</button>
        </div>

        <!-- Trading Toolbar -->
        <div class="trading-toolbar">
            <div class="toolbar-section">
                <div class="symbol-selector">
                    <input type="text" id="symbolInput" placeholder="Enter Symbol (e.g., BTCUSDT)" value="BTCUSDT">
                    <button id="symbolSearch" class="btn btn-icon">Search</button>
                </div>
                
                <div class="timeframe-selector">
                    <button class="timeframe-btn active" data-timeframe="1m">1m</button>
                    <button class="timeframe-btn" data-timeframe="5m">5m</button>
                    <button class="timeframe-btn" data-timeframe="15m">15m</button>
                    <button class="timeframe-btn" data-timeframe="1h">1h</button>
                    <button class="timeframe-btn" data-timeframe="4h">4h</button>
                    <button class="timeframe-btn" data-timeframe="1d">1D</button>
                </div>
            </div>
            
            <div class="toolbar-section">
                <div class="chart-tools">
                    <button id="crosshairTool" class="tool-btn active" title="Crosshair">+</button>
                    <button id="trendlineTool" class="tool-btn" title="Trendline">\/</button>
                    <button id="rectangleTool" class="tool-btn" title="Rectangle">[]</button>
                    <button id="fibTool" class="tool-btn" title="Fibonacci">F</button>
                </div>

                <div class="view-controls">
                    <button id="resetZoom" class="btn btn-secondary">Reset Zoom</button>
                    <button id="fullscreen" class="btn btn-secondary">[ ]</button>
                </div>
            </div>
        </div>

        <!-- Multi-Panel Chart Container -->
        <div class="multi-panel-container">
            <!-- Main Price Chart Panel -->
            <div class="chart-panel main-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <span id="currentSymbol">BTCUSDT</span>
                        <span id="currentTimeframe">1m</span>
                        <span id="priceInfo" class="price-info">
                            <span class="price">$0.00</span>
                            <span class="change">+0.00%</span>
                        </span>
                    </div>
                    <div class="panel-controls">
                        <button class="panel-toggle" data-panel="main">-</button>
                    </div>
                </div>
                <div id="mainChart" class="chart-content"></div>
            </div>

            <!-- Volume Panel -->
            <div class="chart-panel volume-panel">
                <div class="panel-header">
                    <div class="panel-title">Volume</div>
                    <div class="panel-controls">
                        <button class="panel-toggle" data-panel="volume">-</button>
                    </div>
                </div>
                <div id="volumeChart" class="chart-content"></div>
            </div>

            <!-- RSI Panel -->
            <div class="chart-panel indicator-panel">
                <div class="panel-header">
                    <div class="panel-title">RSI (14)</div>
                    <div class="panel-controls">
                        <button class="panel-settings" data-indicator="rsi">Set</button>
                        <button class="panel-toggle" data-panel="rsi">-</button>
                    </div>
                </div>
                <div id="rsiChart" class="chart-content"></div>
            </div>

            <!-- MACD Panel -->
            <div class="chart-panel indicator-panel">
                <div class="panel-header">
                    <div class="panel-title">MACD (12, 26, 9)</div>
                    <div class="panel-controls">
                        <button class="panel-settings" data-indicator="macd">Set</button>
                        <button class="panel-toggle" data-panel="macd">-</button>
                    </div>
                </div>
                <div id="macdChart" class="chart-content"></div>
            </div>
        </div>

        <!-- Chart Status and Info -->
        <div class="chart-info-bar">
            <div id="chart-status">Ready to load data</div>
            <div id="crosshair-info"></div>
            <div id="performance-info"></div>
        </div>
    </div>

    <script src="js/multi-panel-chart.js"></script>
    <script>
        // Demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for chart manager to initialize
            setTimeout(() => {
                setupDemoControls();
            }, 1000);
        });

        function setupDemoControls() {
            // Load sample data button
            document.getElementById('loadSampleData').addEventListener('click', () => {
                loadSampleData('BTCUSDT', 50000);
            });

            document.getElementById('loadBTCData').addEventListener('click', () => {
                loadSampleData('BTCUSDT', 45000);
            });

            document.getElementById('loadETHData').addEventListener('click', () => {
                loadSampleData('ETHUSDT', 3000);
            });

            // Panel toggle buttons
            document.getElementById('toggleVolume').addEventListener('click', () => {
                document.querySelector('.volume-panel').classList.toggle('collapsed');
            });

            document.getElementById('toggleRSI').addEventListener('click', () => {
                document.querySelector('.indicator-panel:nth-of-type(3)').classList.toggle('collapsed');
            });

            document.getElementById('toggleMACD').addEventListener('click', () => {
                document.querySelector('.indicator-panel:nth-of-type(4)').classList.toggle('collapsed');
            });
        }

        function loadSampleData(symbol = 'BTCUSDT', basePrice = 50000) {
            const sampleData = [];
            const startTime = Math.floor(Date.now() / 1000) - (100 * 60);

            for (let i = 0; i < 100; i++) {
                const time = startTime + (i * 60);
                const open = basePrice + Math.random() * (basePrice * 0.1);
                const close = open + (Math.random() - 0.5) * (basePrice * 0.02);
                const high = Math.max(open, close) + Math.random() * (basePrice * 0.01);
                const low = Math.min(open, close) - Math.random() * (basePrice * 0.01);
                const volume = Math.random() * 1000000;

                sampleData.push({
                    timestamp: time,
                    open: open.toFixed(2),
                    high: high.toFixed(2),
                    low: low.toFixed(2),
                    close: close.toFixed(2),
                    volume: volume.toFixed(0)
                });
            }

            // Generate sample indicators
            const sampleIndicators = generateSampleIndicators(sampleData);

            // Update symbol display
            document.getElementById('currentSymbol').textContent = symbol;
            document.getElementById('symbolInput').value = symbol;

            // Load data into chart
            if (window.multiPanelChartManager) {
                window.multiPanelChartManager.loadData(sampleData, sampleIndicators);
                document.getElementById('chart-status').textContent = `Loaded ${sampleData.length} candles for ${symbol}`;
            }
        }

        function generateSampleIndicators(ohlcvData) {
            const closes = ohlcvData.map(d => parseFloat(d.close));
            
            // Simple RSI calculation (simplified for demo)
            const rsi = closes.map((_, index) => {
                if (index < 14) return null;
                return 30 + Math.random() * 40; // Random RSI between 30-70
            });
            
            // Simple MACD calculation (simplified for demo)
            const macd = closes.map((_, index) => {
                if (index < 26) return null;
                return (Math.random() - 0.5) * 2; // Random MACD
            });
            
            const signal = macd.map(value => {
                if (value === null) return null;
                return value + (Math.random() - 0.5) * 0.5; // Signal line
            });
            
            const histogram = macd.map((value, index) => {
                if (value === null || signal[index] === null) return null;
                return value - signal[index]; // Histogram
            });
            
            return {
                rsi: rsi.filter(v => v !== null),
                macd: {
                    macd: macd.filter(v => v !== null),
                    signal: signal.filter(v => v !== null),
                    histogram: histogram.filter(v => v !== null)
                }
            };
        }
    </script>
</body>
</html>
