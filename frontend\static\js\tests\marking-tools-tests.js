/**
 * Comprehensive test suite for marking tools functionality
 * Tests entry mark creation, exit mark data extraction, and error handling
 */

class MarkingToolsTests {
    constructor() {
        this.testResults = [];
        this.mockData = this.createMockData();
    }

    createMockData() {
        return {
            validOHLCVData: {
                time: 1627849200,
                open: 44950.0,
                high: 45100.0,
                low: 44900.0,
                close: 45000.0,
                volume: 1500.0
            },
            validIndicatorData: {
                ema: { ema_20: 44800.0, ema_50: 44600.0 },
                rsi: { rsi_14: 65.5 },
                macd: { macd: 120.5, signal: 110.2, histogram: 10.3 }
            },
            validEntryData: {
                timestamp: 1627849200,
                price: 45000.50,
                side: 'buy',
                quantity: 0.1,
                notes: 'Test entry'
            },
            validExitData: {
                entry_id: 1,
                timestamp: 1627935600,
                price: 46500.75,
                quantity: 0.1,
                notes: 'Test exit'
            },
            invalidData: {
                undefinedSide: null,
                emptySide: '',
                invalidTimestamp: 'invalid',
                invalidPrice: 'not-a-number',
                negativeQuantity: -1
            }
        };
    }

    // Test helper methods
    logTest(testName, passed, message = '') {
        const result = { testName, passed, message, timestamp: new Date().toISOString() };
        this.testResults.push(result);
        console.log(`${passed ? '✅' : '❌'} ${testName}: ${message}`);
        return passed;
    }

    // Mock MarkingTools class for testing
    createMockMarkingTools() {
        return {
            currentClickData: {
                time: 1627849200,
                price: 45000.50,
                candlestickData: this.mockData.validOHLCVData
            },
            marks: new Map(),
            
            // Mock methods
            normalizeOHLCVData: function(data) {
                if (!data) return null;
                if (data.time !== undefined && data.open !== undefined) {
                    return {
                        time: data.time,
                        open: parseFloat(data.open),
                        high: parseFloat(data.high),
                        low: parseFloat(data.low),
                        close: parseFloat(data.close),
                        volume: parseFloat(data.volume || 0)
                    };
                }
                return null;
            },
            
            parseOHLCVData: function(data) {
                if (!data) return null;
                try {
                    if (typeof data === 'string') {
                        return JSON.parse(data);
                    }
                    return data;
                } catch (error) {
                    return null;
                }
            },
            
            parseIndicatorData: function(data) {
                if (!data) return null;
                try {
                    if (typeof data === 'string') {
                        return JSON.parse(data);
                    }
                    return data;
                } catch (error) {
                    return null;
                }
            }
        };
    }

    // Test 1: toUpperCase() error prevention
    testToUpperCaseErrorPrevention() {
        console.log('\n=== Testing toUpperCase() Error Prevention ===');
        
        const testCases = [
            { side: null, expected: 'BUY' },
            { side: undefined, expected: 'BUY' },
            { side: '', expected: 'BUY' },
            { side: 'buy', expected: 'BUY' },
            { side: 'sell', expected: 'SELL' },
            { side: 'BUY', expected: 'BUY' }
        ];

        let allPassed = true;
        testCases.forEach((testCase, index) => {
            try {
                // Simulate the fixed logic
                let side = testCase.side;
                if (!side || typeof side !== 'string') {
                    side = 'buy';
                }
                const result = side.toUpperCase();
                
                const passed = result === testCase.expected;
                allPassed = allPassed && passed;
                this.logTest(
                    `toUpperCase Test ${index + 1}`,
                    passed,
                    `Input: ${testCase.side} → Output: ${result} (Expected: ${testCase.expected})`
                );
            } catch (error) {
                allPassed = false;
                this.logTest(
                    `toUpperCase Test ${index + 1}`,
                    false,
                    `Error: ${error.message}`
                );
            }
        });

        return this.logTest('toUpperCase Error Prevention', allPassed, 'All test cases passed');
    }

    // Test 2: OHLCV data normalization
    testOHLCVDataNormalization() {
        console.log('\n=== Testing OHLCV Data Normalization ===');
        
        const mockTools = this.createMockMarkingTools();
        const testCases = [
            {
                name: 'Standard format',
                input: this.mockData.validOHLCVData,
                shouldPass: true
            },
            {
                name: 'Abbreviated format',
                input: { t: 1627849200, o: 44950, h: 45100, l: 44900, c: 45000, v: 1500 },
                shouldPass: true
            },
            {
                name: 'Array format',
                input: [1627849200, 44950, 45100, 44900, 45000, 1500],
                shouldPass: true
            },
            {
                name: 'Null input',
                input: null,
                shouldPass: false
            },
            {
                name: 'Invalid format',
                input: { invalid: 'data' },
                shouldPass: false
            }
        ];

        let allPassed = true;
        testCases.forEach(testCase => {
            try {
                const result = mockTools.normalizeOHLCVData(testCase.input);
                const passed = testCase.shouldPass ? (result !== null) : (result === null);
                allPassed = allPassed && passed;
                
                this.logTest(
                    `OHLCV Normalization: ${testCase.name}`,
                    passed,
                    `Result: ${result ? 'Valid data' : 'Null'}`
                );
            } catch (error) {
                allPassed = false;
                this.logTest(
                    `OHLCV Normalization: ${testCase.name}`,
                    false,
                    `Error: ${error.message}`
                );
            }
        });

        return this.logTest('OHLCV Data Normalization', allPassed, 'All normalization tests passed');
    }

    // Test 3: Data parsing methods
    testDataParsingMethods() {
        console.log('\n=== Testing Data Parsing Methods ===');
        
        const mockTools = this.createMockMarkingTools();
        const testData = { test: 'data', value: 123 };
        const testDataJSON = JSON.stringify(testData);
        
        const testCases = [
            {
                method: 'parseOHLCVData',
                input: testDataJSON,
                expected: testData,
                name: 'JSON string parsing'
            },
            {
                method: 'parseOHLCVData',
                input: testData,
                expected: testData,
                name: 'Object passthrough'
            },
            {
                method: 'parseOHLCVData',
                input: null,
                expected: null,
                name: 'Null handling'
            },
            {
                method: 'parseOHLCVData',
                input: 'invalid json',
                expected: null,
                name: 'Invalid JSON handling'
            }
        ];

        let allPassed = true;
        testCases.forEach(testCase => {
            try {
                const result = mockTools[testCase.method](testCase.input);
                const passed = JSON.stringify(result) === JSON.stringify(testCase.expected);
                allPassed = allPassed && passed;
                
                this.logTest(
                    `${testCase.method}: ${testCase.name}`,
                    passed,
                    `Input: ${typeof testCase.input}, Output: ${typeof result}`
                );
            } catch (error) {
                allPassed = false;
                this.logTest(
                    `${testCase.method}: ${testCase.name}`,
                    false,
                    `Error: ${error.message}`
                );
            }
        });

        return this.logTest('Data Parsing Methods', allPassed, 'All parsing tests passed');
    }

    // Test 4: Entry data validation
    testEntryDataValidation() {
        console.log('\n=== Testing Entry Data Validation ===');
        
        const validationTests = [
            {
                name: 'Valid entry data',
                data: this.mockData.validEntryData,
                shouldPass: true
            },
            {
                name: 'Missing side',
                data: { ...this.mockData.validEntryData, side: null },
                shouldPass: false
            },
            {
                name: 'Invalid quantity',
                data: { ...this.mockData.validEntryData, quantity: -1 },
                shouldPass: false
            },
            {
                name: 'Invalid timestamp',
                data: { ...this.mockData.validEntryData, timestamp: 'invalid' },
                shouldPass: false
            }
        ];

        let allPassed = true;
        validationTests.forEach(test => {
            try {
                // Simulate validation logic
                const isValid = this.validateEntryData(test.data);
                const passed = test.shouldPass === isValid;
                allPassed = allPassed && passed;
                
                this.logTest(
                    `Entry Validation: ${test.name}`,
                    passed,
                    `Valid: ${isValid}, Expected: ${test.shouldPass}`
                );
            } catch (error) {
                allPassed = false;
                this.logTest(
                    `Entry Validation: ${test.name}`,
                    false,
                    `Error: ${error.message}`
                );
            }
        });

        return this.logTest('Entry Data Validation', allPassed, 'All validation tests passed');
    }

    // Helper method for entry data validation
    validateEntryData(data) {
        if (!data) return false;
        if (!data.side || (data.side !== 'buy' && data.side !== 'sell')) return false;
        if (!data.quantity || data.quantity <= 0) return false;
        if (!data.timestamp || isNaN(data.timestamp)) return false;
        if (!data.price || isNaN(data.price)) return false;
        return true;
    }

    // Test 5: Exit data extraction validation
    testExitDataExtraction() {
        console.log('\n=== Testing Exit Data Extraction ===');
        
        const mockTools = this.createMockMarkingTools();
        
        // Test comprehensive data structure
        const comprehensiveData = {
            symbol: 'BTCUSDT',
            timeframe: '15m',
            entry: {
                timestamp: new Date(1627849200 * 1000).toISOString(),
                entry_side: 'Buy',
                price: 45000.50,
                ohlcv: this.mockData.validOHLCVData,
                indicators: this.mockData.validIndicatorData
            },
            exit: {
                timestamp: new Date(1627935600 * 1000).toISOString(),
                unix_timestamp: 1627935600,
                price: 46500.75,
                ohlcv: this.mockData.validOHLCVData,
                indicators: this.mockData.validIndicatorData,
                data_quality: {
                    ohlcv_available: true,
                    indicators_available: true,
                    timestamp_valid: true,
                    price_valid: true
                }
            },
            pnl: {
                absolute: 150.025,
                percentage: 3.33,
                quantity: 0.1,
                entry_price: 45000.50,
                exit_price: 46500.75,
                side: 'buy'
            }
        };

        const tests = [
            {
                name: 'Comprehensive data structure',
                test: () => {
                    return comprehensiveData.entry && 
                           comprehensiveData.exit && 
                           comprehensiveData.pnl &&
                           comprehensiveData.exit.data_quality;
                }
            },
            {
                name: 'Data quality validation',
                test: () => {
                    const quality = comprehensiveData.exit.data_quality;
                    return quality.ohlcv_available && 
                           quality.indicators_available && 
                           quality.timestamp_valid && 
                           quality.price_valid;
                }
            },
            {
                name: 'PnL calculation accuracy',
                test: () => {
                    const pnl = comprehensiveData.pnl;
                    const expectedAbsolute = (pnl.exit_price - pnl.entry_price) * pnl.quantity;
                    return Math.abs(pnl.absolute - expectedAbsolute) < 0.01;
                }
            }
        ];

        let allPassed = true;
        tests.forEach(test => {
            try {
                const passed = test.test();
                allPassed = allPassed && passed;
                this.logTest(`Exit Data: ${test.name}`, passed, '');
            } catch (error) {
                allPassed = false;
                this.logTest(`Exit Data: ${test.name}`, false, `Error: ${error.message}`);
            }
        });

        return this.logTest('Exit Data Extraction', allPassed, 'All extraction tests passed');
    }

    // Run all tests
    async runAllTests() {
        console.log('🧪 Starting Marking Tools Test Suite...\n');
        
        const tests = [
            () => this.testToUpperCaseErrorPrevention(),
            () => this.testOHLCVDataNormalization(),
            () => this.testDataParsingMethods(),
            () => this.testEntryDataValidation(),
            () => this.testExitDataExtraction()
        ];

        let totalPassed = 0;
        let totalTests = tests.length;

        for (const test of tests) {
            if (await test()) {
                totalPassed++;
            }
        }

        console.log(`\n📊 Test Results: ${totalPassed}/${totalTests} test suites passed`);
        console.log(`📋 Individual test results: ${this.testResults.filter(r => r.passed).length}/${this.testResults.length} tests passed`);
        
        return {
            suitesTotal: totalTests,
            suitesPassed: totalPassed,
            testsTotal: this.testResults.length,
            testsPassed: this.testResults.filter(r => r.passed).length,
            results: this.testResults
        };
    }

    // Generate test report
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total: this.testResults.length,
                passed: this.testResults.filter(r => r.passed).length,
                failed: this.testResults.filter(r => r.passed === false).length
            },
            details: this.testResults
        };

        console.log('\n📄 Test Report Generated:', report);
        return report;
    }
}

// Export for use in browser
if (typeof window !== 'undefined') {
    window.MarkingToolsTests = MarkingToolsTests;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkingToolsTests;
}
