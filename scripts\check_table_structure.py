#!/usr/bin/env python3
"""
Check current table structure
"""
import mysql.connector
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def check_table_structure():
    """Check the structure of all tables and focus on volume data issue"""
    print("🔍 Checking table structures and volume data...")

    try:
        with get_db_cursor() as cursor:
            # Check manual_marks table structure
            print("\n� MANUAL_MARKS table structure:")
            cursor.execute("DESCRIBE manual_marks")
            marks_columns = cursor.fetchall()
            for col in marks_columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")

            # Check ohlcv_data table
            print("\n📊 OHLCV_DATA table structure:")
            cursor.execute("DESCRIBE ohlcv_data")
            ohlcv_columns = cursor.fetchall()
            for col in ohlcv_columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")

            # Check recent OHLCV data with volume
            print("\n📈 Recent OHLCV data with volume:")
            cursor.execute("SELECT timestamp, open, high, low, close, volume FROM ohlcv_data ORDER BY timestamp DESC LIMIT 5")
            recent_ohlcv = cursor.fetchall()
            for row in recent_ohlcv:
                print(f"  {row['timestamp']}: O:{row['open']} H:{row['high']} L:{row['low']} C:{row['close']} V:{row['volume']}")

            # Check recent marks with OHLCV data
            print("\n📋 Recent marks with OHLCV snapshots:")
            cursor.execute("""
                SELECT id, symbol, timeframe, mark_type, price,
                       ohlcv_snapshot,
                       JSON_EXTRACT(ohlcv_snapshot, '$.volume') as extracted_volume,
                       created_at
                FROM manual_marks
                ORDER BY id DESC
                LIMIT 5
            """)
            recent_marks = cursor.fetchall()

            for mark in recent_marks:
                print(f"\n  Mark ID {mark['id']} ({mark['mark_type']}):")
                print(f"    Price: ${mark['price']}")
                print(f"    Created: {mark['created_at']}")
                print(f"    Volume extracted: {mark['extracted_volume']}")

                if mark['ohlcv_snapshot']:
                    import json
                    try:
                        ohlcv_data = json.loads(mark['ohlcv_snapshot'])
                        print(f"    OHLCV snapshot: {ohlcv_data}")
                        if 'volume' in ohlcv_data:
                            print(f"    Volume in snapshot: {ohlcv_data['volume']} ({'✅ HAS VOLUME' if float(ohlcv_data['volume']) > 0 else '❌ ZERO VOLUME'})")
                        else:
                            print(f"    ❌ No volume field in OHLCV snapshot")
                    except json.JSONDecodeError:
                        print(f"    ❌ Invalid JSON in ohlcv_snapshot")
                else:
                    print(f"    ❌ No OHLCV snapshot")

            # Check volume statistics
            print("\n📊 Volume statistics:")
            cursor.execute("""
                SELECT
                    COUNT(*) as total_ohlcv,
                    COUNT(CASE WHEN volume > 0 THEN 1 END) as ohlcv_with_volume,
                    AVG(volume) as avg_volume
                FROM ohlcv_data
            """)
            ohlcv_stats = cursor.fetchone()
            print(f"  OHLCV records: {ohlcv_stats['total_ohlcv']} total, {ohlcv_stats['ohlcv_with_volume']} with volume > 0")
            print(f"  Average volume: {ohlcv_stats['avg_volume']:.2f}")

            cursor.execute("""
                SELECT
                    COUNT(*) as total_marks,
                    COUNT(CASE WHEN JSON_EXTRACT(ohlcv_snapshot, '$.volume') > 0 THEN 1 END) as marks_with_volume
                FROM manual_marks
                WHERE ohlcv_snapshot IS NOT NULL
            """)
            marks_stats = cursor.fetchone()
            print(f"  Mark records: {marks_stats['total_marks']} total, {marks_stats['marks_with_volume']} with volume > 0")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_table_structure()
