-- Enhanced Multi-Indicator System Database Schema Migration (Fixed)
-- Version: 002
-- Description: Add support for configurable multi-indicator system

-- 1. Create indicator_defaults table for managing default configurations
CREATE TABLE IF NOT EXISTS indicator_defaults (
    id INT AUTO_INCREMENT PRIMARY KEY,
    indicator_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_config JSON NOT NULL,
    chart_type ENUM('overlay', 'subchart') NOT NULL DEFAULT 'overlay',
    supports_multiple BOOLEAN DEFAULT FALSE,
    parameter_schema JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_chart_type (chart_type),
    INDEX idx_supports_multiple (supports_multiple)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create strategy_indicator_configs table for strategy-specific configurations
CREATE TABLE IF NOT EXISTS strategy_indicator_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    strategy_id INT NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    config JSON NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_strategy_indicator (strategy_id, indicator_name),
    INDEX idx_strategy_enabled (strategy_id, is_enabled),
    INDEX idx_display_order (display_order),
    FOREIGN KEY (strategy_id) REFERENCES strategies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Check if indicators_data table needs strategy_id column
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'strategy_builder' 
    AND TABLE_NAME = 'indicators_data' 
    AND COLUMN_NAME = 'strategy_id'
);

-- Add strategy_id column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE indicators_data ADD COLUMN strategy_id INT AFTER id', 
    'SELECT "strategy_id column already exists" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Check if indicators_data table needs indicator_config column
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'strategy_builder' 
    AND TABLE_NAME = 'indicators_data' 
    AND COLUMN_NAME = 'indicator_config'
);

-- Add indicator_config column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE indicators_data ADD COLUMN indicator_config JSON AFTER indicator_name', 
    'SELECT "indicator_config column already exists" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Check if indicators_data table needs indicator_values column
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'strategy_builder' 
    AND TABLE_NAME = 'indicators_data' 
    AND COLUMN_NAME = 'indicator_values'
);

-- Add indicator_values column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE indicators_data ADD COLUMN indicator_values JSON AFTER indicator_config', 
    'SELECT "indicator_values column already exists" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. Check if indicators_data table needs updated_at column
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'strategy_builder' 
    AND TABLE_NAME = 'indicators_data' 
    AND COLUMN_NAME = 'updated_at'
);

-- Add updated_at column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE indicators_data ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at', 
    'SELECT "updated_at column already exists" as status'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. Insert default indicator configurations
INSERT IGNORE INTO indicator_defaults (indicator_name, display_name, description, default_config, chart_type, supports_multiple, parameter_schema) VALUES
('EMA', 'Exponential Moving Average', 'Exponential Moving Average with customizable periods', 
 '{"periods": [20, 50, 100], "colors": ["#FF6B6B", "#4ECDC4", "#45B7D1"], "lineWidth": 2}', 
 'overlay', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 500}}, "colors": {"type": "array", "items": {"type": "string"}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('SMA', 'Simple Moving Average', 'Simple Moving Average with customizable periods',
 '{"periods": [20, 50, 100], "colors": ["#9C27B0", "#FF9800", "#795548"], "lineWidth": 2}',
 'overlay', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 1, "maximum": 500}}, "colors": {"type": "array", "items": {"type": "string"}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('RSI', 'Relative Strength Index', 'RSI oscillator with multiple timeframes',
 '{"periods": [14], "colors": ["#2196F3"], "overbought": 70, "oversold": 30, "lineWidth": 2}',
 'subchart', true,
 '{"type": "object", "properties": {"periods": {"type": "array", "items": {"type": "integer", "minimum": 2, "maximum": 100}}, "colors": {"type": "array", "items": {"type": "string"}}, "overbought": {"type": "number", "minimum": 50, "maximum": 100}, "oversold": {"type": "number", "minimum": 0, "maximum": 50}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('MACD', 'MACD Oscillator', 'Moving Average Convergence Divergence',
 '{"fast": 12, "slow": 26, "signal": 9, "colors": {"macd": "#2196F3", "signal": "#FF9800", "histogram": "#4CAF50"}, "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"fast": {"type": "integer", "minimum": 1, "maximum": 50}, "slow": {"type": "integer", "minimum": 1, "maximum": 100}, "signal": {"type": "integer", "minimum": 1, "maximum": 50}, "colors": {"type": "object", "properties": {"macd": {"type": "string"}, "signal": {"type": "string"}, "histogram": {"type": "string"}}}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('BOLLINGER_BANDS', 'Bollinger Bands', 'Bollinger Bands with customizable parameters',
 '{"period": 20, "stdDev": 2, "colors": {"upper": "#FF5722", "middle": "#607D8B", "lower": "#FF5722"}, "fillOpacity": 0.1, "lineWidth": 1}',
 'overlay', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 5, "maximum": 100}, "stdDev": {"type": "number", "minimum": 0.5, "maximum": 5}, "colors": {"type": "object", "properties": {"upper": {"type": "string"}, "middle": {"type": "string"}, "lower": {"type": "string"}}}, "fillOpacity": {"type": "number", "minimum": 0, "maximum": 1}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('STOCHASTIC', 'Stochastic Oscillator', 'Stochastic %K and %D oscillator',
 '{"kPeriod": 14, "dPeriod": 3, "colors": {"k": "#E91E63", "d": "#9C27B0"}, "overbought": 80, "oversold": 20, "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"kPeriod": {"type": "integer", "minimum": 1, "maximum": 50}, "dPeriod": {"type": "integer", "minimum": 1, "maximum": 20}, "colors": {"type": "object", "properties": {"k": {"type": "string"}, "d": {"type": "string"}}}, "overbought": {"type": "number", "minimum": 50, "maximum": 100}, "oversold": {"type": "number", "minimum": 0, "maximum": 50}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('VOLUME_SMA', 'Volume SMA', 'Simple Moving Average of Volume',
 '{"period": 20, "color": "#FFC107", "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 1, "maximum": 200}, "color": {"type": "string"}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}'),

('ATR', 'Average True Range', 'Average True Range volatility indicator',
 '{"period": 14, "color": "#795548", "lineWidth": 2}',
 'subchart', false,
 '{"type": "object", "properties": {"period": {"type": "integer", "minimum": 1, "maximum": 100}, "color": {"type": "string"}, "lineWidth": {"type": "integer", "minimum": 1, "maximum": 5}}}');

-- 8. Add comments for documentation
ALTER TABLE indicator_defaults COMMENT = 'Default configurations and metadata for technical indicators';
ALTER TABLE strategy_indicator_configs COMMENT = 'Strategy-specific indicator configurations and settings';

-- Migration completed successfully
SELECT 'Enhanced Multi-Indicator System Schema Migration Completed Successfully' as status;
