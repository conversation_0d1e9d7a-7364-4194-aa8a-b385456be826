"""
OHLCV Data Model
"""
from sqlalchemy import Column, String, Float, DateTime, Index
from sqlalchemy.sql import func
from app.core.database import Base

class OHLCVData(Base):
    """OHLCV candlestick data model"""
    __tablename__ = "ohlcv_data"
    
    symbol = Column(String(20), primary_key=True, nullable=False)
    timeframe = Column(String(10), primary_key=True, nullable=False)
    timestamp = Column(DateTime, primary_key=True, nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # Indexes for better query performance
    __table_args__ = (
        Index('idx_symbol_timeframe', 'symbol', 'timeframe'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_symbol_timeframe_timestamp', 'symbol', 'timeframe', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<OHLCVData(symbol='{self.symbol}', timeframe='{self.timeframe}', timestamp='{self.timestamp}', close={self.close})>"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }
