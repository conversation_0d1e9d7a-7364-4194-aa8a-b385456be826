"""
Intelligent Indicator Caching System
Provides high-performance caching for indicator calculations with smart invalidation
"""

import logging
import hashlib
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from threading import Lock
import asyncio
from collections import OrderedDict

import redis
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Any
    timestamp: float
    ttl: int
    access_count: int = 0
    last_access: float = 0
    size_bytes: int = 0

class IndicatorCache:
    """
    High-performance indicator cache with intelligent invalidation
    Supports both in-memory and Redis caching
    """
    
    def __init__(self, max_memory_size: int = 100 * 1024 * 1024):  # 100MB default
        self.max_memory_size = max_memory_size
        self.memory_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.current_memory_size = 0
        self.cache_lock = Lock()
        self.hit_count = 0
        self.miss_count = 0
        
        # Redis connection (optional)
        self.redis_client = None
        self._init_redis()
        
        # Cache configuration
        self.default_ttl = 3600  # 1 hour
        self.indicator_ttls = {
            'EMA': 1800,      # 30 minutes
            'SMA': 1800,      # 30 minutes  
            'RSI': 900,       # 15 minutes
            'MACD': 1200,     # 20 minutes
            'BOLLINGER_BANDS': 1800,  # 30 minutes
            'STOCHASTIC': 900  # 15 minutes
        }
    
    def _init_redis(self):
        """Initialize Redis connection if available"""
        try:
            if hasattr(settings, 'REDIS_URL') and settings.REDIS_URL:
                self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
                self.redis_client.ping()
                logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Redis not available, using memory cache only: {e}")
            self.redis_client = None
    
    def _generate_cache_key(self, symbol: str, timeframe: str, indicator_name: str, 
                          config: Dict[str, Any], start_time: Optional[datetime] = None,
                          end_time: Optional[datetime] = None) -> str:
        """Generate unique cache key for indicator data"""
        key_data = {
            'symbol': symbol.upper(),
            'timeframe': timeframe,
            'indicator': indicator_name.upper(),
            'config': config,
            'start': start_time.isoformat() if start_time else None,
            'end': end_time.isoformat() if end_time else None
        }
        
        key_string = json.dumps(key_data, sort_keys=True)
        return f"indicator:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def _calculate_size(self, data: Any) -> int:
        """Estimate memory size of data"""
        try:
            return len(json.dumps(data, default=str).encode('utf-8'))
        except:
            return 1024  # Default estimate
    
    def _evict_lru_memory(self, required_size: int):
        """Evict least recently used items from memory cache"""
        with self.cache_lock:
            while (self.current_memory_size + required_size > self.max_memory_size 
                   and self.memory_cache):
                # Remove oldest item
                key, entry = self.memory_cache.popitem(last=False)
                self.current_memory_size -= entry.size_bytes
                logger.debug(f"Evicted cache entry: {key}")
    
    async def get(self, symbol: str, timeframe: str, indicator_name: str, 
                  config: Dict[str, Any], start_time: Optional[datetime] = None,
                  end_time: Optional[datetime] = None) -> Optional[Dict[str, Any]]:
        """Get cached indicator data"""
        cache_key = self._generate_cache_key(
            symbol, timeframe, indicator_name, config, start_time, end_time
        )
        
        # Try memory cache first
        with self.cache_lock:
            if cache_key in self.memory_cache:
                entry = self.memory_cache[cache_key]
                
                # Check if expired
                if time.time() - entry.timestamp > entry.ttl:
                    del self.memory_cache[cache_key]
                    self.current_memory_size -= entry.size_bytes
                else:
                    # Update access info and move to end (most recent)
                    entry.access_count += 1
                    entry.last_access = time.time()
                    self.memory_cache.move_to_end(cache_key)
                    self.hit_count += 1
                    return entry.data
        
        # Try Redis cache
        if self.redis_client:
            try:
                cached_data = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.get, cache_key
                )
                if cached_data:
                    data = json.loads(cached_data)
                    self.hit_count += 1
                    
                    # Store in memory cache for faster access
                    await self._store_in_memory(cache_key, data, indicator_name)
                    return data
            except Exception as e:
                logger.warning(f"Redis cache read error: {e}")
        
        self.miss_count += 1
        return None
    
    async def set(self, symbol: str, timeframe: str, indicator_name: str,
                  config: Dict[str, Any], data: Dict[str, Any],
                  start_time: Optional[datetime] = None,
                  end_time: Optional[datetime] = None):
        """Store indicator data in cache"""
        cache_key = self._generate_cache_key(
            symbol, timeframe, indicator_name, config, start_time, end_time
        )
        
        ttl = self.indicator_ttls.get(indicator_name.upper(), self.default_ttl)
        
        # Store in memory cache
        await self._store_in_memory(cache_key, data, indicator_name, ttl)
        
        # Store in Redis cache
        if self.redis_client:
            try:
                await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: self.redis_client.setex(
                        cache_key, ttl, json.dumps(data, default=str)
                    )
                )
            except Exception as e:
                logger.warning(f"Redis cache write error: {e}")
    
    async def _store_in_memory(self, cache_key: str, data: Dict[str, Any], 
                              indicator_name: str, ttl: Optional[int] = None):
        """Store data in memory cache"""
        if ttl is None:
            ttl = self.indicator_ttls.get(indicator_name.upper(), self.default_ttl)
        
        size_bytes = self._calculate_size(data)
        
        # Evict if necessary
        self._evict_lru_memory(size_bytes)
        
        with self.cache_lock:
            entry = CacheEntry(
                data=data,
                timestamp=time.time(),
                ttl=ttl,
                size_bytes=size_bytes,
                last_access=time.time()
            )
            
            self.memory_cache[cache_key] = entry
            self.current_memory_size += size_bytes
    
    async def invalidate_symbol(self, symbol: str):
        """Invalidate all cache entries for a symbol"""
        pattern = f"indicator:*{symbol.upper()}*"
        
        # Invalidate memory cache
        with self.cache_lock:
            keys_to_remove = []
            for key in self.memory_cache:
                # This is a simplified check - in production you'd want better pattern matching
                if symbol.upper() in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                entry = self.memory_cache.pop(key, None)
                if entry:
                    self.current_memory_size -= entry.size_bytes
        
        # Invalidate Redis cache
        if self.redis_client:
            try:
                keys = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.keys, pattern
                )
                if keys:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.delete, *keys
                    )
            except Exception as e:
                logger.warning(f"Redis cache invalidation error: {e}")
    
    async def invalidate_timeframe(self, symbol: str, timeframe: str):
        """Invalidate cache entries for specific symbol and timeframe"""
        # Similar to invalidate_symbol but more specific
        await self.invalidate_symbol(symbol)  # Simplified for now
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
        
        with self.cache_lock:
            memory_entries = len(self.memory_cache)
            memory_usage_mb = self.current_memory_size / (1024 * 1024)
        
        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate_percent': round(hit_rate, 2),
            'memory_entries': memory_entries,
            'memory_usage_mb': round(memory_usage_mb, 2),
            'max_memory_mb': self.max_memory_size / (1024 * 1024),
            'redis_available': self.redis_client is not None
        }
    
    async def clear_cache(self):
        """Clear all cache entries"""
        with self.cache_lock:
            self.memory_cache.clear()
            self.current_memory_size = 0
        
        if self.redis_client:
            try:
                keys = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.keys, "indicator:*"
                )
                if keys:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.redis_client.delete, *keys
                    )
            except Exception as e:
                logger.warning(f"Redis cache clear error: {e}")
    
    async def warm_cache(self, symbols: List[str], timeframes: List[str], 
                        indicators: List[str]):
        """Pre-warm cache with commonly used indicators"""
        logger.info(f"Warming cache for {len(symbols)} symbols, "
                   f"{len(timeframes)} timeframes, {len(indicators)} indicators")
        
        # This would typically trigger background calculation tasks
        # Implementation depends on your specific calculation engine
        pass

# Global cache instance
indicator_cache = IndicatorCache()

class CacheManager:
    """High-level cache management interface"""
    
    @staticmethod
    async def get_cached_indicator(symbol: str, timeframe: str, indicator_name: str,
                                  config: Dict[str, Any], 
                                  start_time: Optional[datetime] = None,
                                  end_time: Optional[datetime] = None) -> Optional[Dict[str, Any]]:
        """Get cached indicator data with fallback to calculation"""
        return await indicator_cache.get(
            symbol, timeframe, indicator_name, config, start_time, end_time
        )
    
    @staticmethod
    async def cache_indicator_result(symbol: str, timeframe: str, indicator_name: str,
                                   config: Dict[str, Any], data: Dict[str, Any],
                                   start_time: Optional[datetime] = None,
                                   end_time: Optional[datetime] = None):
        """Cache calculated indicator data"""
        await indicator_cache.set(
            symbol, timeframe, indicator_name, config, data, start_time, end_time
        )
    
    @staticmethod
    async def invalidate_symbol_cache(symbol: str):
        """Invalidate all cached data for a symbol"""
        await indicator_cache.invalidate_symbol(symbol)
    
    @staticmethod
    def get_cache_statistics() -> Dict[str, Any]:
        """Get cache performance statistics"""
        return indicator_cache.get_cache_stats()
