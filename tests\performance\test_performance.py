"""
Performance tests for Strategy Builder
"""
import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
import statistics

import sys
from pathlib import Path
backend_dir = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.services.indicators import IndicatorsService
from app.core.rate_limiter import RateLimiter


class TestIndicatorsPerformance:
    """Test indicators calculation performance"""
    
    @pytest.fixture
    def large_dataset(self):
        """Generate large dataset for performance testing"""
        base_time = datetime.now()
        data = []
        
        for i in range(10000):  # 10k data points
            timestamp = base_time + timedelta(minutes=i)
            price = 50000 + (i * 0.1) + (i % 100) * 10  # Realistic price movement
            
            data.append({
                'timestamp': timestamp,
                'open': price - 5,
                'high': price + 10,
                'low': price - 10,
                'close': price,
                'volume': 1000 + (i % 500)
            })
        
        return data
    
    def test_rsi_calculation_performance(self, large_dataset):
        """Test RSI calculation performance"""
        start_time = time.time()
        
        df = IndicatorsService.prepare_dataframe(large_dataset)
        rsi = IndicatorsService.calculate_rsi(df, period=14)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 1 second for 10k data points
        assert execution_time < 1.0
        assert len(rsi) == len(large_dataset)
        
        print(f"RSI calculation for {len(large_dataset)} points: {execution_time:.3f}s")
    
    def test_macd_calculation_performance(self, large_dataset):
        """Test MACD calculation performance"""
        start_time = time.time()
        
        df = IndicatorsService.prepare_dataframe(large_dataset)
        macd = IndicatorsService.calculate_macd(df, fast=12, slow=26, signal=9)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 1 second for 10k data points
        assert execution_time < 1.0
        assert len(macd['macd']) == len(large_dataset)
        
        print(f"MACD calculation for {len(large_dataset)} points: {execution_time:.3f}s")
    
    def test_all_indicators_performance(self, large_dataset):
        """Test all indicators calculation performance"""
        config = {
            'rsi': {'period': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9},
            'ema': {'period': 20},
            'sma': {'period': 50},
            'bollinger_bands': {'period': 20, 'std': 2.0}
        }
        
        start_time = time.time()
        
        result = IndicatorsService.calculate_all_indicators(large_dataset, config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 2 seconds for all indicators on 10k data points
        assert execution_time < 2.0
        assert len(result['rsi']) == len(large_dataset)
        assert len(result['ema']) == len(large_dataset)
        
        print(f"All indicators calculation for {len(large_dataset)} points: {execution_time:.3f}s")
    
    def test_concurrent_indicators_calculation(self, large_dataset):
        """Test concurrent indicators calculation"""
        config = {
            'rsi': {'period': 14},
            'ema': {'period': 20}
        }
        
        def calculate_indicators():
            return IndicatorsService.calculate_all_indicators(large_dataset, config)
        
        start_time = time.time()
        
        # Run 5 concurrent calculations
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(calculate_indicators) for _ in range(5)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 3 seconds for 5 concurrent calculations
        assert execution_time < 3.0
        assert len(results) == 5
        
        print(f"5 concurrent indicators calculations: {execution_time:.3f}s")


class TestRateLimiterPerformance:
    """Test rate limiter performance"""
    
    @pytest.mark.asyncio
    async def test_rate_limiter_throughput(self):
        """Test rate limiter throughput"""
        limiter = RateLimiter(max_requests=100, time_window=60)
        
        start_time = time.time()
        
        # Try to acquire 100 permits
        acquired = 0
        for _ in range(100):
            if await limiter.acquire():
                acquired += 1
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert acquired == 100
        # Should complete very quickly (under 0.1 seconds)
        assert execution_time < 0.1
        
        print(f"Rate limiter 100 acquisitions: {execution_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_rate_limiter_blocking_performance(self):
        """Test rate limiter blocking performance"""
        limiter = RateLimiter(max_requests=2, time_window=1)  # Very restrictive
        
        # Acquire all permits
        await limiter.acquire()
        await limiter.acquire()
        
        # This should block
        start_time = time.time()
        await limiter.wait_if_needed()
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Should wait approximately 1 second
        assert 0.9 < execution_time < 1.5
        
        print(f"Rate limiter blocking time: {execution_time:.3f}s")


class TestDatabasePerformance:
    """Test database operation performance"""
    
    def test_bulk_insert_performance(self, test_db_session):
        """Test bulk insert performance"""
        from app.models.ohlcv import OHLCVData
        
        # Generate test data
        records = []
        base_time = datetime.now()
        
        for i in range(1000):
            record = OHLCVData(
                symbol="TESTPERF",
                timeframe="1m",
                timestamp=base_time + timedelta(minutes=i),
                open=50000 + i,
                high=50100 + i,
                low=49900 + i,
                close=50000 + i,
                volume=1000
            )
            records.append(record)
        
        start_time = time.time()
        
        # Bulk insert
        test_db_session.bulk_save_objects(records)
        test_db_session.commit()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete within 1 second for 1000 records
        assert execution_time < 1.0
        
        print(f"Bulk insert 1000 records: {execution_time:.3f}s")
    
    def test_query_performance(self, test_db_session, sample_ohlcv_records):
        """Test query performance"""
        from app.models.ohlcv import OHLCVData
        
        start_time = time.time()
        
        # Query with filters
        records = test_db_session.query(OHLCVData).filter(
            OHLCVData.symbol == "BTCUSDT",
            OHLCVData.timeframe == "1h"
        ).order_by(OHLCVData.timestamp.desc()).limit(100).all()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete very quickly (under 0.1 seconds)
        assert execution_time < 0.1
        assert len(records) > 0
        
        print(f"Query 100 records: {execution_time:.3f}s")


class TestAPIPerformance:
    """Test API endpoint performance"""
    
    def test_health_endpoint_performance(self, test_client):
        """Test health endpoint performance"""
        response_times = []
        
        # Make 10 requests
        for _ in range(10):
            start_time = time.time()
            response = test_client.get("/health")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        # Average response time should be under 0.1 seconds
        assert avg_response_time < 0.1
        # Max response time should be under 0.2 seconds
        assert max_response_time < 0.2
        
        print(f"Health endpoint - Avg: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")
    
    def test_concurrent_api_requests(self, test_client):
        """Test concurrent API requests performance"""
        def make_request():
            start_time = time.time()
            response = test_client.get("/health")
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        start_time = time.time()
        
        # Make 20 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # All requests should succeed
        assert all(status == 200 for status, _ in results)
        
        # Should complete within 2 seconds
        assert total_time < 2.0
        
        response_times = [time for _, time in results]
        avg_response_time = statistics.mean(response_times)
        
        print(f"20 concurrent requests - Total: {total_time:.3f}s, Avg: {avg_response_time:.3f}s")


class TestMemoryUsage:
    """Test memory usage"""
    
    def test_indicators_memory_usage(self, large_dataset):
        """Test memory usage during indicators calculation"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        config = {
            'rsi': {'period': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9},
            'ema': {'period': 20},
            'sma': {'period': 50}
        }
        
        # Calculate indicators multiple times
        for _ in range(10):
            result = IndicatorsService.calculate_all_indicators(large_dataset, config)
            del result  # Explicit cleanup
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        assert memory_increase < 100
        
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB, Increase: {memory_increase:.1f}MB")
