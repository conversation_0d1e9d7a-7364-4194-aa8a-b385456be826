"""
Unit tests for validators
"""
import pytest
from datetime import datetime, timedelta
from pydantic import ValidationError

import sys
from pathlib import Path
backend_dir = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.core.validators import (
    SymbolValidator, TimeframeValidator, ExchangeValidator,
    LimitValidator, DateTimeValidator, OHLCVFetchRequest,
    IndicatorsConfigValidator, TradeMarkRequest
)


class TestSymbolValidator:
    """Test symbol validation"""
    
    def test_valid_symbol(self):
        """Test valid symbol"""
        validator = SymbolValidator(symbol="BTCUSDT")
        assert validator.symbol == "BTCUSDT"
    
    def test_symbol_case_insensitive(self):
        """Test symbol is converted to uppercase"""
        validator = SymbolValidator(symbol="btcusdt")
        assert validator.symbol == "BTCUSDT"
    
    def test_symbol_with_whitespace(self):
        """Test symbol with whitespace is trimmed"""
        validator = SymbolValidator(symbol="  BTCUSDT  ")
        assert validator.symbol == "BTCUSDT"
    
    def test_empty_symbol(self):
        """Test empty symbol raises error"""
        with pytest.raises(ValidationError):
            SymbolValidator(symbol="")
    
    def test_whitespace_only_symbol(self):
        """Test whitespace-only symbol raises error"""
        with pytest.raises(ValidationError):
            SymbolValidator(symbol="   ")
    
    def test_invalid_characters(self):
        """Test symbol with invalid characters"""
        with pytest.raises(ValidationError):
            SymbolValidator(symbol="BTC-USDT")
        
        with pytest.raises(ValidationError):
            SymbolValidator(symbol="BTC/USDT")


class TestTimeframeValidator:
    """Test timeframe validation"""
    
    @pytest.mark.parametrize("timeframe", [
        "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", 
        "6h", "8h", "12h", "1d", "3d", "1w", "1M"
    ])
    def test_valid_timeframes(self, timeframe):
        """Test valid timeframes"""
        validator = TimeframeValidator(timeframe=timeframe)
        assert validator.timeframe == timeframe
    
    def test_invalid_timeframe(self):
        """Test invalid timeframe"""
        with pytest.raises(ValidationError):
            TimeframeValidator(timeframe="2m")
        
        with pytest.raises(ValidationError):
            TimeframeValidator(timeframe="invalid")


class TestExchangeValidator:
    """Test exchange validation"""
    
    @pytest.mark.parametrize("exchange", ["binance", "mexc", "BINANCE", "MEXC"])
    def test_valid_exchanges(self, exchange):
        """Test valid exchanges"""
        validator = ExchangeValidator(exchange=exchange)
        assert validator.exchange == exchange.lower()
    
    def test_invalid_exchange(self):
        """Test invalid exchange"""
        with pytest.raises(ValidationError):
            ExchangeValidator(exchange="invalid")


class TestLimitValidator:
    """Test limit validation"""
    
    @pytest.mark.parametrize("limit", [1, 100, 500, 1000, 2000])
    def test_valid_limits(self, limit):
        """Test valid limits"""
        validator = LimitValidator(limit=limit)
        assert validator.limit == limit
    
    @pytest.mark.parametrize("limit", [0, -1, 2001, 5000])
    def test_invalid_limits(self, limit):
        """Test invalid limits"""
        with pytest.raises(ValidationError):
            LimitValidator(limit=limit)


class TestDateTimeValidator:
    """Test datetime validation"""
    
    def test_valid_datetime_range(self):
        """Test valid datetime range"""
        start = datetime.now()
        end = start + timedelta(hours=1)
        
        validator = DateTimeValidator(start_time=start, end_time=end)
        assert validator.start_time == start
        assert validator.end_time == end
    
    def test_invalid_datetime_range(self):
        """Test invalid datetime range (end before start)"""
        start = datetime.now()
        end = start - timedelta(hours=1)
        
        with pytest.raises(ValidationError):
            DateTimeValidator(start_time=start, end_time=end)
    
    def test_none_values(self):
        """Test None values are allowed"""
        validator = DateTimeValidator(start_time=None, end_time=None)
        assert validator.start_time is None
        assert validator.end_time is None


class TestIndicatorsConfigValidator:
    """Test indicators configuration validation"""
    
    def test_valid_rsi_config(self):
        """Test valid RSI configuration"""
        config = IndicatorsConfigValidator(rsi={"period": 14})
        assert config.rsi == {"period": 14}
    
    def test_invalid_rsi_period(self):
        """Test invalid RSI period"""
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(rsi={"period": 1})
        
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(rsi={"period": 101})
    
    def test_valid_macd_config(self):
        """Test valid MACD configuration"""
        config = IndicatorsConfigValidator(macd={"fast": 12, "slow": 26, "signal": 9})
        assert config.macd == {"fast": 12, "slow": 26, "signal": 9}
    
    def test_invalid_macd_config(self):
        """Test invalid MACD configuration"""
        # Fast >= slow
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(macd={"fast": 26, "slow": 12, "signal": 9})
        
        # Invalid range
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(macd={"fast": 0, "slow": 26, "signal": 9})
    
    def test_valid_ema_config(self):
        """Test valid EMA configuration"""
        config = IndicatorsConfigValidator(ema={"period": 20})
        assert config.ema == {"period": 20}
    
    def test_invalid_ema_period(self):
        """Test invalid EMA period"""
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(ema={"period": 0})
        
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(ema={"period": 201})
    
    def test_valid_bollinger_bands_config(self):
        """Test valid Bollinger Bands configuration"""
        config = IndicatorsConfigValidator(bollinger_bands={"period": 20, "std": 2.0})
        assert config.bollinger_bands == {"period": 20, "std": 2.0}
    
    def test_invalid_bollinger_bands_config(self):
        """Test invalid Bollinger Bands configuration"""
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(bollinger_bands={"period": 0, "std": 2.0})
        
        with pytest.raises(ValidationError):
            IndicatorsConfigValidator(bollinger_bands={"period": 20, "std": 0.05})


class TestTradeMarkRequest:
    """Test trade mark request validation"""
    
    def test_valid_entry_mark(self):
        """Test valid entry mark"""
        request = TradeMarkRequest(
            symbol="BTCUSDT",
            timeframe="1h",
            timestamp=datetime.now(),
            mark_type="entry",
            price=50000.0,
            entry_side="buy"
        )
        assert request.mark_type == "entry"
        assert request.entry_side == "buy"
    
    def test_valid_exit_mark(self):
        """Test valid exit mark"""
        request = TradeMarkRequest(
            symbol="BTCUSDT",
            timeframe="1h",
            timestamp=datetime.now(),
            mark_type="exit",
            price=51000.0
        )
        assert request.mark_type == "exit"
        assert request.entry_side is None
    
    def test_entry_mark_without_side(self):
        """Test entry mark without entry side raises error"""
        with pytest.raises(ValidationError):
            TradeMarkRequest(
                symbol="BTCUSDT",
                timeframe="1h",
                timestamp=datetime.now(),
                mark_type="entry",
                price=50000.0
            )
    
    def test_invalid_mark_type(self):
        """Test invalid mark type"""
        with pytest.raises(ValidationError):
            TradeMarkRequest(
                symbol="BTCUSDT",
                timeframe="1h",
                timestamp=datetime.now(),
                mark_type="invalid",
                price=50000.0
            )
    
    def test_invalid_entry_side(self):
        """Test invalid entry side"""
        with pytest.raises(ValidationError):
            TradeMarkRequest(
                symbol="BTCUSDT",
                timeframe="1h",
                timestamp=datetime.now(),
                mark_type="entry",
                price=50000.0,
                entry_side="invalid"
            )
    
    def test_negative_price(self):
        """Test negative price raises error"""
        with pytest.raises(ValidationError):
            TradeMarkRequest(
                symbol="BTCUSDT",
                timeframe="1h",
                timestamp=datetime.now(),
                mark_type="entry",
                price=-1000.0,
                entry_side="buy"
            )
