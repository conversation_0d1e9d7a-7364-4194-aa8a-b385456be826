// Main Application Logic

class StrategyBuilderApp {
    constructor() {
        this.currentData = null;
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Data fetching buttons
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchDataFromExchange();
        });
        
        document.getElementById('loadData').addEventListener('click', () => {
            this.loadDataFromDatabase();
        });

        document.getElementById('testChart').addEventListener('click', () => {
            this.testChart();
        });

        // Test connection on page load
        this.testConnections();
    }
    
    async testConnections() {
        try {
            // Test database connection
            const response = await fetch('/health');
            if (response.ok) {
                console.log('Backend connection successful');
            }
            
            // Test exchange connections
            const exchanges = ['binance', 'mexc'];
            for (const exchange of exchanges) {
                try {
                    const testResponse = await fetch(`/api/v1/ohlcv/test-connection?exchange=${exchange}`);
                    const result = await testResponse.json();
                    console.log(`${exchange} connection:`, result.success ? 'OK' : 'Failed');
                } catch (error) {
                    console.log(`${exchange} connection: Failed`);
                }
            }
        } catch (error) {
            console.error('Connection test failed:', error);
        }
    }
    
    async fetchDataFromExchange() {
        const symbol = document.getElementById('symbol').value.trim();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;
        const limit = parseInt(document.getElementById('limit').value);
        
        if (!symbol) {
            this.showError('Please enter a trading symbol');
            return;
        }
        
        try {
            this.showLoading('Fetching data from exchange...');
            
            const params = new URLSearchParams({
                symbol: symbol,
                timeframe: timeframe,
                exchange: exchange,
                limit: limit.toString()
            });
            
            const response = await fetch(`/api/v1/ohlcv/fetch?${params}`, {
                method: 'GET'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(`Fetched ${result.data.total_fetched} candles, stored ${result.data.new_records} new records`);
                
                // Load the data to display
                await this.loadDataFromDatabase();
                
            } else {
                throw new Error('Failed to fetch data from exchange');
            }
            
        } catch (error) {
            console.error('Error fetching data:', error);
            this.showError(`Error fetching data: ${error.message}`);
        }
    }
    
    async loadDataFromDatabase() {
        const symbol = document.getElementById('symbol').value.trim();
        const timeframe = document.getElementById('timeframe').value;
        const limit = parseInt(document.getElementById('limit').value);
        
        if (!symbol) {
            this.showError('Please enter a trading symbol');
            return;
        }
        
        try {
            this.showLoading('Loading data from database...');
            
            const params = new URLSearchParams({
                symbol: symbol,
                timeframe: timeframe,
                limit: limit.toString()
            });
            
            const response = await fetch(`/api/v1/ohlcv/data?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.currentData = result.data.ohlcv;
                
                // Update chart (try professional chart first, then fallback to chart manager)
                if (window.professionalChart) {
                    // Use the same data loading logic as strategy manager
                    this.loadDataToProfessionalChart(this.currentData);
                } else if (window.chartManager) {
                    window.chartManager.updateData(this.currentData);
                }

                // Update indicators manager
                if (window.indicatorsManager) {
                    window.indicatorsManager.setCurrentData(this.currentData);
                }
                
                this.showSuccess(`Loaded ${result.data.count} candles`);
                
            } else {
                throw new Error('No data found in database');
            }
            
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError(`Error loading data: ${error.message}`);
        }
    }
    
    showLoading(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="loading"></span> ${message}`;
        }
        
        // Disable buttons during loading
        this.setButtonsEnabled(false);
    }
    
    showSuccess(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="success">${message}</span>`;
        }
        
        // Re-enable buttons
        this.setButtonsEnabled(true);
        
        setTimeout(() => {
            if (statusElement.textContent.includes(message)) {
                statusElement.textContent = 'Ready';
            }
        }, 3000);
    }
    
    showError(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="error">${message}</span>`;
        }
        
        // Re-enable buttons
        this.setButtonsEnabled(true);
        
        setTimeout(() => {
            if (statusElement.textContent.includes(message)) {
                statusElement.textContent = 'Ready';
            }
        }, 5000);
    }
    
    setButtonsEnabled(enabled) {
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.disabled = !enabled;
        });
    }
    
    getCurrentData() {
        return this.currentData;
    }

    testChart() {
        console.log('Testing multi-panel chart with sample data...');

        // Generate sample OHLCV data
        const sampleData = [];
        const startTime = Math.floor(Date.now() / 1000) - (100 * 60); // 100 minutes ago

        for (let i = 0; i < 100; i++) {
            const time = startTime + (i * 60); // 1 minute intervals
            const open = 50000 + Math.random() * 10000;
            const close = open + (Math.random() - 0.5) * 2000;
            const high = Math.max(open, close) + Math.random() * 1000;
            const low = Math.min(open, close) - Math.random() * 1000;
            const volume = Math.random() * 1000000;

            sampleData.push({
                symbol: 'BTCUSDT',
                timeframe: '1m',
                timestamp: time,
                open: open.toFixed(2),
                high: high.toFixed(2),
                low: low.toFixed(2),
                close: close.toFixed(2),
                volume: volume.toFixed(0)
            });
        }

        // Generate sample indicators
        const sampleIndicators = this.generateSampleIndicators(sampleData);

        console.log('Generated sample data:', sampleData.length, 'candles');

        // Update multi-panel chart with sample data
        if (window.multiPanelChartManager) {
            window.multiPanelChartManager.loadData(sampleData, sampleIndicators);
            this.updateStatus('Sample multi-panel chart data loaded successfully');
        } else {
            console.error('Multi-panel chart manager not available');
            this.updateStatus('Error: Multi-panel chart manager not initialized', 'error');
        }
    }

    generateSampleIndicators(ohlcvData) {
        const closes = ohlcvData.map(d => parseFloat(d.close));

        // Simple RSI calculation (simplified for demo)
        const rsi = closes.map((_, index) => {
            if (index < 14) return null;
            return 30 + Math.random() * 40; // Random RSI between 30-70
        });

        // Simple MACD calculation (simplified for demo)
        const macd = closes.map((_, index) => {
            if (index < 26) return null;
            return (Math.random() - 0.5) * 2; // Random MACD
        });

        const signal = macd.map(value => {
            if (value === null) return null;
            return value + (Math.random() - 0.5) * 0.5; // Signal line
        });

        const histogram = macd.map((value, index) => {
            if (value === null || signal[index] === null) return null;
            return value - signal[index]; // Histogram
        });

        return {
            rsi: rsi.filter(v => v !== null),
            macd: {
                macd: macd.filter(v => v !== null),
                signal: signal.filter(v => v !== null),
                histogram: histogram.filter(v => v !== null)
            }
        };
    }

    loadDataToProfessionalChart(ohlcvData) {
        if (!window.professionalChart || !ohlcvData || ohlcvData.length === 0) {
            console.warn('Professional chart not available or no data provided');
            return;
        }

        try {
            // Convert data to chart format (include volume for OHLCV extraction)
            const candleData = ohlcvData.map(item => ({
                time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
                volume: parseFloat(item.volume) // Include volume for marking tools
            })).sort((a, b) => a.time - b.time);

            const volumeColors = { up: '#26a69a', down: '#ef5350' };
            const volumeData = ohlcvData.map(item => ({
                time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                value: parseFloat(item.volume),
                color: parseFloat(item.close) >= parseFloat(item.open) ? volumeColors.up : volumeColors.down,
            })).sort((a, b) => a.time - b.time);

            // Update chart
            window.professionalChart.candlestickSeries.setData(candleData);
            window.professionalChart.volumeSeries.setData(volumeData);

            // Store candleData with volume for marking tools access
            window.professionalChart.currentData = candleData;

            // Apply chart configuration first
            if (window.professionalChart.applyChartConfiguration) {
                window.professionalChart.applyChartConfiguration();
            }

            // Position chart to show earliest data instead of latest (with delay)
            setTimeout(() => {
                if (window.professionalChart.positionToEarliestData) {
                    window.professionalChart.positionToEarliestData(candleData);
                } else {
                    // Fallback to fitContent if positioning method not available
                    window.professionalChart.chart.timeScale().fitContent();
                }
            }, 100);

            console.log('App: Loaded data to professional chart:', candleData.length, 'candles');
        } catch (error) {
            console.error('Error loading data to professional chart:', error);
        }
    }
}

// Global app instance
let app = null;

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    app = new StrategyBuilderApp();

    // Initialize legacy chart manager for backward compatibility
    if (document.getElementById('chart')) {
        window.chartManager = new ChartManager('chart');
        console.log('Legacy chart manager initialized:', window.chartManager);
    }

    // Multi-panel chart manager is initialized in its own file
    // window.multiPanelChartManager will be available after initialization

    // Initialize indicators manager
    window.indicatorsManager = new IndicatorsManager();
    console.log('Indicators manager initialized:', window.indicatorsManager);

    // Initialize trade manager
    window.tradeManager = new TradeManager();
    console.log('Trade manager initialized:', window.tradeManager);
    
    // Add some helpful keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        // Ctrl+F to fetch data
        if (event.ctrlKey && event.key === 'f') {
            event.preventDefault();
            document.getElementById('fetchData').click();
        }
        
        // Ctrl+L to load data
        if (event.ctrlKey && event.key === 'l') {
            event.preventDefault();
            document.getElementById('loadData').click();
        }
        
        // Ctrl+I to calculate indicators
        if (event.ctrlKey && event.key === 'i') {
            event.preventDefault();
            document.getElementById('calculateIndicators').click();
        }
        
        // Escape to cancel marking mode
        if (event.key === 'Escape' && window.tradeManager) {
            window.tradeManager.stopMarking();
        }
    });
    
    console.log('Strategy Builder initialized successfully!');
    console.log('Keyboard shortcuts:');
    console.log('  Ctrl+F: Fetch data from exchange');
    console.log('  Ctrl+L: Load data from database');
    console.log('  Ctrl+I: Calculate indicators');
    console.log('  Escape: Cancel marking mode');
});
