"""
Configuration settings for Strategy Builder
"""
from pydantic_settings import BaseSettings
from pydantic import Field, field_validator, SecretStr
from typing import Optional, List
from pathlib import Path
import logging
from urllib.parse import quote_plus

# Load environment variables from .env file
env_file = Path(__file__).parent.parent.parent.parent / ".env"

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings loaded from environment variables with validation"""

    # Application Settings
    DEBUG: bool = Field(default=True, description="Enable debug mode")
    HOST: str = Field(default="127.0.0.1", description="Server host")
    PORT: int = Field(default=8000, ge=1, le=65535, description="Server port")
    ENVIRONMENT: str = Field(default="development", description="Environment (development/production)")

    # Security Settings
    SECRET_KEY: str = Field(default="dev-secret-key-change-in-production", min_length=32)
    ALLOWED_HOSTS: List[str] = Field(default=["localhost", "127.0.0.1"], description="Allowed CORS origins")
    API_RATE_LIMIT: int = Field(default=100, ge=1, description="API rate limit per minute")

    # Database Configuration
    DB_HOST: str = Field(default="localhost", description="Database host")
    DB_PORT: int = Field(default=3306, ge=1, le=65535, description="Database port")
    DB_USERNAME: str = Field(..., description="Database username")
    DB_PASSWORD: SecretStr = Field(..., description="Database password")
    DB_DATABASE: str = Field(default="strategy_builder", description="Database name")
    DB_POOL_SIZE: int = Field(default=10, ge=1, le=50, description="Database connection pool size")
    DB_MAX_OVERFLOW: int = Field(default=20, ge=0, le=100, description="Database max overflow connections")

    # Binance API Configuration
    BINANCE_API_KEY: SecretStr = Field(default="", description="Binance API key")
    BINANCE_API_SECRET: SecretStr = Field(default="", description="Binance API secret")
    BINANCE_TESTNET: bool = Field(default=False, description="Use Binance testnet")
    BINANCE_BASE_URL: str = Field(default="https://fapi.binance.com", description="Binance base URL")
    BINANCE_RATE_LIMIT: int = Field(default=1200, ge=1, description="Binance rate limit per minute")

    # Binance Testnet (optional)
    BINANCE_TESTNET_API_KEY: Optional[SecretStr] = Field(default=None, description="Binance testnet API key")
    BINANCE_TESTNET_SECRET_KEY: Optional[SecretStr] = Field(default=None, description="Binance testnet secret")

    # MEXC API Configuration
    MEXC_API_KEY: SecretStr = Field(default="", description="MEXC API key")
    MEXC_API_SECRET: SecretStr = Field(default="", description="MEXC API secret")
    MEXC_BASE_URL: str = Field(default="https://contract.mexc.com", description="MEXC base URL")
    MEXC_RATE_LIMIT: int = Field(default=600, ge=1, description="MEXC rate limit per minute")
    
    @field_validator('ENVIRONMENT')
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting"""
        allowed = ['development', 'production', 'testing']
        if v.lower() not in allowed:
            raise ValueError(f'Environment must be one of: {allowed}')
        return v.lower()

    @field_validator('ALLOWED_HOSTS')
    @classmethod
    def validate_hosts(cls, v: List[str]) -> List[str]:
        """Validate allowed hosts"""
        if not v:
            raise ValueError('At least one allowed host must be specified')
        return v

    @property
    def database_url(self) -> str:
        """Generate database URL for SQLAlchemy with proper encoding"""
        password = self.DB_PASSWORD.get_secret_value() if hasattr(self.DB_PASSWORD, 'get_secret_value') else str(self.DB_PASSWORD)
        encoded_password = quote_plus(password)
        return f"mysql+pymysql://{self.DB_USERNAME}:{encoded_password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}?charset=utf8mb4"

    @property
    def binance_config(self) -> dict:
        """Get Binance configuration with secure handling"""
        if self.BINANCE_TESTNET and self.BINANCE_TESTNET_API_KEY:
            return {
                "api_key": self.BINANCE_TESTNET_API_KEY.get_secret_value(),
                "api_secret": self.BINANCE_TESTNET_SECRET_KEY.get_secret_value() if self.BINANCE_TESTNET_SECRET_KEY else "",
                "base_url": "https://testnet.binancefuture.com",
                "testnet": True,
                "rate_limit": self.BINANCE_RATE_LIMIT
            }
        return {
            "api_key": self.BINANCE_API_KEY.get_secret_value(),
            "api_secret": self.BINANCE_API_SECRET.get_secret_value(),
            "base_url": self.BINANCE_BASE_URL,
            "testnet": False,
            "rate_limit": self.BINANCE_RATE_LIMIT
        }

    @property
    def mexc_config(self) -> dict:
        """Get MEXC configuration with secure handling"""
        return {
            "api_key": self.MEXC_API_KEY.get_secret_value(),
            "api_secret": self.MEXC_API_SECRET.get_secret_value(),
            "base_url": self.MEXC_BASE_URL,
            "rate_limit": self.MEXC_RATE_LIMIT
        }

    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.ENVIRONMENT == "production"

    @property
    def cors_origins(self) -> List[str]:
        """Get CORS origins based on environment"""
        if self.is_production:
            return self.ALLOWED_HOSTS
        return ["*"]  # Allow all in development

    class Config:
        env_file = str(env_file) if env_file.exists() else None
        case_sensitive = True
        extra = "forbid"  # Prevent extra fields

# Global settings instance
settings = Settings()
