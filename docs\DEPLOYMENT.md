# Strategy Builder - Deployment Guide

This guide covers different deployment options for the Strategy Builder application.

## 🚀 Quick Start (Development)

### Option 1: Using the Quick Start Script

```bash
python start.py
```

### Option 2: Manual Setup

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Setup database
python scripts/setup_database.py

# 3. Validate configuration
python scripts/validate_config.py

# 4. Start server
python scripts/run_server.py
```

## 🔧 Configuration

### Environment Variables (.env)

```bash
# Application Settings
DEBUG=true
HOST=127.0.0.1
PORT=8000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=strategy_builder

# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_secret
BINANCE_TESTNET=false
BINANCE_BASE_URL=https://fapi.binance.com

# MEXC API Configuration
MEXC_API_KEY=your_mexc_api_key
MEXC_API_SECRET=your_mexc_secret
MEXC_BASE_URL=https://contract.mexc.com
```

## 🐳 Docker Deployment (Future)

### Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for PyMySQL
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libssl-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ ./backend/
COPY frontend/ ./frontend/
COPY scripts/ ./scripts/
COPY .env .

# Expose port
EXPOSE 8000

# Start application
CMD ["python", "scripts/run_server.py"]
```

### Docker Compose

```yaml
version: "3.8"

services:
  strategy-builder:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=strategy_user
      - DB_PASSWORD=strategy_password
      - DB_DATABASE=strategy_builder
    depends_on:
      - mysql
    volumes:
      - ./.env:/app/.env

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: strategy_builder
      MYSQL_USER: strategy_user
      MYSQL_PASSWORD: strategy_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## ☁️ Cloud Deployment

### AWS EC2 Deployment

1. **Launch EC2 Instance**

   - Ubuntu 20.04 LTS
   - t3.medium or larger
   - Security group: Allow ports 22, 80, 443, 8000

2. **Setup Environment**

   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Python and MySQL
   sudo apt install python3 python3-pip mysql-server -y

   # Clone repository
   git clone <your-repo-url>
   cd Strategy_builder

   # Setup MySQL
   sudo mysql_secure_installation

   # Create database and user
   sudo mysql -e "CREATE DATABASE strategy_builder;"
   sudo mysql -e "CREATE USER 'strategy_user'@'localhost' IDENTIFIED BY 'your_password';"
   sudo mysql -e "GRANT ALL PRIVILEGES ON strategy_builder.* TO 'strategy_user'@'localhost';"

   # Install dependencies
   pip3 install -r requirements.txt

   # Configure environment
   cp .env.example .env
   # Edit .env with your settings

   # Setup database
   python3 scripts/setup_database.py

   # Start application
   python3 scripts/run_server.py
   ```

3. **Setup Nginx (Optional)**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

### Heroku Deployment

1. **Prepare for Heroku**

   ```bash
   # Create Procfile
   echo "web: python scripts/run_server.py" > Procfile

   # Create runtime.txt
   echo "python-3.11.0" > runtime.txt
   ```

2. **Deploy to Heroku**

   ```bash
   # Install Heroku CLI and login
   heroku login

   # Create app
   heroku create your-strategy-builder

   # Add MySQL addon
   heroku addons:create jawsdb:kitefin

   # Set environment variables
   heroku config:set DEBUG=false
   heroku config:set HOST=0.0.0.0
   heroku config:set PORT=8000

   # Deploy
   git add .
   git commit -m "Deploy to Heroku"
   git push heroku main

   # Setup database
   heroku run python scripts/setup_database.py
   ```

## 🔒 Security Considerations

### Production Security Checklist

- [ ] Change default database passwords
- [ ] Use environment variables for all secrets
- [ ] Enable HTTPS/SSL
- [ ] Configure firewall rules
- [ ] Regular security updates
- [ ] Monitor API usage and rate limits
- [ ] Backup database regularly
- [ ] Use strong API keys with minimal permissions

### API Security

```python
# Add to main.py for production
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware

# Force HTTPS in production
if not settings.DEBUG:
    app.add_middleware(HTTPSRedirectMiddleware)
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=["yourdomain.com"])
```

## 📊 Monitoring & Logging

### Application Monitoring

```python
# Add to main.py
import logging
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("strategy_builder.log"),
        logging.StreamHandler()
    ]
)
```

### Database Monitoring

```sql
-- Monitor database performance
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';

-- Monitor table sizes
SELECT
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'strategy_builder';
```

## 🔄 Backup & Recovery

### Database Backup

```bash
# Create backup
mysqldump -u strategy_user -p strategy_builder > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
mysql -u strategy_user -p strategy_builder < backup_file.sql
```

### Automated Backup Script

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/strategy_builder_$DATE.sql"

# Create backup
mysqldump -u strategy_user -p strategy_builder > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "strategy_builder_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**

   ```bash
   # Check MySQL status
   sudo systemctl status mysql

   # Check connection
   mysql -u strategy_user -p -h localhost

   # Check firewall
   sudo ufw status
   ```

2. **Port Already in Use**

   ```bash
   # Find process using port 8000
   lsof -i :8000

   # Kill process
   kill -9 <PID>
   ```

3. **Permission Denied**

   ```bash
   # Fix file permissions
   chmod +x scripts/*.py

   # Fix directory permissions
   chmod 755 backend/ frontend/
   ```

4. **API Rate Limits**
   - Check exchange API documentation
   - Implement request throttling
   - Use testnet for development

### Log Analysis

```bash
# View application logs
tail -f strategy_builder.log

# View system logs
sudo journalctl -u your-service-name -f

# Check disk space
df -h

# Check memory usage
free -h
```

## 📈 Performance Optimization

### Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_ohlcv_symbol_timeframe_timestamp ON ohlcv_data(symbol, timeframe, timestamp);
CREATE INDEX idx_indicators_symbol_timeframe ON indicators_data(symbol, timeframe);
CREATE INDEX idx_marks_symbol_timeframe ON manual_marks(symbol, timeframe);

-- Optimize tables
OPTIMIZE TABLE ohlcv_data;
OPTIMIZE TABLE indicators_data;
OPTIMIZE TABLE manual_marks;
OPTIMIZE TABLE strategy_log;
```

### Application Optimization

- Use connection pooling
- Implement caching for frequently accessed data
- Optimize database queries
- Use async operations where possible
- Monitor memory usage and optimize data structures
