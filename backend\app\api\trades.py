"""
Trade Management API Endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.core.data_access import TradeDataAccess, OHLCVDataAccess
from app.services.indicators import IndicatorsService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/mark")
async def create_trade_mark(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    timestamp: str = Query(..., description="Mark timestamp (ISO format)"),
    mark_type: str = Query(..., description="Mark type (entry or exit)"),
    entry_side: Optional[str] = Query(None, description="Entry side (buy or sell) - required for entry marks"),
    price: float = Query(..., description="Price at mark"),
    indicators_config: Optional[Dict[str, Dict]] = Body(None, description="Indicators configuration for snapshot"),
    linked_trade_id: Optional[int] = Query(None, description="Linked trade ID for exit marks"),
    db: Session = Depends(get_db)
):
    """Create a trade mark (entry or exit)"""
    try:
        # Validate mark type
        if mark_type.lower() not in ['entry', 'exit']:
            raise HTTPException(status_code=400, detail="Mark type must be 'entry' or 'exit'")
        
        mark_type_enum = MarkType.ENTRY if mark_type.lower() == 'entry' else MarkType.EXIT
        
        # Validate entry side for entry marks
        entry_side_enum = None
        if mark_type_enum == MarkType.ENTRY:
            if not entry_side or entry_side.lower() not in ['buy', 'sell']:
                raise HTTPException(status_code=400, detail="Entry side must be 'buy' or 'sell' for entry marks")
            entry_side_enum = EntrySide.BUY if entry_side.lower() == 'buy' else EntrySide.SELL
        
        # Parse timestamp
        mark_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        # Get OHLCV snapshot at the timestamp
        ohlcv_query = db.query(OHLCVData).filter(
            OHLCVData.symbol == symbol.upper(),
            OHLCVData.timeframe == timeframe,
            OHLCVData.timestamp == mark_timestamp
        ).first()
        
        ohlcv_snapshot = ohlcv_query.to_dict() if ohlcv_query else None
        
        # Get indicators snapshot if configuration provided
        indicator_snapshot = None
        if indicators_config:
            # Get OHLCV data up to the mark timestamp for indicator calculation
            ohlcv_data_query = db.query(OHLCVData).filter(
                OHLCVData.symbol == symbol.upper(),
                OHLCVData.timeframe == timeframe,
                OHLCVData.timestamp <= mark_timestamp
            ).order_by(OHLCVData.timestamp.asc()).limit(1000)
            
            ohlcv_records = ohlcv_data_query.all()
            if ohlcv_records:
                ohlcv_data = [record.to_dict() for record in ohlcv_records]
                indicator_snapshot = IndicatorsService.get_indicator_value_at_timestamp(
                    ohlcv_data, timestamp, indicators_config
                )
        
        # Create the mark
        trade_mark = ManualMarks(
            symbol=symbol.upper(),
            timeframe=timeframe,
            mark_type=mark_type_enum,
            entry_side=entry_side_enum,
            timestamp=mark_timestamp,
            price=price,
            indicator_snapshot=indicator_snapshot,
            ohlcv_snapshot=ohlcv_snapshot,
            linked_trade_id=linked_trade_id
        )
        
        db.add(trade_mark)
        db.commit()
        db.refresh(trade_mark)
        
        return {
            "success": True,
            "data": {
                "mark_id": trade_mark.id,
                "mark": trade_mark.to_dict()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating trade mark: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/complete-trade")
async def complete_trade(
    entry_id: int = Query(..., description="Entry mark ID"),
    exit_id: int = Query(..., description="Exit mark ID"),
    db: Session = Depends(get_db)
):
    """Complete a trade by linking entry and exit marks and calculating profit"""
    try:
        # Get entry and exit marks
        entry_mark = db.query(ManualMarks).filter(ManualMarks.id == entry_id).first()
        exit_mark = db.query(ManualMarks).filter(ManualMarks.id == exit_id).first()
        
        if not entry_mark or not exit_mark:
            raise HTTPException(status_code=404, detail="Entry or exit mark not found")
        
        if entry_mark.mark_type != MarkType.ENTRY:
            raise HTTPException(status_code=400, detail="First mark must be an entry")
        
        if exit_mark.mark_type != MarkType.EXIT:
            raise HTTPException(status_code=400, detail="Second mark must be an exit")
        
        if entry_mark.symbol != exit_mark.symbol or entry_mark.timeframe != exit_mark.timeframe:
            raise HTTPException(status_code=400, detail="Entry and exit marks must have same symbol and timeframe")
        
        # Calculate profit percentage
        entry_price = entry_mark.price
        exit_price = exit_mark.price
        entry_side = entry_mark.entry_side
        
        if entry_side == EntrySide.BUY:
            profit_pct = ((exit_price - entry_price) / entry_price) * 100
        else:  # SELL
            profit_pct = ((entry_price - exit_price) / entry_price) * 100
        
        # Update linked_trade_id for both marks
        trade_id = entry_mark.id  # Use entry mark ID as trade ID
        entry_mark.linked_trade_id = trade_id
        exit_mark.linked_trade_id = trade_id
        
        # Create strategy log entry
        strategy_log = StrategyLog(
            symbol=entry_mark.symbol,
            timeframe=entry_mark.timeframe,
            entry_id=entry_id,
            exit_id=exit_id,
            entry_side=entry_side,
            profit_pct=profit_pct,
            entry_ohlcv=entry_mark.ohlcv_snapshot,
            exit_ohlcv=exit_mark.ohlcv_snapshot,
            entry_indicator_snapshot=entry_mark.indicator_snapshot,
            exit_indicator_snapshot=exit_mark.indicator_snapshot
        )
        
        db.add(strategy_log)
        db.commit()
        db.refresh(strategy_log)
        
        return {
            "success": True,
            "data": {
                "trade_id": trade_id,
                "strategy_log_id": strategy_log.strategy_id,
                "profit_pct": profit_pct,
                "entry_price": entry_price,
                "exit_price": exit_price,
                "entry_side": entry_side.value,
                "trade": strategy_log.to_dict()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing trade: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/marks")
async def get_trade_marks(
    symbol: Optional[str] = Query(None, description="Trading pair symbol"),
    timeframe: Optional[str] = Query(None, description="Timeframe"),
    mark_type: Optional[str] = Query(None, description="Mark type (entry or exit)"),
    limit: int = Query(100, description="Number of marks to return", ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get trade marks with optional filtering"""
    try:
        query = db.query(ManualMarks)
        
        if symbol:
            query = query.filter(ManualMarks.symbol == symbol.upper())
        
        if timeframe:
            query = query.filter(ManualMarks.timeframe == timeframe)
        
        if mark_type:
            if mark_type.lower() not in ['entry', 'exit']:
                raise HTTPException(status_code=400, detail="Mark type must be 'entry' or 'exit'")
            mark_type_enum = MarkType.ENTRY if mark_type.lower() == 'entry' else MarkType.EXIT
            query = query.filter(ManualMarks.mark_type == mark_type_enum)
        
        marks = query.order_by(ManualMarks.timestamp.desc()).limit(limit).all()
        
        return {
            "success": True,
            "data": {
                "count": len(marks),
                "marks": [mark.to_dict() for mark in marks]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trade marks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/strategy-log")
async def get_strategy_log(
    symbol: Optional[str] = Query(None, description="Trading pair symbol"),
    timeframe: Optional[str] = Query(None, description="Timeframe"),
    limit: int = Query(100, description="Number of trades to return", ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get completed trades from strategy log"""
    try:
        query = db.query(StrategyLog)
        
        if symbol:
            query = query.filter(StrategyLog.symbol == symbol.upper())
        
        if timeframe:
            query = query.filter(StrategyLog.timeframe == timeframe)
        
        trades = query.order_by(StrategyLog.created_at.desc()).limit(limit).all()
        
        # Calculate summary statistics
        if trades:
            profits = [trade.profit_pct for trade in trades]
            winning_trades = [p for p in profits if p > 0]
            losing_trades = [p for p in profits if p < 0]
            
            summary = {
                "total_trades": len(trades),
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": (len(winning_trades) / len(trades)) * 100 if trades else 0,
                "total_profit": sum(profits),
                "avg_profit": sum(profits) / len(profits) if profits else 0,
                "max_profit": max(profits) if profits else 0,
                "max_loss": min(profits) if profits else 0
            }
        else:
            summary = {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0,
                "total_profit": 0,
                "avg_profit": 0,
                "max_profit": 0,
                "max_loss": 0
            }
        
        return {
            "success": True,
            "data": {
                "summary": summary,
                "trades": [trade.to_dict() for trade in trades]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy log: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/mark/{mark_id}")
async def delete_trade_mark(
    mark_id: int,
    db: Session = Depends(get_db)
):
    """Delete a trade mark"""
    try:
        mark = db.query(ManualMarks).filter(ManualMarks.id == mark_id).first()
        
        if not mark:
            raise HTTPException(status_code=404, detail="Trade mark not found")
        
        # Check if mark is part of a completed trade
        if mark.linked_trade_id:
            # Also delete the strategy log entry
            strategy_log = db.query(StrategyLog).filter(
                (StrategyLog.entry_id == mark_id) | (StrategyLog.exit_id == mark_id)
            ).first()
            
            if strategy_log:
                db.delete(strategy_log)
        
        db.delete(mark)
        db.commit()
        
        return {
            "success": True,
            "message": f"Trade mark {mark_id} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting trade mark: {e}")
        raise HTTPException(status_code=500, detail=str(e))
