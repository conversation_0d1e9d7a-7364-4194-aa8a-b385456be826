"""
Enhanced unit tests for technical indicators
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.services.indicators import IndicatorsService
from app.core.exceptions import IndicatorCalculationError, InsufficientDataError

class TestIndicatorsService:
    """Test cases for IndicatorsService"""
    
    @pytest.fixture
    def sample_ohlcv_data(self):
        """Generate sample OHLCV data for testing"""
        base_time = datetime.now()
        data = []
        
        for i in range(100):
            timestamp = base_time + timedelta(hours=i)
            price = 50000 + (i * 10) + (i % 10) * 100  # Trending price with volatility
            
            data.append({
                'timestamp': timestamp,
                'open': price - 50,
                'high': price + 100,
                'low': price - 100,
                'close': price,
                'volume': 1000 + (i * 10)
            })
        
        return data
    
    def test_prepare_dataframe(self, sample_ohlcv_data):
        """Test DataFrame preparation"""
        df = IndicatorsService.prepare_dataframe(sample_ohlcv_data)
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 100
        assert 'open' in df.columns
        assert 'high' in df.columns
        assert 'low' in df.columns
        assert 'close' in df.columns
        assert 'volume' in df.columns
        assert df.index.name == 'timestamp'
    
    def test_calculate_rsi(self, sample_ohlcv_data):
        """Test RSI calculation"""
        df = IndicatorsService.prepare_dataframe(sample_ohlcv_data)
        rsi = IndicatorsService.calculate_rsi(df, period=14)
        
        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(df)
        assert all(0 <= val <= 100 for val in rsi if val != 0)  # RSI should be between 0-100
    
    def test_calculate_macd(self, sample_ohlcv_data):
        """Test MACD calculation"""
        df = IndicatorsService.prepare_dataframe(sample_ohlcv_data)
        macd_data = IndicatorsService.calculate_macd(df, fast=12, slow=26, signal=9)
        
        assert isinstance(macd_data, dict)
        assert 'macd' in macd_data
        assert 'signal' in macd_data
        assert 'histogram' in macd_data
        
        for key, series in macd_data.items():
            assert isinstance(series, pd.Series)
            assert len(series) == len(df)
    
    def test_calculate_ema(self, sample_ohlcv_data):
        """Test EMA calculation"""
        df = IndicatorsService.prepare_dataframe(sample_ohlcv_data)
        ema = IndicatorsService.calculate_ema(df, period=20)
        
        assert isinstance(ema, pd.Series)
        assert len(ema) == len(df)
        assert all(val > 0 for val in ema)  # EMA should be positive for our test data
    
    def test_calculate_all_indicators(self, sample_ohlcv_data):
        """Test calculating all indicators together"""
        config = {
            'rsi': {'period': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9},
            'ema': {'period': 20}
        }
        
        result = IndicatorsService.calculate_all_indicators(sample_ohlcv_data, config)
        
        assert isinstance(result, dict)
        assert 'rsi' in result
        assert 'macd' in result
        assert 'ema' in result
        assert 'timestamps' in result
        
        # Check data integrity
        assert len(result['timestamps']) == 100
        assert len(result['rsi']) == 100
        assert len(result['ema']) == 100
        
        # Check MACD structure
        assert isinstance(result['macd'], dict)
        assert 'macd' in result['macd']
        assert 'signal' in result['macd']
        assert 'histogram' in result['macd']
    
    def test_get_indicator_value_at_timestamp(self, sample_ohlcv_data):
        """Test getting indicator values at specific timestamp"""
        config = {
            'rsi': {'period': 14},
            'ema': {'period': 20}
        }
        
        # Use the 50th timestamp
        target_timestamp = sample_ohlcv_data[50]['timestamp'].isoformat()
        
        snapshot = IndicatorsService.get_indicator_value_at_timestamp(
            sample_ohlcv_data, target_timestamp, config
        )
        
        assert isinstance(snapshot, dict)
        assert 'rsi' in snapshot
        assert 'ema' in snapshot
        
        # Values should be numbers
        assert isinstance(snapshot['rsi'], (int, float))
        assert isinstance(snapshot['ema'], (int, float))
    
    def test_empty_data_handling(self):
        """Test handling of empty data"""
        result = IndicatorsService.calculate_all_indicators([])
        assert result == {}
        
        snapshot = IndicatorsService.get_indicator_value_at_timestamp([], "2023-01-01T00:00:00")
        assert snapshot == {}
    
    def test_invalid_config_handling(self, sample_ohlcv_data):
        """Test handling of invalid configuration"""
        # Test with empty config
        result = IndicatorsService.calculate_all_indicators(sample_ohlcv_data, {})
        assert isinstance(result, dict)
        
        # Test with invalid parameters
        config = {
            'rsi': {'period': -1},  # Invalid period
            'ema': {'period': 0}    # Invalid period
        }
        
        result = IndicatorsService.calculate_all_indicators(sample_ohlcv_data, config)
        assert isinstance(result, dict)  # Should not crash
