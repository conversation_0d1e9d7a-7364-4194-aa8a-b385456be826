"""
Rate limiting utilities
"""
import time
import asyncio
from typing import Dict, Optional
from collections import defaultdict, deque
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, max_requests: int, time_window: int = 60):
        """
        Initialize rate limiter
        
        Args:
            max_requests: Maximum requests allowed in time window
            time_window: Time window in seconds (default: 60)
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: deque = deque()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """
        Acquire permission to make a request
        
        Returns:
            True if request is allowed, False if rate limited
        """
        async with self._lock:
            now = time.time()
            
            # Remove old requests outside time window
            while self.requests and self.requests[0] <= now - self.time_window:
                self.requests.popleft()
            
            # Check if we can make a request
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True
            
            return False
    
    async def wait_if_needed(self) -> None:
        """Wait if rate limited"""
        if not await self.acquire():
            # Calculate wait time
            if self.requests:
                wait_time = self.time_window - (time.time() - self.requests[0]) + 0.1
                if wait_time > 0:
                    logger.info(f"Rate limited, waiting {wait_time:.2f} seconds")
                    await asyncio.sleep(wait_time)
                    await self.acquire()  # Try again after waiting
    
    def get_remaining_requests(self) -> int:
        """Get number of remaining requests in current window"""
        now = time.time()
        # Remove old requests
        while self.requests and self.requests[0] <= now - self.time_window:
            self.requests.popleft()
        
        return max(0, self.max_requests - len(self.requests))
    
    def get_reset_time(self) -> Optional[float]:
        """Get time when rate limit resets"""
        if not self.requests:
            return None
        return self.requests[0] + self.time_window


class ExchangeRateLimiter:
    """Rate limiter for multiple exchanges"""
    
    def __init__(self):
        self.limiters: Dict[str, RateLimiter] = {}
    
    def get_limiter(self, exchange: str, max_requests: int, time_window: int = 60) -> RateLimiter:
        """Get or create rate limiter for exchange"""
        key = f"{exchange}_{max_requests}_{time_window}"
        if key not in self.limiters:
            self.limiters[key] = RateLimiter(max_requests, time_window)
        return self.limiters[key]
    
    async def acquire(self, exchange: str, max_requests: int, time_window: int = 60) -> bool:
        """Acquire permission for exchange request"""
        limiter = self.get_limiter(exchange, max_requests, time_window)
        return await limiter.acquire()
    
    async def wait_if_needed(self, exchange: str, max_requests: int, time_window: int = 60) -> None:
        """Wait if rate limited for exchange"""
        limiter = self.get_limiter(exchange, max_requests, time_window)
        await limiter.wait_if_needed()


# Global rate limiter instance
exchange_rate_limiter = ExchangeRateLimiter()
